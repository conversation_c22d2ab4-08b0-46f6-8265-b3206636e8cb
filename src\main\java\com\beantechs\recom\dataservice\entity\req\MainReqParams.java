package com.beantechs.recom.dataservice.entity.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 推荐请求参数
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "推荐请求参数")
public class MainReqParams {

    /**
     * 用户ID, 游客传设备id
     */
    @Schema(description = "用户ID", example = "U1034137801798758400")
    private String userId;

    /**
     * 频道id, 默空-首页推荐
     */
    @Schema(description = "频道id", example = "-1")
    private String channelId;

    /**
     * 推荐每批次数量, 默认：10
     */
    @Schema(description = "推荐每批次数量", example = "10")
    private Integer pageSize;

    /**
     * 频道标签
     */
    @Schema(description = "频道标签", example = "[\"坦克:7_坦克300:123\",\"魏:2_魏高山:211\"]")
    private List<String> contentTagList;

    /**
     * 是否需要打印明细
     */
    @Schema(description = "是否需要返回明细, 1-需要, 0-不需要, 默认-0", example = "0")
    private Integer showDetail = 0;

}
