package com.beantechs.recom.dataservice.service.handle.recall;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.beantechs.recom.dataservice.config.Constants;
import com.beantechs.recom.dataservice.entity.bo.ContentScoreVo;
import com.beantechs.recom.dataservice.entity.bo.RecommendContext;
import com.beantechs.recom.dataservice.service.CommonHandel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@MultiRecallType(type = "itemCF-video")
public class ItemCFVideoRecall implements RecallStrategy {
    @Override
    public List<ContentScoreVo> execute(RecommendContext context) {
        String userId = context.getMainReqParams().getUserId();
        String channelId = context.getMainReqParams().getChannelId();
        RedisTemplate<String, String> redisTemplate = SpringUtil.getBean("stringRedisTemplate");
        JSONObject weight = JSON.parseObject(context.getAbTestConfig().getWeight());

        List<ContentScoreVo> t0Behavior = t0Behavior(userId, channelId, redisTemplate);
        Map<String, List<ContentScoreVo>> t0Recall = t0Recall(t0Behavior, userId, channelId, redisTemplate);
        List<ContentScoreVo> t1Recall = t1Recall(userId, channelId, redisTemplate);

        return fusion(t0Behavior, t0Recall, t1Recall, weight);
    }


    private List<ContentScoreVo> t0Behavior(String userId, String channelId, RedisTemplate<String, String> redisTemplate) {
        String key = Constants.REDIS_KEY_RECOM_T0_BEHAVIOUR_PREFIX + LocalDateTime.now().minusHours(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH")) + ":" + channelId;
        List<ContentScoreVo> collect = List.of();
        try {
            String toBehaviour = (String) redisTemplate.opsForHash().get(key, userId);
            if (StringUtils.hasText(toBehaviour)) {
                collect = JSONArray.parseArray(toBehaviour)
                        .stream()
                        .map(JSON::toJSONString)
                        .map(JSONObject::parseObject)
                        .filter(o -> o.getString("post_type").equals("-1"))
                        .map(o -> o.getString("content_score"))
                        .map(CommonHandel::parseContentScoreVoRecallResult)
                        .flatMap(List::stream).toList();
            }
        } catch (Exception e) {
            String errorMsg = "itemCF用户T0行为查询 => 查询T0行为失败, userId：【%s】, channelId：【%s】".formatted(userId, channelId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }
        return collect;
    }


    private Map<String, List<ContentScoreVo>> t0Recall(List<ContentScoreVo> t0Behavior, String userId, String channelId, RedisTemplate<String, String> redisTemplate) {
        Set<String> contentIds = t0Behavior.stream().map(ContentScoreVo::getContentId).collect(Collectors.toSet());
        if (contentIds.isEmpty()) {
            return new HashMap<>();
        }
        String key = Constants.REDIS_KEY_RECOM_SIMILARIT_VIDEO_ITEMCF_PREFIX + channelId;

        List<Object> idList = contentIds.stream().map(o -> (Object) o).toList();
        Map<String, List<ContentScoreVo>> ContentSimilarCIDSMap = new HashMap<>();
        try {
            List<Object> value = redisTemplate.opsForHash().multiGet(key, idList);
            if (!CollectionUtils.isEmpty(value)) {
                for (int i = 0; i < idList.size(); i++) {
                    if (!Objects.isNull(value.get(i))) {
                        ContentSimilarCIDSMap.put(String.valueOf(idList.get(i)), CommonHandel.parseContentScoreVoRecallResult(String.valueOf(value.get(i))));
                    }
                }
            }
        } catch (Exception e) {
            String errorMsg = "itemCF视频实时召回 => ItemCF从Redis查询相似视频内容失败, userId：【%s】, channelId：【%s】".formatted(userId, channelId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }
        return ContentSimilarCIDSMap;
    }


    private List<ContentScoreVo> t1Recall(String userId, String channelId, RedisTemplate<String, String> redisTemplate) {
        String key = Constants.REDIS_KEY_RECOM_T1_VIDEO_ITEMCF_PREFIX + channelId + ":" + userId;

        String value;
        try {
            value = (String) redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            String errorMsg = "itemCF视频离线召回 => 查询物料离线召回视频内容redis失败, userId：【%s】, channelId：【%s】".formatted(userId, channelId);
            log.error(errorMsg, e);
            e.printStackTrace();
            return Collections.emptyList();
        }

        return CommonHandel.parseContentScoreVoRecallResult(value);
    }


    private List<ContentScoreVo> fusion(
            List<ContentScoreVo> t0Behavior,
            Map<String, List<ContentScoreVo>> t0Recall,
            List<ContentScoreVo> t1Recall,
            JSONObject weight
    ) {
        Map<String, Double> fusionMap = new HashMap<>();

        if (!CollectionUtils.isEmpty(t0Recall)) {
            t0Recall.forEach((itemId, contentList) -> {
                for (ContentScoreVo content : contentList) {
                    String contentId = content.getContentId();
                    double score = Double.parseDouble(content.getScore());
                    Optional<ContentScoreVo> first = t0Behavior.stream().filter(t -> contentId.equals(t.getContentId())).findFirst();
                    if (first.isPresent()) {
                        score = score * Double.parseDouble(first.get().getScore()) * weight.getDouble("t0_weight");
                    }
                    fusionMap.merge(contentId, score, Double::sum);
                }
            });
        }

        for (ContentScoreVo content : t1Recall) {
            fusionMap.merge(content.getContentId(), Double.parseDouble(content.getScore()), Double::sum);
        }

        return fusionMap.entrySet().stream()
                .map(entry -> {
                    ContentScoreVo vo = new ContentScoreVo();
                    vo.setContentId(entry.getKey());
                    vo.setScore(String.valueOf(entry.getValue()));
                    return vo;
                })
                .sorted((a, b) -> Double.compare(
                        Double.parseDouble(b.getScore()),
                        Double.parseDouble(a.getScore())))
                .collect(Collectors.toList());
    }
}
