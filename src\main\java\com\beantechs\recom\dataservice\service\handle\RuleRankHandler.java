package com.beantechs.recom.dataservice.service.handle;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beantechs.recom.dataservice.config.Constants;
import com.beantechs.recom.dataservice.config.ConstantsEnum;
import com.beantechs.recom.dataservice.config.RecommendContextHolder;
import com.beantechs.recom.dataservice.entity.bo.HotContentInfoBo;
import com.beantechs.recom.dataservice.entity.bo.RecommendContext;
import com.beantechs.recom.dataservice.entity.bo.ResortCommonBo;
import com.beantechs.recom.dataservice.entity.resp.RecommendRespContentInfo;
import com.beantechs.recom.dataservice.service.CommonHandel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 规则重排处理器
 */
@Slf4j
public class RuleRankHandler extends ModuleHandle {

    @Override
    public void handle() {
        RecommendContext context = RecommendContextHolder.getContext();
        String userId = context.getMainReqParams().getUserId();
        String channelId = context.getMainReqParams().getChannelId();
        Integer pageSize = context.getMainReqParams().getPageSize();
        List<String> graphicIds = context.getCTRSortGraphicList();
        List<String> videoIds = context.getCTRSortVideoList();
        List<String> contentTypeListBackUp = context.getContentTypeListBackUp();
        String weight = context.getAbTestConfig().getWeight();
        Set<String> filter = context.getFilterCollections().getTotalFilterCollections();


        RedisTemplate<String, String> redisTemplate = SpringUtil.getBean("stringRedisTemplate");
        String graphicKey = Constants.REDIS_KEY_RECOM_GRAPHIC_HOT_HASH_PREFIX + channelId;
        String videoKey = Constants.REDIS_KEY_RECOM_VIDEO_HOT_HASH_PREFIX + channelId;

        List<HotContentInfoBo> graphicDetailList = detailGet(graphicKey, graphicIds, redisTemplate);
        List<HotContentInfoBo> videoDetailList = detailGet(videoKey, videoIds, redisTemplate);

        // 图文配比
        List<String> contentTypeList;
        try {
            contentTypeList = contentTypeListCal(weight, pageSize);
        } catch (Exception e) {
            contentTypeList = contentTypeListBackUp;
        }

        // 标签打散
        List<ResortCommonBo> result = new ArrayList<>();
        try {
            result = scatter(userId, channelId, pageSize, contentTypeList, graphicDetailList, videoDetailList, filter, redisTemplate);
        } catch (Exception e) {
            result = scatterBackup(graphicDetailList, videoDetailList, contentTypeList, channelId, result);
        }

        List<HotContentInfoBo> newestGraphic = graphicDetailList.stream().filter(HotContentInfoBo::isNewest).toList();
        List<HotContentInfoBo> newestVideo = videoDetailList.stream().filter(HotContentInfoBo::isNewest).toList();

        try {
            fillRecently(channelId, result, newestGraphic, newestVideo);
        } catch (Exception e) {
            log.warn("插新失败, userId:【{}】, channelId：【{}】", userId, channelId);
        }

        context.setResp(result.stream().limit(pageSize).map(r -> RecommendRespContentInfo.build(r.getContentId(), r.getType())).toList());

        RecommendContextHolder.setContext(context);

        if (nextHandler != null) {
            nextHandler.handle();
        }
    }

    private static @NotNull List<ResortCommonBo> scatterBackup(List<HotContentInfoBo> graphicDetailList, List<HotContentInfoBo> videoDetailList, List<String> contentTypeList, String channelId, List<ResortCommonBo> result) {
        LinkedList<HotContentInfoBo> graphicLinkList = new LinkedList<>(graphicDetailList);
        LinkedList<HotContentInfoBo> videoLinkList = new LinkedList<>(videoDetailList);

        for (String type : contentTypeList) {
            if (type.equals("graphic")) {
                HotContentInfoBo hotContentInfoBo = graphicLinkList.pollFirst();
                if (channelId.equals("-1")) {
                    if (null == hotContentInfoBo) {
                        hotContentInfoBo = videoLinkList.pollFirst();
                        if (null == hotContentInfoBo) {
                            break;
                        } else {
                            result.add(ResortCommonBo.of(hotContentInfoBo.getContentId(), "video", hotContentInfoBo.getPublishTime(), hotContentInfoBo.getCarBrandNameNew()));
                        }
                    } else {
                        result.add(ResortCommonBo.of(hotContentInfoBo.getContentId(), "graphic", hotContentInfoBo.getPublishTime(), hotContentInfoBo.getCarBrandNameNew()));
                    }
                } else {
                    if (null == hotContentInfoBo) {
                        hotContentInfoBo = videoLinkList.pollFirst();
                        if (null == hotContentInfoBo) {
                            break;
                        } else {
                            result.add(ResortCommonBo.of(hotContentInfoBo.getContentId(), "video", hotContentInfoBo.getPublishTime(), hotContentInfoBo.getTopicKmeansLabel()));
                        }
                    } else {
                        result.add(ResortCommonBo.of(hotContentInfoBo.getContentId(), "graphic", hotContentInfoBo.getPublishTime(), hotContentInfoBo.getTopicKmeansLabel()));
                    }
                }
            } else {
                HotContentInfoBo hotContentInfoBo = videoLinkList.pollFirst();
                if (channelId.equals("-1")) {
                    if (null == hotContentInfoBo) {
                        hotContentInfoBo = graphicLinkList.pollFirst();
                        if (null == hotContentInfoBo) {
                            break;
                        } else {
                            result.add(ResortCommonBo.of(hotContentInfoBo.getContentId(), "graphic", hotContentInfoBo.getPublishTime(), hotContentInfoBo.getCarBrandNameNew()));
                        }
                    } else {
                        result.add(ResortCommonBo.of(hotContentInfoBo.getContentId(), "video", hotContentInfoBo.getPublishTime(), hotContentInfoBo.getCarBrandNameNew()));
                    }
                } else {
                    if (null == hotContentInfoBo) {
                        hotContentInfoBo = graphicLinkList.pollFirst();
                        if (null == hotContentInfoBo) {
                            break;
                        } else {
                            result.add(ResortCommonBo.of(hotContentInfoBo.getContentId(), "graphic", hotContentInfoBo.getPublishTime(), hotContentInfoBo.getTopicKmeansLabel()));
                        }
                    } else {
                        result.add(ResortCommonBo.of(hotContentInfoBo.getContentId(), "video", hotContentInfoBo.getPublishTime(), hotContentInfoBo.getTopicKmeansLabel()));
                    }
                }
            }
        }
        return result.stream().filter(Objects::nonNull).toList();
    }

    private static List<ResortCommonBo> scatter(
            String userId,
            String channelId,
            Integer pageSize,
            List<String> contentTypeList,
            List<HotContentInfoBo> graphicDetailList,
            List<HotContentInfoBo> videoDetailList,
            Set<String> filter,
            RedisTemplate<String, String> redisTemplate
    ) {
        List<ResortCommonBo> result = new ArrayList<>();
        LinkedList<String> checkSequentialWindow = new LinkedList<>();
        LinkedList<String> checkDifferentTagSizeWindow = new LinkedList<>();
        Set<String> readySet = new HashSet<>();

        int currentIndex = 0;

        int sideNum = 1;

        while (result.size() < pageSize) {
            if (sideNum >= 10) {
                log.warn("重排策略 => 到达查询次数限制");
                break;
            }
            sideNum++;
            String type = contentTypeList.get(currentIndex);
            ResortCommonBo content = null;
            String selectedLabel;
            // 当前位置是图文
            if (type.equals("graphic")) {
                for (HotContentInfoBo graphic : graphicDetailList) {
                    if (!readySet.contains(graphic.getContentId())) {
                        // 首页选车系, 频道选话题
                        selectedLabel = "-1".equals(channelId) ? graphic.getCarBrandNameNew() : graphic.getTopicKmeansLabel();
                        // 如果不会形成连续的标签就插入
                        if (!isModelConsecutive(checkSequentialWindow, selectedLabel)) {
                            content = ResortCommonBo.of(graphic.getContentId(), "graphic", graphic.getPublishTime(), selectedLabel);
                            // 更新6内容组的标签统计
                            checkDifferentTagSizeWindow.addLast(selectedLabel);
                            // 检查每6个内容是否满足至少3个不同标签的要求
                            if (checkDifferentTagSizeWindow.size() == 6) {
                                HashSet<String> set = new HashSet<>(checkDifferentTagSizeWindow);
                                if (set.size() < 3) {
                                    content = null;
                                    checkDifferentTagSizeWindow.removeLast();
                                } else {
                                    result.add(content);
                                    readySet.add(content.getContentId());
                                    checkDifferentTagSizeWindow.removeFirst();

                                    // 更新标签队列
                                    checkSequentialWindow.addLast(selectedLabel);
                                    if (checkSequentialWindow.size() > 3) {
                                        checkSequentialWindow.removeFirst();
                                    }
                                    currentIndex++;
                                    graphicDetailList.remove(graphic);
                                    break;
                                }
                            } else {
                                result.add(content);
                                readySet.add(content.getContentId());

                                // 更新标签队列
                                checkSequentialWindow.addLast(selectedLabel);
                                if (checkSequentialWindow.size() > 3) {
                                    checkSequentialWindow.removeFirst();
                                }
                                currentIndex++;
                                graphicDetailList.remove(graphic);
                                break;
                            }
                        }
                    }
                }
                boolean isQuery = true;
                int limitStart = 0;
                int limitEnd = 200;
                int queryNum = 1;
                while (isQuery) {
                    if (queryNum > 3) {
                        break;
                    }
                    if (null == content) {
                        String graphicHotKey = Constants.REDIS_KEY_RECOM_GRAPHIC_HOT_ZSET_PREFIX + channelId;
                        Set<String> rangeGraphic;
                        List<HotContentInfoBo> researchGraphicDetailList = new ArrayList<>();
                        try {
                            queryNum++;
                            rangeGraphic = redisTemplate.opsForZSet().range(graphicHotKey, limitStart, limitEnd);
                            if (!CollectionUtils.isEmpty(rangeGraphic)) {
                                researchGraphicDetailList = rangeGraphic.stream()
                                        .filter(Objects::nonNull)
                                        .map(o -> {
                                            JSONObject json = JSON.parseObject(o);
                                            if (filter.contains(json.getString("content_id"))) {
                                                return null;
                                            }
                                            if (!CommonHandel.isWithinOneYear(json.getString("publish_time"))) {
                                                return null;
                                            }
                                            return HotContentInfoBo.ofJSON(json);
                                        })
                                        .filter(Objects::nonNull)
                                        .toList();
                                if (CollectionUtils.isEmpty(researchGraphicDetailList)) {
                                    limitStart = limitEnd;
                                    limitEnd += 100;
                                }
                            }
                        } catch (Exception e) {
                            String errorMsg = "标签打散图文热度表补足失败, userId:【%s】, channelId：【%s】".formatted(userId, channelId);
                            log.error(errorMsg, e);
                e.printStackTrace();
                        }

                        if (!CollectionUtils.isEmpty(researchGraphicDetailList)) {
                            for (HotContentInfoBo researchGraphic : researchGraphicDetailList) {
                                if (!readySet.contains(researchGraphic.getContentId())) {
                                    // 首页选车系, 频道选话题
                                    selectedLabel = "-1".equals(channelId) ? researchGraphic.getCarBrandNameNew() : researchGraphic.getTopicKmeansLabel();

                                    // 如果不会形成连续的标签就插入
                                    if (!isModelConsecutive(checkSequentialWindow, selectedLabel)) {
                                        content = ResortCommonBo.of(researchGraphic.getContentId(), "graphic", researchGraphic.getPublishTime(), selectedLabel);

                                        // 更新6内容组的标签统计
                                        checkDifferentTagSizeWindow.addLast(selectedLabel);

                                        // 检查每6个内容是否满足至少3个不同标签的要求
                                        if (checkDifferentTagSizeWindow.size() == 6) {
                                            HashSet<String> set = new HashSet<>(checkDifferentTagSizeWindow);
                                            if (set.size() < 3) {
                                                content = null;
                                                checkDifferentTagSizeWindow.removeLast();
                                            } else {
                                                result.add(content);
                                                readySet.add(content.getContentId());
                                                checkDifferentTagSizeWindow.removeFirst();

                                                // 更新标签队列
                                                checkSequentialWindow.addLast(selectedLabel);
                                                if (checkSequentialWindow.size() > 3) {
                                                    checkSequentialWindow.removeFirst();
                                                }
                                                currentIndex++;
                                                isQuery = false;
                                                break;
                                            }
                                        } else {
                                            result.add(content);
                                            readySet.add(content.getContentId());

                                            // 更新标签队列
                                            checkSequentialWindow.addLast(selectedLabel);
                                            if (checkSequentialWindow.size() > 3) {
                                                checkSequentialWindow.removeFirst();
                                            }
                                            currentIndex++;
                                            isQuery = false;
                                            break;
                                        }
                                    }
                                }
                            }
                            limitStart = limitEnd;
                            limitEnd += 100;
                        }
                    } else {
                        isQuery = false;
                    }
                }
            } else {
                for (HotContentInfoBo video : videoDetailList) {
                    if (!readySet.contains(video.getContentId())) {
                        // 首页选车系, 频道选话题
                        selectedLabel = "-1".equals(channelId) ? video.getCarBrandNameNew() : video.getTopicKmeansLabel();

                        // 如果不会形成连续的标签就插入
                        if (!isModelConsecutive(checkSequentialWindow, selectedLabel)) {
                            content = ResortCommonBo.of(video.getContentId(), "video", video.getPublishTime(), selectedLabel);

                            // 更新6内容组的标签统计
                            checkDifferentTagSizeWindow.addLast(selectedLabel);
                            // 检查每6个内容是否满足至少3个不同标签的要求
                            if (checkDifferentTagSizeWindow.size() == 6) {
                                HashSet<String> set = new HashSet<>(checkDifferentTagSizeWindow);
                                if (set.size() < 3) {
                                    content = null;
                                    checkDifferentTagSizeWindow.removeLast();
                                } else {
                                    result.add(content);
                                    readySet.add(content.getContentId());
                                    checkDifferentTagSizeWindow.removeFirst();

                                    // 更新标签队列
                                    checkSequentialWindow.addLast(selectedLabel);
                                    if (checkSequentialWindow.size() > 3) {
                                        checkSequentialWindow.removeFirst();
                                    }
                                    currentIndex++;
                                    videoDetailList.remove(video);
                                    break;
                                }
                            } else {
                                result.add(content);
                                readySet.add(content.getContentId());

                                // 更新标签队列
                                checkSequentialWindow.addLast(selectedLabel);
                                if (checkSequentialWindow.size() > 3) {
                                    checkSequentialWindow.removeFirst();
                                }
                                currentIndex++;
                                videoDetailList.remove(video);
                                break;
                            }
                        }
                    }
                }
                if (null == content) {
                    contentTypeList.set(currentIndex, "graphic");
                }
            }
        }
        return result;
    }


    private static boolean isModelConsecutive(LinkedList<String> selectedLabels, String newLabel) {
        if (selectedLabels.size() < 2) return false;

        // 检查是否会形成连续3个相同标签
        List<String> lastLabels = new ArrayList<>(selectedLabels);
        return lastLabels.get(lastLabels.size() - 1).equals(newLabel)
               && lastLabels.get(lastLabels.size() - 2).equals(newLabel);
    }


    private static List<String> contentTypeListCal(String weight, Integer pageSize) {
        String proportion = JSONObject.parseObject(weight).getString(ConstantsEnum.ABGROUP_WEIGHT_FIELD_CONTENT_PROPORTION.getValue());
        String[] split = proportion.split(":");
        String graphicProportion = split[0];
        String videoProportion = split[1];

        // 根据比例计算内容类型分布
        return distributeTextAndImages(
                Integer.parseInt(graphicProportion),
                Integer.parseInt(videoProportion),
                pageSize
        );
    }


    public static List<String> distributeTextAndImages(int graphicProportion, int videoProportion, int pageSize) {
        // 总比例
        int totalRatio = graphicProportion + videoProportion;
        int graphicNum = (int) Math.round((double) graphicProportion / totalRatio * pageSize);
        int videoNum = pageSize - graphicNum;


        List<Integer> result = new ArrayList<>(pageSize);

        // 初始化列表为占位符
        for (int i = 0; i < pageSize; i++) {
            result.add(0); // 占位符
        }

        // 文本和图片的分布间隔
        double textInterval = (double) pageSize / graphicNum;
        double imageInterval = (double) pageSize / videoNum;

        // 均匀分布文本
        double currentTextPosition = 0;
        for (int i = 0; i < graphicNum; i++) {
            int index = (int) Math.round(currentTextPosition);
            while (index < pageSize && !(result.get(index) == 0)) {
                index++; // 找下一个空位
            }
            if (index < pageSize) {
                result.set(index, 1);
            }
            currentTextPosition += textInterval;
        }

        // 均匀分布图片
        double currentImagePosition = 0;
        for (int i = 0; i < videoNum; i++) {
            int index = (int) Math.round(currentImagePosition);
            while (index < pageSize && !(result.get(index) == 0)) {
                index++; // 找下一个空位
            }
            if (index < pageSize) {
                result.set(index, 2);
            }
            currentImagePosition += imageInterval;
        }

        return result.stream().map(i -> i.equals(1) ? "graphic" : "video").toList();
    }


    private static List<HotContentInfoBo> detailGet(
            String graphicKey,
            List<String> graphicIds,
            RedisTemplate<String, String> redisTemplate
    ) {
        List<HotContentInfoBo> detailList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(graphicIds)) {
            List<Object> values = redisTemplate.opsForHash().multiGet(graphicKey,
                    graphicIds.stream().map(o -> (Object) o).toList());
            if (!CollectionUtils.isEmpty(values)) {
                detailList = values.stream()
                        .filter(Objects::nonNull)
                        .map(String::valueOf)
                        .map(JSONObject::parseObject)
                        .filter(o -> CommonHandel.isWithinOneYear(o.getString("publish_time")))
                        .map(HotContentInfoBo::ofJSON)
                        .peek(o -> {
                            if (!StringUtils.hasText(o.getCarBrandNameNew())) {
                                o.setCarBrandNameNew("");
                            }
                            if (!StringUtils.hasText(o.getTopicKmeansLabel())) {
                                o.setTopicKmeansLabel("");
                            }
                        })
                        .toList();
            }
        }
        return detailList;
    }


    private static void fillRecently(
            String channelId,
            List<ResortCommonBo> result,
            List<HotContentInfoBo> newestGraphicDetails,
            List<HotContentInfoBo> newestVideoDetails
    ) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime threeDaysAgo = LocalDateTime.now().minusDays(3);
        int recentCount = (int) Math.round(result.size() * 0.1);
        long currentNewCount = result.stream().filter(o -> LocalDateTime.parse(o.getPublishTime(), formatter).isAfter(threeDaysAgo)).count();
        if (currentNewCount > recentCount) {
            return;
        }

        List<String> resultIdList = new ArrayList<>(result.stream().map(ResortCommonBo::getContentId).toList());
        newestGraphicDetails = newestGraphicDetails.stream().filter(o -> !resultIdList.contains(o.getContentId())).toList();
        newestVideoDetails = newestVideoDetails.stream().filter(o -> !resultIdList.contains(o.getContentId())).toList();


        if (newestGraphicDetails.size() + newestVideoDetails.size() == 0) {
            return;
        }

        // 计算近3天内容的比例
        for (int i = result.size() - 1; i >= 0; i--) {
            if (currentNewCount >= recentCount) {
                break;
            }
            ResortCommonBo recomContent = result.get(i);
            LocalDateTime publishTime = LocalDateTime.parse(recomContent.getPublishTime(), formatter);
            if (!publishTime.isAfter(threeDaysAgo)) {
                String type = recomContent.getType();
                if ("graphic".equals(type)) {
                    Optional<HotContentInfoBo> first = newestGraphicDetails.stream().filter(g -> {
                        // 结果中不包含
                        boolean bool1 = !resultIdList.contains(g.getContentId());
                        // 标签要是一样的
                        boolean bool2 = ("-1".equals(channelId) ? g.getCarBrandNameNew() : g.getTopicKmeansLabel()).equals(recomContent.getTag());
                        return bool1 && bool2;
                    }).findFirst();
                    if (first.isPresent()) {
                        HotContentInfoBo replaceContent = first.get();
                        String label = "-1".equals(channelId) ? replaceContent.getCarBrandNameNew() : replaceContent.getTopicKmeansLabel();
                        ResortCommonBo graphic = ResortCommonBo.of(replaceContent.getContentId(), "graphic", replaceContent.getPublishTime(), label);
                        result.add(i, graphic);
                        resultIdList.add(replaceContent.getContentId());
                        currentNewCount++;
                    }
                } else {
                    Optional<HotContentInfoBo> first = newestVideoDetails.stream().filter(g -> {
                        // 结果中不包含
                        boolean bool1 = !resultIdList.contains(g.getContentId());
                        // 标签要是一样的
                        boolean bool2 = ("-1".equals(channelId) ? g.getCarBrandNameNew() : g.getTopicKmeansLabel()).equals(recomContent.getTag());
                        return bool1 && bool2;
                    }).findFirst();
                    if (first.isPresent()) {
                        HotContentInfoBo replaceContent = first.get();
                        String label = "-1".equals(channelId) ? replaceContent.getCarBrandNameNew() : replaceContent.getTopicKmeansLabel();
                        ResortCommonBo video = ResortCommonBo.of(replaceContent.getContentId(), "video", replaceContent.getPublishTime(), label);
                        result.add(i, video);
                        resultIdList.add(replaceContent.getContentId());
                        currentNewCount++;
                    }
                }
            }
        }
    }
}
