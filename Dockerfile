# 使用OpenJDK 17作为基础镜像，alpine版本更轻量级
FROM openjdk:17-jdk-alpine

# 设置容器时区为上海时间
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

# 维护者信息
MAINTAINER luoyl<<EMAIL>>

# 设置工作目录
WORKDIR /app

# 定义JAR包文件名变量
ARG JAR_FILE=APP_NAME.jar

# 将编译好的JAR包复制到容器中
COPY ${JAR_FILE} .

# 设置环境变量
# 应用运行端口
ENV PORT 8080
# JAR包完整路径
ENV JAR "/app/${JAR_FILE}"
# JVM容器支持参数
ENV JAVA_OPTS "-XX:+UseContainerSupport"

# 暴露应用端口
EXPOSE $PORT

# 启动命令
# 1. 输出启动信息
# 2. 使用exec确保Java进程正确接收Unix信号
# 3. 配置JVM参数:
#    - 使用urandom提高系统随机数生成速度
#    - 启用G1垃圾收集器
#    - 设置最大堆内存为容器内存的75%
CMD echo "[Entrypoint] Beantechs Bigdata data service Docker Image" && \
    exec java -Djava.security.egd=file:/dev/./urandom \
    -XX:+UseG1GC \
    -XX:MaxRAMPercentage=75.0 \
    ${JAVA_OPTS} \
    -jar ${JAR} "$@" 