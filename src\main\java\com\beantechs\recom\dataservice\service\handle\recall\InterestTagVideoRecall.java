package com.beantechs.recom.dataservice.service.handle.recall;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beantechs.recom.dataservice.config.Constants;
import com.beantechs.recom.dataservice.entity.bo.ContentScoreVo;
import com.beantechs.recom.dataservice.entity.bo.RecommendContext;
import com.beantechs.recom.dataservice.entity.bo.T0UserLabel;
import com.beantechs.recom.dataservice.service.CommonHandel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@MultiRecallType(type = "interest-tag-video")
public class InterestTagVideoRecall implements RecallStrategy {
    @Override
    public List<ContentScoreVo> execute(RecommendContext context) {
        String userId = context.getMainReqParams().getUserId();
        String channelId = context.getMainReqParams().getChannelId();
        RedisTemplate<String, String> redisTemplate = SpringUtil.getBean("stringRedisTemplate");
        JdbcTemplate jdbcTemplate = SpringUtil.getBean(JdbcTemplate.class);

        List<ContentScoreVo> recall;
        String t0UserLabelStr = t0UserLabelGet(userId, redisTemplate);
        if (StringUtils.hasText(t0UserLabelStr)) {
            T0UserLabel t0UserLabel = JSON.parseObject(t0UserLabelStr, T0UserLabel.class);
            recall = t0Recall(userId, channelId, t0UserLabel, jdbcTemplate);
        } else {
            recall = t1Recall(userId, channelId, redisTemplate);
        }
        return recall;
    }

    private String t0UserLabelGet(String userId, RedisTemplate<String, String> redisTemplate) {
        String userTodaySelectLabel = null;
        String key = Constants.REDIS_KEY_RECOM_USER_SELECT_INTEREST_LABEL + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + ":" + userId;
        try {
            userTodaySelectLabel = redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            String errorMsg = "兴趣标签召回 => 获取用户今日选择标签失败, userId:【%s】".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }
        return userTodaySelectLabel;
    }


    private List<ContentScoreVo> t0Recall(String userId, String channelId, T0UserLabel t0UserLabel, JdbcTemplate jdbcTemplate) {
        Set<ContentScoreVo> priceRangeList = new HashSet<>();
        Set<ContentScoreVo> drivingModeList = new HashSet<>();
        Set<ContentScoreVo> vehicleSceneList = new HashSet<>();
        Set<ContentScoreVo> modelLevelList = new HashSet<>();
        Set<ContentScoreVo> allList = new HashSet<>();
        List<ContentScoreVo> recallList = new ArrayList<>();

        try {
            String sql = querySQL(
                    channelId,
                    t0UserLabel.getPriceRange(),
                    t0UserLabel.getDrivingMode(),
                    t0UserLabel.getVehicleScene(),
                    t0UserLabel.getModelLevel()
            );
            List<JSONObject> objs = jdbcTemplate.query(sql, (r, s) -> {
                        JSONObject json = new JSONObject();
                        json.put("param_value", r.getString("param_value"));
                        json.put("content_id", r.getString("content_id"));
                        json.put("hot_score", r.getString("hot_score"));
                        return json;
                    }
            ).stream().filter(Objects::nonNull).toList();


            objs.forEach(json -> {
                ContentScoreVo score = new ContentScoreVo();
                score.setContentId(json.getString("content_id"));
                score.setScore(json.getString("hot_score"));
                if (json.getString("param_value").equals(t0UserLabel.getPriceRange())) {
                    priceRangeList.add(score);
                }

                if (json.getString("param_value").equals(t0UserLabel.getDrivingMode())) {
                    drivingModeList.add(score);
                }

                if (json.getString("param_value").equals(t0UserLabel.getVehicleScene())) {
                    vehicleSceneList.add(score);
                }

                if (json.getString("param_value").equals(t0UserLabel.getModelLevel())) {
                    modelLevelList.add(score);
                }

                if (json.containsKey("all")) {
                    allList.add(score);
                }
            });


            int labelNum = 0;
            if (StringUtils.hasText(t0UserLabel.getPriceRange())) {
                labelNum++;
            }
            if (StringUtils.hasText(t0UserLabel.getDrivingMode())) {
                labelNum++;
            }
            if (StringUtils.hasText(t0UserLabel.getVehicleScene())) {
                labelNum++;
            }
            if (StringUtils.hasText(t0UserLabel.getModelLevel())) {
                labelNum++;
            }

            if (labelNum == 0) {
                recallList.addAll(allList);
            } else {
                int resultNum = 150 / labelNum;
                if (priceRangeList.size() > resultNum) {
                    recallList.addAll(priceRangeList.stream().limit(resultNum).toList());
                } else {
                    priceRangeList.addAll(allList.stream().filter(o -> !priceRangeList.contains(o)).limit(resultNum - priceRangeList.size()).toList());
                    recallList.addAll(priceRangeList);
                }
                if (drivingModeList.size() > resultNum) {
                    recallList.addAll(drivingModeList.stream().filter(o -> !recallList.contains(o)).limit(resultNum).toList());
                } else {
                    drivingModeList.addAll(allList.stream().filter(o -> !drivingModeList.contains(o) && !recallList.contains(o)).limit(resultNum - drivingModeList.size()).toList());
                    recallList.addAll(drivingModeList);
                }

                if (vehicleSceneList.size() > resultNum) {
                    recallList.addAll(vehicleSceneList.stream().filter(o -> !recallList.contains(o)).limit(resultNum).toList());
                } else {
                    vehicleSceneList.addAll(allList.stream().filter(o -> !vehicleSceneList.contains(o) && !recallList.contains(o)).limit(resultNum - vehicleSceneList.size()).toList());
                    recallList.addAll(vehicleSceneList);
                }

                if (modelLevelList.size() > resultNum) {
                    recallList.addAll(modelLevelList.stream().filter(o -> !recallList.contains(o)).limit(resultNum).toList());
                } else {
                    modelLevelList.addAll(allList.stream().filter(o -> !modelLevelList.contains(o) && !recallList.contains(o)).limit(resultNum - modelLevelList.size()).toList());
                    recallList.addAll(modelLevelList);
                }
            }
        } catch (Exception e) {
            String errorMsg = "兴趣标签根据用户选择兴趣标签视频召回 => 根据用户选择兴趣标签召回失败, userId:【%s】".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }

        return recallList.subList(0, Math.min(recallList.size(), 150));
    }


    private static String querySQL(String channelId, String priceRange, String drivingMode, String vehicleScene, String modelLevel) {
        return """
                (
                SELECT
                    post_price_range as param_value,
                    hot_score,
                    content_id
                FROM
                    dwd_gwm_app_video_hot_result_info_t0t1_df
                WHERE
                    post_price_range = '%s'
                AND channel_ids = '%s'
                AND hdfs_create_hr = (
                    SELECT MAX(hdfs_create_hr)
                    FROM dwd_gwm_app_video_hot_result_info_t0t1_df
                )
                ORDER BY
                    hot_score DESC
                LIMIT 150
                )
                UNION ALL
                (
                SELECT
                        post_drive_mode as param_value,
                        hot_score,
                        content_id
                FROM
                dwd_gwm_app_video_hot_result_info_t0t1_df
                WHERE
                post_drive_mode = '%s'
                AND channel_ids = '%s'
                AND hdfs_create_hr = (
                    SELECT MAX(hdfs_create_hr)
                    FROM dwd_gwm_app_video_hot_result_info_t0t1_df
                )
                ORDER BY
                hot_score DESC
                LIMIT 150
                )
                UNION ALL
                (
                SELECT
                        post_car_scene as param_value,
                        hot_score,
                        content_id
                FROM
                dwd_gwm_app_video_hot_result_info_t0t1_df
                WHERE
                post_car_scene = '%s'
                AND channel_ids = '%s'
                AND hdfs_create_hr = (
                    SELECT MAX(hdfs_create_hr)
                    FROM dwd_gwm_app_video_hot_result_info_t0t1_df
                )
                ORDER BY
                hot_score DESC
                LIMIT 150
                )
                UNION ALL
                (
                SELECT
                        post_car_level as param_value,
                        hot_score,
                        content_id
                FROM
                dwd_gwm_app_video_hot_result_info_t0t1_df
                WHERE
                post_car_level = '%s'
                AND channel_ids = '%s'
                AND hdfs_create_hr = (
                    SELECT MAX(hdfs_create_hr)
                    FROM dwd_gwm_app_video_hot_result_info_t0t1_df
                )
                ORDER BY
                hot_score DESC
                LIMIT 150
                )
                UNION ALL
                (
                SELECT
                        'all' as param_value,
                        hot_score,
                        content_id
                FROM
                dwd_gwm_app_video_hot_result_info_t0t1_df
                WHERE hdfs_create_hr = (
                    SELECT MAX(hdfs_create_hr)
                    FROM dwd_gwm_app_video_hot_result_info_t0t1_df
                )
                ORDER BY
                hot_score DESC
                LIMIT 150
                )
                """.formatted(priceRange, channelId, drivingMode, channelId, vehicleScene, channelId, modelLevel, channelId);
    }


    public List<ContentScoreVo> t1Recall(String userId, String channelId, RedisTemplate<String, String> redisTemplate) {
        String key = Constants.REDIS_KEY_RECOM_GRAPHIC_SELECTTAGS_PREFIX + channelId + ":" + userId;

        String value = null;
        try {
            value = (String) redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            String errorMsg = "兴趣标签T1视频召回 => 查询兴趣标签召回redis失败, userId：【%s】, channelId：【%s】".formatted(userId, channelId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }

        return CommonHandel.parseContentScoreVoRecallResult(value);
    }
}
