package com.beantechs.recom.dataservice.service;

import com.beantechs.recom.dataservice.entity.bo.ContentScoreVo;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class CommonHandel {

    public static List<ContentScoreVo> parseContentScoreVoRecallResult(String recallResult) {
        if (!StringUtils.hasText(recallResult)) {
            return Collections.emptyList();
        }

        return Arrays.stream(recallResult.split(","))
                .map(item -> {
                    String[] split = item.split(":");
                    ContentScoreVo contentScoreVo = new ContentScoreVo();
                    contentScoreVo.setContentId(split[0]);
                    contentScoreVo.setScore(split[1]);
                    return contentScoreVo;
                })
                .toList();
    }


    public static boolean isWithinOneYear(String dateStr) {
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 将时间字符串解析为 LocalDate 对象
        LocalDate inputDate;
        try {
            inputDate = LocalDate.parse(dateStr, formatter);
        } catch (Exception e) {
            return false;
        }
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 计算两个日期之间相差的年数
        long yearsDifference = ChronoUnit.YEARS.between(inputDate, currentDate);
        // 判断是否在一年以内
        return yearsDifference < 1;
    }
}
