package com.beantechs.recom.dataservice.service.handle;

import cn.hutool.extra.spring.SpringUtil;
import com.beantechs.recom.dataservice.config.AsyncHandle;
import com.beantechs.recom.dataservice.config.Constants;
import com.beantechs.recom.dataservice.config.RecommendContextHolder;
import com.beantechs.recom.dataservice.entity.bo.ContentScoreVo;
import com.beantechs.recom.dataservice.entity.bo.RecommendContext;
import com.beantechs.recom.dataservice.entity.resp.RecommendResp;
import com.beantechs.recom.dataservice.entity.resp.RecommendRespContentInfo;
import com.beantechs.recom.dataservice.service.handle.recall.RecallHandleResult;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Set;

/**
 * 输出处理器
 */
@Slf4j
public class ResponseHandler extends ModuleHandle {

    @Override
    public void handle() {
        RecommendContext context = RecommendContextHolder.getContext();
        String strategyWay = context.getStrategyWay();
        String userId = context.getMainReqParams().getUserId();
        String channelId = context.getMainReqParams().getChannelId();
        RecommendResp recommendResp = context.getRecommendResp();
        RecallHandleResult resultResult = context.getResultResult();
        Set<String> filterSet = context.getFilterCollections().getTotalFilterCollections();

        AsyncHandle asyncHandle = SpringUtil.getBean(AsyncHandle.class);

        List<RecommendRespContentInfo> result = switch (strategyWay) {
            case "label" ->
                    popularRespHandle(resultResult.getInterestTagGraphicResult(), resultResult.getInterestTagVideoResult(), filterSet);
            case "channel-tag" -> resultResult.getChannelTagResult();
            case "full" -> context.getResp();
            default ->
                    popularRespHandle(resultResult.getHotGraphicResult(), resultResult.getHotVideoResult(), filterSet);
        };

        try {
            List<String> recomIds = result.stream().map(RecommendRespContentInfo::getContentId).toList();
            String contentRecommendedSetKey = Constants.REDIS_KEY_RECOM_CONTENTS_ALREADY_RECOM + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + ":" + userId;
            asyncHandle.saveCacheRecommend(contentRecommendedSetKey, recomIds, userId);
        } catch (Exception e) {
            String errorMsg = "缓存已推内容至redis失败, userId:【%s】, channelId:【%s】".formatted(userId, channelId);
            log.error(errorMsg, e);
                e.printStackTrace();
        }

        recommendResp.setUserId(userId);
        recommendResp.setContents(result);

        context.setResp(result);
        context.setRecommendResp(recommendResp);
        RecommendContextHolder.setContext(context);


        if (nextHandler != null) {
            nextHandler.handle();
        }
    }

    private static List<RecommendRespContentInfo> popularRespHandle(
            List<ContentScoreVo> graphic,
            List<ContentScoreVo> video,
            Set<String> filterSet
    ) {
        List<ContentScoreVo> collect = new ArrayList<>(graphic);
        collect.addAll(video);

        return new ArrayList<>(collect.stream()
                .sorted(Comparator.comparing(ContentScoreVo::getScore).reversed())
                .filter(o -> !filterSet.contains(o.getContentId()))
                .map(o -> {
                    RecommendRespContentInfo resp = new RecommendRespContentInfo();
                    resp.setContentId(o.getContentId());
                    if (graphic.contains(o)) {
                        resp.setType("graphics");
                    } else {
                        resp.setType("videos");
                    }
                    return resp;
                })
                .limit(10)
                .toList());
    }
}
