package com.beantechs.recom.dataservice.service.handle.recall;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.beantechs.recom.dataservice.config.Constants;
import com.beantechs.recom.dataservice.entity.bo.ContentScoreVo;
import com.beantechs.recom.dataservice.entity.bo.RecommendContext;
import com.beantechs.recom.dataservice.entity.bo.UserScoreVo;
import com.beantechs.recom.dataservice.service.CommonHandel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@MultiRecallType(type = "userCF-video")
public class UserCFVideoRecall extends CommonRecall implements RecallStrategy {
    @Override
    public List<ContentScoreVo> execute(RecommendContext context) {
        String userId = context.getMainReqParams().getUserId();
        String channelId = context.getMainReqParams().getChannelId();
        RedisTemplate<String, String> redisTemplate = SpringUtil.getBean("stringRedisTemplate");
        JSONObject weight = JSON.parseObject(context.getAbTestConfig().getWeight());

        List<UserScoreVo> similarUsers = getSimilarUsers(userId, redisTemplate);
        Map<String, List<ContentScoreVo>> t0Recall = t0Recall(userId, channelId, similarUsers, redisTemplate);
        List<ContentScoreVo> t1Recall = t1Recall(userId, channelId, redisTemplate);

        return fusion(similarUsers, t0Recall, t1Recall, weight);
    }

    private Map<String, List<ContentScoreVo>> t0Recall(String userId, String channelId, List<UserScoreVo> similarUsers, RedisTemplate<String, String> redisTemplate) {
        String key = Constants.REDIS_KEY_RECOM_T0_BEHAVIOUR_PREFIX + LocalDateTime.now().minusHours(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH")) + ":" + channelId;
        Map<String, List<ContentScoreVo>> t0RecallMap = new HashMap<>();
        List<String> similarUIDs = similarUsers.stream().map(UserScoreVo::getUserId).toList();

        try {
            List<Object> similarUserBehaviourScore = redisTemplate.opsForHash().multiGet(key, similarUIDs.stream().map(o -> (Object) o).toList());
            if (!CollectionUtils.isEmpty(similarUserBehaviourScore)) {
                for (int i = 0; i < similarUIDs.size(); i++) {
                    Object similarUserContent = similarUserBehaviourScore.get(i);
                    if (!Objects.isNull(similarUserContent)) {
                        Optional<JSONObject> value = JSONArray.parseArray(String.valueOf(similarUserContent))
                                .stream()
                                .map(JSON::toJSONString)
                                .map(JSONObject::parseObject)
                                .filter(o -> o.getString("post_type").equals("2"))
                                .findFirst();
                        if (value.isPresent()) {
                            t0RecallMap.put(String.valueOf(similarUIDs.get(i)), CommonHandel.parseContentScoreVoRecallResult(value.get().getString("content_score")));
                        }
                    }
                }
            }
        } catch (Exception e) {
            String errorMsg = "用户视频实时召回 => userCF召回失败, userId：【%s】, channelId：【%s】".formatted(userId, channelId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }
        return t0RecallMap;
    }


    private List<ContentScoreVo> t1Recall(String userId, String channelId, RedisTemplate<String, String> redisTemplate) {
        String key = Constants.REDIS_KEY_RECOM_VIDEO_USERCF_PREFIX + channelId + ":" + userId;
        String value = null;
        try {
            value = (String) redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            String errorMsg = "用户视频离线召回 => 查询用户离线召回视频内容redis失败, userId：【%s】".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }
        return CommonHandel.parseContentScoreVoRecallResult(value);
    }


    private List<ContentScoreVo> fusion(
            List<UserScoreVo> similarUsers,
            Map<String, List<ContentScoreVo>> t0Recall,
            List<ContentScoreVo> t1Recall,
            JSONObject weight
    ) {
        Map<String, Double> contentScores = new HashMap<>();

        for (UserScoreVo similarUser : similarUsers) {
            processUserScores(similarUser, t0Recall, contentScores, weight);
        }

        for (ContentScoreVo content : t1Recall) {
            contentScores.merge(content.getContentId(), Double.parseDouble(content.getScore()), Double::sum);
        }

        return contentScores.entrySet().stream()
                .map(entry -> {
                    ContentScoreVo vo = new ContentScoreVo();
                    vo.setContentId(entry.getKey());
                    vo.setScore(String.valueOf(entry.getValue()));
                    return vo;
                })
                .sorted((a, b) -> Double.compare(
                        Double.parseDouble(b.getScore()),
                        Double.parseDouble(a.getScore())))
                .collect(Collectors.toList());
    }


    private void processUserScores(
            UserScoreVo similarUser,
            Map<String, List<ContentScoreVo>> t0Recall,
            Map<String, Double> contentScores,
            JSONObject weight
    ) {

        for (ContentScoreVo score : t0Recall.getOrDefault(similarUser.getUserId(), Collections.emptyList())) {
            String contentId = score.getContentId();
            double behaviorScore = Double.parseDouble(score.getScore());
            double weightedScore = behaviorScore * Double.parseDouble(similarUser.getScore()) * weight.getDouble("t0_weight");
            contentScores.merge(contentId, weightedScore, Double::sum);
        }
    }
}
