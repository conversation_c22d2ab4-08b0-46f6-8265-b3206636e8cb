package com.beantechs.recom.dataservice.service.recall;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.beantechs.recom.dataservice.config.Constants;
import com.beantechs.recom.dataservice.entity.bo.ContentScoreVo;
import com.beantechs.recom.dataservice.entity.bo.MultiRecallResultBo;
import com.beantechs.recom.dataservice.service.CommonHandel;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.Request;
import org.opensearch.client.Response;
import org.opensearch.client.RestClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 基于内容的召回处理器
 * 该类负责基于用户历史行为数据，通过内容相似度计算进行内容召回
 * 主要功能包括:
 * 1. 获取用户在不同时间窗口(T0/T1)的行为数据
 * 2. 基于用户交互过的内容，在OpenSearch中查找相似内容
 * 3. 对不同来源的内容进行加权打分，生成最终的召回结果
 */
@Slf4j
@RecallType(type = Constants.RECALL_WAY_CONTENT)
@Service
public class RecallContentHandle extends CommonHandel implements RecallHandle {

    private final RedisTemplate<String, String> redisTemplate;
    private final RestClient restClient;
    // 在类的开头添加常量
    private static final int BULK_SIZE = 10;

    /**
     * 构造函数
     *
     * @param restClient OpenSearch客户端
     */
    public RecallContentHandle(RedisTemplate<String, String> redisTemplate, RestClient restClient) {
        this.redisTemplate = redisTemplate;
        this.restClient = restClient;
    }

    /**
     * 处理内容召回的主要方法
     *
     * @param userId    用户ID
     * @param channelId 频道ID
     * @return 多路召回结果，包含图文和视频内容
     */
    @Override
    public MultiRecallResultBo handle(String userId, String channelId) {
        long l = System.currentTimeMillis();

        // T0时间窗口的图文和视频行为数据
        List<ContentScoreVo> t0Scores = getT0BehaviourScores(userId, channelId);

        String graphicT1Key = Constants.REDIS_KEY_RECOM_T1_RECALL_CONTENT_GRAPHIC_PREFIX + channelId + ":" + userId;
        String videoT1Key = Constants.REDIS_KEY_RECOM_T1_RECALL_CONTENT_VIDEO_PREFIX + channelId + ":" + userId;

        long l1 = System.currentTimeMillis();
        String graphicT1Ids = null;
        try {
            graphicT1Ids = (String) redisTemplate.opsForValue().get(graphicT1Key);
        } catch (Exception e) {
            String errorMsg = "内容召回 => userId:%s, 获取T1图文召回结果失败".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }
        long l2 = System.currentTimeMillis();
        log.warn("t1图文内容召回耗时：{}ms", l2 - l1);

        String videoT1Ids = null;
        try {
            videoT1Ids = (String) redisTemplate.opsForValue().get(videoT1Key);
        } catch (Exception e) {
            String errorMsg = "内容召回 => userId:%s, 获取T1视频召回结果失败".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }

        long l3 = System.currentTimeMillis();
        log.warn("t1视频内容召回耗时：{}ms", l3 - l2);

        List<ContentScoreVo> graphicT1ScoreVoList = parseContentScoreVoRecallResult(graphicT1Ids);
        List<ContentScoreVo> videoT1ScoreVoList = parseContentScoreVoRecallResult(videoT1Ids);

        // 获取内容ID集合
        List<String> t0Ids = t0Scores.stream().map(ContentScoreVo::getContentId).toList();

        // 获取相似内容
        Map<String, List<ContentScoreVo>> t0SimilarGraphics = new HashMap<>();
        Map<String, List<ContentScoreVo>> t0SimilarVideos = new HashMap<>();

        try {
            getSimilarContents(userId, t0Ids, channelId, t0SimilarGraphics, t0SimilarVideos);
        } catch (Exception e) {
            String errorMsg = "内容召回 => userId:%s, 获取es相似内容失败".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }

        long l4 = System.currentTimeMillis();
        log.warn("内容召回获取相似内容耗时：{}ms", l4 - l3);


        // 计算加权分数
        List<ContentScoreVo> weightedGraphics = calculateWeightedScores(
                t0Scores,
                graphicT1ScoreVoList,
                t0SimilarGraphics
        );
        List<ContentScoreVo> weightedVideos = calculateWeightedScores(
                t0Scores,
                videoT1ScoreVoList,
                t0SimilarVideos
        );

        // 封装结果
        MultiRecallResultBo result = new MultiRecallResultBo();
        result.setContentGraphics(weightedGraphics);
        result.setContentVideos(weightedVideos);

        log.warn("内容召回结果 => userId:【{}】, 图文:【{}】条, 视频:【{}】条, 总耗时:【{}】ms", userId, weightedGraphics.size(), weightedVideos.size(), System.currentTimeMillis() - l);
        return result;
    }

    /**
     * 获取T0时间窗口的用户行为数据
     * T0通常表示最近的时间窗口，例如最近1天
     *
     * @param userId 用户ID
     * @return 内容得分列表
     */
    private List<ContentScoreVo> getT0BehaviourScores(String userId, String channelId) {
        String dateString = LocalDateTime.now().minusHours(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH"));
        String t0Key = Constants.REDIS_KEY_RECOM_T0_BEHAVIOUR_PREFIX + dateString + ":" + channelId;
        String toBehaviour;
        List<ContentScoreVo> result = List.of();
        try {
            toBehaviour = (String) redisTemplate.opsForHash().get(t0Key, userId);
            if (StringUtils.hasText(toBehaviour)) {
                result = JSONArray.parseArray(toBehaviour)
                        .stream()
                        .map(JSON::toJSONString)
                        .map(JSONObject::parseObject)
                        .filter(o -> o.getString("post_type").equals("-1"))
                        .map(o -> o.getString("content_score"))
                        .map(CommonHandel::parseContentScoreVoRecallResult)
                        .flatMap(List::stream).toList();
            }
        } catch (Exception e) {
            String errorMsg = "内容召回 => Redis查询T0行为数据异常 === 用户ID:%s".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }
        return result;
    }

    /**
     * 计算内容的加权得分
     * 综合考虑T0、T1时间窗口的行为分数和内容相似度分数
     *
     * @param t0Scores T0时间窗口的得分
     * @param t1Scores T1时间窗口的得分
     * @return 加权后的内容得分列表
     */
    private List<ContentScoreVo> calculateWeightedScores(
            List<ContentScoreVo> t0Scores,
            List<ContentScoreVo> t1Scores,
            Map<String, List<ContentScoreVo>> t0SimScoresMap) {
        Map<String, Double> contentScores = new HashMap<>();

        if (!CollectionUtils.isEmpty(t0SimScoresMap)) {
            t0SimScoresMap.forEach((id, contentList) -> {
                for (ContentScoreVo contentScoreVo : contentList) {
                    String contentId = contentScoreVo.getContentId();
                    double score = Double.parseDouble(contentScoreVo.getScore());
                    Optional<ContentScoreVo> first = t0Scores.stream().filter(t -> contentId.equals(t.getContentId())).findFirst();
                    if (first.isPresent()) {
                        score = Double.parseDouble(first.get().getScore()) * score * 1.1;
                    }
                    contentScores.merge(contentId, score, Double::sum);
                }
            });
        }

        if (!CollectionUtils.isEmpty(t1Scores)) {
            for (ContentScoreVo contentScoreVo : t1Scores) {
                String contentId = contentScoreVo.getContentId();
                String score = contentScoreVo.getScore();
                contentScores.merge(contentId, Double.parseDouble(score), Double::sum);
            }
        }

        if (!CollectionUtils.isEmpty(contentScores)) {
            return contentScores.entrySet().stream()
                    .map(entry -> {
                        ContentScoreVo vo = new ContentScoreVo();
                        vo.setContentId(entry.getKey());
                        vo.setScore(String.valueOf(entry.getValue()));
                        return vo;
                    })
                    .sorted((a, b) -> Double.compare(
                            Double.parseDouble(b.getScore()),
                            Double.parseDouble(a.getScore())))
                    .toList();
        } else {
            return Collections.emptyList();
        }
    }

    /**
     * 在OpenSearch中查找相似内容
     *
     * @param contentIds 用户交互过的内容ID集合
     */
    public void getSimilarContents(
            String userId,
            List<String> contentIds,
            String channelId,
            Map<String, List<ContentScoreVo>> similarGraphics,
            Map<String, List<ContentScoreVo>> similarVideos
    ) throws IOException {
        if (contentIds.isEmpty()) {
            return;
        }

        long l1 = System.currentTimeMillis();
        Map<String, List<Double>> contentVectors = getContentVectors(userId, contentIds);
        if (contentVectors.isEmpty()) {
            return;
        }

        long l2 = System.currentTimeMillis();
        log.warn("获取内容向量耗时：{}ms", l2 - l1);

        StringBuilder requestBody = new StringBuilder();
        for (Map.Entry<String, List<Double>> entry : contentVectors.entrySet()) {
            Map<String, Object> searchGraphicRequest = buildSearchRequest(entry.getValue(), channelId, "1");
            requestBody.append("{\"index\":\"").append(Constants.OPENSEARCH_RECOM_CONTENT_INDEX).append("\"}\n");
            requestBody.append(new ObjectMapper().writeValueAsString(searchGraphicRequest)).append("\n");

            Map<String, Object> searchVideoRequest = buildSearchRequest(entry.getValue(), channelId, "2");
            requestBody.append("{\"index\":\"").append(Constants.OPENSEARCH_RECOM_CONTENT_INDEX).append("\"}\n");
            requestBody.append(new ObjectMapper().writeValueAsString(searchVideoRequest)).append("\n");
        }

        // 执行bulk请求
        Request request = new Request("POST", "/_msearch");
        request.setJsonEntity(requestBody.toString());

        Response response = restClient.performRequest(request);
        Iterator<String> contentIdIterator = contentVectors.keySet().iterator();

        long l3 = System.currentTimeMillis();
        log.warn("内容召回es查询耗时：{}ms", l3 - l2);

        // 解析响应
        JSONObject responseObj = new ObjectMapper().readValue(response.getEntity().getContent(), JSONObject.class);
        JSONArray responses = responseObj.getJSONArray("responses");
        for (int i = 0; i < (responses.size() / 2) && contentIdIterator.hasNext(); i += 2) {
            String contentId = contentIdIterator.next();
            JSONObject searchResponseGraphic = responses.getJSONObject(i);
            JSONObject searchResponseVideo = responses.getJSONObject(i + 1);
            JSONArray graphicHits;
            try {
                graphicHits = searchResponseGraphic.getJSONObject("hits").getJSONArray("hits");
                List<ContentScoreVo> graphicScoreVos = new ArrayList<>();
                graphicHits.forEach(item -> graphicScoreVos.add(ContentScoreVo.esObjConvertVo(JSONObject.parseObject(JSON.toJSONString(item)))));
                List<ContentScoreVo> graphicScoreVoList = similarGraphics.get(contentId);
                if (CollectionUtils.isEmpty(graphicScoreVoList)) {
                    similarGraphics.put(contentId, graphicScoreVos);
                } else {
                    graphicScoreVoList.addAll(graphicScoreVos);
                    similarGraphics.put(contentId, graphicScoreVoList);
                }
            } catch (Exception e) {
                String errorMsg = "内容召回 => userId:%s, 未根据向量匹配到es相似度图文内容!".formatted(userId);
                log.error(errorMsg, e);
                e.printStackTrace();
            }

            JSONArray videoHits;
            try {
                videoHits = searchResponseVideo.getJSONObject("hits").getJSONArray("hits");
                List<ContentScoreVo> videoScoreVos = new ArrayList<>();
                videoHits.forEach(item -> videoScoreVos.add(ContentScoreVo.esObjConvertVo(JSONObject.parseObject(JSON.toJSONString(item)))));
                List<ContentScoreVo> videoScoreVoList = similarVideos.get(contentId);
                if (CollectionUtils.isEmpty(videoScoreVoList)) {
                    similarVideos.put(contentId, videoScoreVos);
                } else {
                    videoScoreVoList.addAll(videoScoreVos);
                    similarVideos.put(contentId, videoScoreVoList);
                }
            } catch (Exception e) {
                String errorMsg = "内容召回 => userId:%s, 未根据向量匹配到es相似度视频内容!".formatted(userId);
                log.error(errorMsg, e);
                e.printStackTrace();
            }
        }
    }

    /**
     * 从OpenSearch获取内容向量
     *
     * @param contentIds 内容ID集合
     * @return 内容ID到向量的映射
     */
    private Map<String, List<Double>> getContentVectors(String userId, List<String> contentIds) {
        Map<String, List<Double>> collect = new HashMap<>();

        List<String> vectorKeyList = contentIds.stream().map(o -> Constants.REDIS_KEY_RECOM_CONTENT_VECTOR_PREFIX + o).toList();
        try {
            List<String> vectorValueList = redisTemplate.opsForValue().multiGet(vectorKeyList);
            if (!CollectionUtils.isEmpty(vectorValueList)) {
                for (int i = 0; i < contentIds.size(); i++) {
                    if (StringUtils.hasText(vectorValueList.get(i))) {
                        collect.put(contentIds.get(i), JSONArray.parseArray(vectorValueList.get(i), Double.class));
                    }
                }
            }
        } catch (Exception e) {
            String errorMsg = "内容召回 => userId:%s, 从redis获取内容向量失败!".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }
        return collect;
    }

    /**
     * 构建搜索请求，只返回_id和_score字段
     * <p>
     * 查询es：
     * ```java
     * {
     * "size": 100,
     * "track_scores": true,
     * "query": {
     * "bool": {
     * "should": [
     * {
     * "knn": {
     * "vector": {
     * "vector": "YOUR_VECTOR_HERE",
     * "k": 100
     * }
     * }
     * }
     * ],
     * "filter": {
     * "bool": {
     * "must": [
     * {
     * "match": {
     * "post_type": "YOUR_POST_TYPE_HERE"
     * }
     * },
     * {
     * "match": {
     * "channel_ids": "YOUR_CHANNEL_ID_HERE"
     * }
     * },
     * {
     * "term": {
     * "is_deleted": {
     * "value": 0
     * }
     * }
     * },
     * {
     * "term": {
     * "is_online": {
     * "value": 1
     * }
     * }
     * },
     * {
     * "term": {
     * "is_recommend": {
     * "value": 1
     * }
     * }
     * },
     * {
     * "term": {
     * "is_sink": {
     * "value": 0
     * }
     * }
     * },
     * {
     * "term": {
     * "is_usable": {
     * "value": 1
     * }
     * }
     * }
     * ]
     * }            * 			}
     * }    * 	},
     * "_source": "post_type"
     * }
     * ```
     *
     * @param vector    内容向量
     * @param channelId 频道ID
     * @return 搜索请求
     */
    private Map<String, Object> buildSearchRequest(List<Double> vector, String channelId, String postType) {
        Map<String, Object> searchRequest = new HashMap<>();

        // 构建KNN查询
        Map<String, Object> knnParams = new HashMap<>();
        Map<String, Object> vectorParams = new HashMap<>();
        vectorParams.put("vector", vector);
        vectorParams.put("k", BULK_SIZE);
        knnParams.put("vector", vectorParams);

        // 构建bool查询
        Map<String, Object> bool = new HashMap<>();
        bool.put("should", Collections.singletonList(Map.of("knn", knnParams)));

        // 构建filter条件
        List<Map<String, Object>> mustClauses = new ArrayList<>();
        mustClauses.add(Map.of("match", Map.of("post_type", postType)));
        mustClauses.add(Map.of("match", Map.of("channel_ids", channelId)));
        mustClauses.add(Map.of("term", Map.of("is_deleted", Map.of("value", 0))));
        mustClauses.add(Map.of("term", Map.of("is_online", Map.of("value", 1))));
        mustClauses.add(Map.of("term", Map.of("is_recommend", Map.of("value", 1))));
        mustClauses.add(Map.of("term", Map.of("is_sink", Map.of("value", 0))));
        mustClauses.add(Map.of("term", Map.of("is_usable", Map.of("value", 1))));

        Map<String, Object> filterBool = new HashMap<>();
        filterBool.put("must", mustClauses);
        bool.put("filter", Map.of("bool", filterBool));

        // 组装最终请求
        searchRequest.put("size", BULK_SIZE);
        searchRequest.put("track_scores", true);
        searchRequest.put("query", Map.of("bool", bool));

        searchRequest.put("_source", "post_type");

        return searchRequest;
    }
}
