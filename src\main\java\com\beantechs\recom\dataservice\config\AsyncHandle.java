package com.beantechs.recom.dataservice.config;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.beantechs.recom.dataservice.entity.bo.ContentRecomPoolListBo;
import com.beantechs.recom.dataservice.entity.bo.HotContentInfoBo;
import com.beantechs.recom.dataservice.entity.bo.MultiRecallResultBo;
import com.beantechs.recom.dataservice.enums.RecallWayEnum;
import com.beantechs.recom.dataservice.service.RecommendOldService;
import com.beantechs.recom.dataservice.service.recall.RecallHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@EnableAsync
@Component
@Slf4j
public class AsyncHandle {

    private final ThreadPoolTaskExecutor taskExecutor;
    private final RedisTemplate<String, String> redisTemplate;

    public AsyncHandle(@Qualifier("taskExecutor") ThreadPoolTaskExecutor taskExecutor,
                       RedisTemplate<String, String> redisTemplate) {
        this.taskExecutor = taskExecutor;
        this.redisTemplate = redisTemplate;
    }

    @Async
    public CompletableFuture<MultiRecallResultBo> asyncHotHandle(String userId, String channelId) {
        // 创建热门召回异步任务
        return CompletableFuture.supplyAsync(() -> {
            try {
                RecommendOldService bean = SpringUtil.getBean(RecommendOldService.class);
                RecallHandle hotRecallHandle = bean.getRecallHandle(RecallWayEnum.HOT.getCode());
                return hotRecallHandle.handle(userId, channelId);
            } catch (DIYException e) {
                return new MultiRecallResultBo();
            }
        }, taskExecutor);
    }

    @Async
    public CompletableFuture<MultiRecallResultBo> asyncContentHandle(String userId, String channelId) {
        // 创建热门召回异步任务
        return CompletableFuture.supplyAsync(() -> {
            try {
                RecommendOldService bean = SpringUtil.getBean(RecommendOldService.class);
                RecallHandle contentRecallHandle = bean.getRecallHandle(RecallWayEnum.CONTENT.getCode());
                return contentRecallHandle.handle(userId, channelId);
            } catch (DIYException e) {
                return new MultiRecallResultBo();
            }
        }, taskExecutor);
    }

    @Async
    public CompletableFuture<MultiRecallResultBo> asyncUserCFHandle(String userId, String channelId) {
        // 创建热门召回异步任务
        return CompletableFuture.supplyAsync(() -> {
            try {
                RecommendOldService bean = SpringUtil.getBean(RecommendOldService.class);
                RecallHandle userCFRecallHandle = bean.getRecallHandle(RecallWayEnum.USER_CF.getCode());
                return userCFRecallHandle.handle(userId, channelId);
            } catch (DIYException e) {
                return new MultiRecallResultBo();
            }
        }, taskExecutor);
    }

    @Async
    public CompletableFuture<MultiRecallResultBo> asyncItemCFHandle(String userId, String channelId) {
        // 创建热门召回异步任务
        return CompletableFuture.supplyAsync(() -> {
            try {
                RecommendOldService bean = SpringUtil.getBean(RecommendOldService.class);
                RecallHandle itemCFRecallHandle = bean.getRecallHandle(RecallWayEnum.ITEM_CF.getCode());
                return itemCFRecallHandle.handle(userId, channelId);
            } catch (DIYException e) {
                return new MultiRecallResultBo();
            }
        }, taskExecutor);
    }

    @Async
    public CompletableFuture<MultiRecallResultBo> asyncInterestHandle(String userId, String channelId) {
        // 创建热门召回异步任务
        return CompletableFuture.supplyAsync(() -> {
            try {
                RecommendOldService bean = SpringUtil.getBean(RecommendOldService.class);
                RecallHandle interestRecallHandle = bean.getRecallHandle(RecallWayEnum.USER_INTEREST.getCode());
                return interestRecallHandle.handle(userId, channelId);
            } catch (DIYException e) {
                return new MultiRecallResultBo();
            }
        }, taskExecutor);
    }

    @Async
    public CompletableFuture<MultiRecallResultBo> asyncUserFollowHandle(String userId, String channelId) {
        // 创建热门召回异步任务
        return CompletableFuture.supplyAsync(() -> {
            try {
                RecommendOldService bean = SpringUtil.getBean(RecommendOldService.class);
                RecallHandle followRecallHandle = bean.getRecallHandle(RecallWayEnum.USER_FOLLOW.getCode());
                return followRecallHandle.handle(userId, channelId);
            } catch (DIYException e) {
                return new MultiRecallResultBo();
            }
        }, taskExecutor);
    }

    @Async
    public void saveCacheCTR(MultiRecallResultBo multiRecallResultBo, String userId, String channelId) {
        // 创建热门召回异步任务
        List<HotContentInfoBo> newestGraphicDetails = multiRecallResultBo.getNewestGraphicDetails();
        List<HotContentInfoBo> newestVideoDetails = multiRecallResultBo.getNewestVideoDetails();
        List<HotContentInfoBo> ctrSortGraphicDetails = multiRecallResultBo.getCtrSortGraphicDetails();
        List<HotContentInfoBo> ctrSortVideoDetails = multiRecallResultBo.getCtrSortVideoDetails();
        ContentRecomPoolListBo contentRecomPoolListBo = new ContentRecomPoolListBo();
        contentRecomPoolListBo.setNewestGraphicDetails(newestGraphicDetails);
        contentRecomPoolListBo.setNewestVideoDetails(newestVideoDetails);
        contentRecomPoolListBo.setCtrSortGraphicDetails(ctrSortGraphicDetails);
        contentRecomPoolListBo.setCtrSortVideoDetails(ctrSortVideoDetails);

        String key = Constants.REDIS_KEY_RECOM_CONTENTS_BEFORE_RESORT + channelId + ":" + userId;
        try {
            redisTemplate.delete(key);
            redisTemplate.opsForValue().set(key, JSON.toJSONString(contentRecomPoolListBo), 1, TimeUnit.HOURS);
        } catch (Exception e) {
            log.error("缓存推荐池结果至redis失败, 错误信息:{}", e.getMessage(), e);
        }
        log.warn("缓存推荐内容 => userId:【{}】, 推荐池key:【{}】", userId, key);
    }

    @Async
    public void saveCacheRecommend(String contentRecommendedSetKey, List<String> recomResult, String userId) {
        if (!CollectionUtils.isEmpty(recomResult)) {
            // 检查缓存中数据量是否大于等于500条
            Long size = redisTemplate.opsForSet().size(contentRecommendedSetKey);
            if (size != null && size >= 500) {
                log.warn("缓存已推荐数量已达到500条，不再添加新数据 => userId:【{}】", userId);
                return;
            }

            redisTemplate.opsForSet().add(contentRecommendedSetKey, recomResult.toArray(new String[0]));
            redisTemplate.expire(contentRecommendedSetKey, 24, TimeUnit.HOURS);
            log.warn("缓存已推荐 => userId:【{}】, 已推荐key:【{}】", userId, contentRecommendedSetKey);
        }
    }


}
