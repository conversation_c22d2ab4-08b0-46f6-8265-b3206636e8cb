package com.beantechs.recom.dataservice.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.opensearch.client.RestClient;
import org.opensearch.client.RestClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;

/**
 * OpenSearch配置类
 * 用于配置和创建OpenSearch客户端连接
 * 支持以下功能：
 * 1. 多节点集群配置
 * 2. 用户名密码认证
 * 3. SSL/TLS安全连接
 */
@Configuration
@Slf4j
public class OpenSearchConfig {

    /**
     * OpenSearch集群节点地址，格式：host1:port1,host2:port2
     */
    @Value("${opensearch.hosts}")
    private String hosts;

    /**
     * OpenSearch认证用户名
     */
    @Value("${opensearch.username}")
    private String username;

    /**
     * OpenSearch认证密码
     */
    @Value("${opensearch.password}")
    private String password;

    @Bean
    public RestClient restClient() {
        // 解析hosts配置为HttpHost数组
        HttpHost[] httpHosts = parseHosts(hosts);

        // 构建客户端配置
        RestClientBuilder builder = RestClient.builder(httpHosts);

        CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY,
                new UsernamePasswordCredentials(username, password));

        builder.setHttpClientConfigCallback(httpClientBuilder -> {
            httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
            return httpClientBuilder;
        });

        // 创建并返回RestClient
        RestClient restClient = builder.build();
        log.warn("RestClient is build, Rest客户端已创建, 地址:{}", Arrays.toString(httpHosts));
        return restClient;
    }


    /**
     * 解析hosts字符串为HttpHost数组
     * 格式：host1:port1,host2:port2
     *
     * @param hosts 主机配置字符串
     * @return HttpHost数组
     */
    private static HttpHost[] parseHosts(String hosts) {
        return Arrays.stream(hosts.split(","))
                .map(host -> {
                    String[] parts = host.split(":");
                    return new HttpHost(parts[0], Integer.parseInt(parts[1]), "http");
                })
                .toArray(HttpHost[]::new);
    }


}
