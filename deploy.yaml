apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: default
  name: APP_NAME
  labels:
    app: APP_NAME
spec:
  replicas: 1
  selector:
    matchLabels:
      app: APP_NAME
  template:
    metadata:
      labels:
        app: APP_NAME
    spec:
      initContainers:
        - name: init-agent
          image: ailab-cn-beijing.cr.volces.com/default/skywalking-java-agent:8.13.0-alpine
          imagePullPolicy: Always
          command: [ "/app/init-agent" ]
          args: [ "" ]
          volumeMounts:
            - name: sw-agent
              mountPath: /opt/skywalking
      volumes:
        - name: sw-agent
          emptyDir: { }
      containers:
        - name: APP_NAME
          image: ailab-cn-beijing.cr.volces.com/default/IMAGE_NAME:IMG_VERSION
          imagePullPolicy: Always
          ports:
            - containerPort: 8080
          volumeMounts:
            - name: sw-agent
              mountPath: /opt/skywalking
          #容器运行前需设置的环境变量列表
          env:
            - name: JAVA_TOOL_OPTIONS
              value: -javaagent:/opt/skywalking/agent/skywalking-agent.jar
            - name: SW_AGENT_NAME
              value: APP_NAME
            - name: SW_AGENT_COLLECTOR_BACKEND_SERVICES
              value: skywalking-svc.default:11800
            - name: SPRING_PROFILES_ACTIVE
              value: APP-PROFILE
            - name: volc_logs_APP_NAME
              value: stdout
          # 存活检查：kubernetes认为该pod是存活的,不存活则需要重启
          livenessProbe:
            tcpSocket:
              port: 8080
            # equals to the maximum startup time of the application + couple of seconds
            initialDelaySeconds: 30
            timeoutSeconds: 5
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 5
          # 就绪检查：kubernetes认为该pod是启动成功的
          readinessProbe:
            tcpSocket:
              port: 8080
            # equals to minimum startup time of the application
            initialDelaySeconds: 15
            timeoutSeconds: 5
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 5
          # 资源限制 keep request = limit to keep this container in guaranteed class
          resources:
            requests:
              cpu: 1000m
              memory: 2G
            limits:
              cpu: POD_LIMIT_CPU
              memory: POD_LIMIT_MEMORY
---
apiVersion: autoscaling/v1
kind: HorizontalPodAutoscaler
metadata:
  namespace: default
  name: APP_NAME
  labels:
    app: APP_NAME
spec:
  maxReplicas: POD_HA_MAX_NUM
  minReplicas: POD_HA_MIN_NUM
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: APP_NAME
  targetCPUUtilizationPercentage: 80
