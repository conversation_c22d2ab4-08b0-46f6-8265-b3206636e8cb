package com.beantechs.recom.dataservice.config;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class CommonUtils {

    public static String stringToMD5(String input) {
        try {
            // 获取 MD5 算法的 MessageDigest 实例
            MessageDigest md = MessageDigest.getInstance("MD5");
            // 将输入字符串转换为字节数组
            byte[] messageDigest = md.digest(input.getBytes());
            // 创建 StringBuilder 用于构建十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                // 将字节转换为十六进制字符串
                String hex = Integer.toHexString(0xFF & b);
                if (hex.length() == 1) {
                    // 如果十六进制字符串长度为 1，则在前面补 0
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            // 返回十六进制字符串
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            // 若指定的算法（MD5）不可用，抛出运行时异常
            throw new RuntimeException(e);
        }
    }


    @SafeVarargs
    public static <T> Set<T> mergeMultipleSets(Set<T>... sets) {
        Set<T> mergedSet = new HashSet<>();
        for (Set<T> set : sets) {
            if (set != null) {
                mergedSet.addAll(set);
            }
        }
        return mergedSet;
    }


    public static <T> List<T> mergeMultipleLists(List<T>... lists) {
        List<T> mergedList = new ArrayList<>();
        for (List<T> list : lists) {
            if (list != null) {
                mergedList.addAll(list);
            }
        }
        return mergedList;
    }


    /**
     * 计算两个向量的余弦相似度
     */
    public static double calculateCosineSimilarity(double[] vectorA, double[] vectorB) {
        double dotProduct = 0.0;
        double normA = 0.0;
        double normB = 0.0;
        for (int i = 0; i < vectorA.length; i++) {
            dotProduct += vectorA[i] * vectorB[i];
            normA += Math.pow(vectorA[i], 2);
            normB += Math.pow(vectorB[i], 2);
        }
        return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
    }


}
