package com.beantechs.recom.dataservice.service.handle.recall;

import com.beantechs.recom.dataservice.entity.bo.ContentScoreVo;
import com.beantechs.recom.dataservice.entity.bo.RecommendContext;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@MultiRecallType(type = "DSSM-video")
public class DSSMVideoRecall implements RecallStrategy{
    @Override
    public List<ContentScoreVo> execute(RecommendContext context) {
        return null;
    }
}
