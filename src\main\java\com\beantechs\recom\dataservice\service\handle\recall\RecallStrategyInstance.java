package com.beantechs.recom.dataservice.service.handle.recall;


import com.beantechs.recom.dataservice.config.RecommendContextHolder;
import com.beantechs.recom.dataservice.entity.bo.RecommendContext;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class RecallStrategyInstance {

    /**
     * 用户
     */
    private RecallStrategy userCFGraphicRecall;
    private RecallStrategy userCFVideoRecall;

    /**
     * 物料
     */
    private RecallStrategy itemCFGraphicRecall;
    private RecallStrategy itemCFVideoRecall;

    /**
     * 内容
     */
    private RecallStrategy contentGraphicRecall;
    private RecallStrategy contentVideoRecall;

    /**
     * 兴趣标签
     */
    private RecallStrategy interestTagGraphicRecall;
    private RecallStrategy interestTagVideoRecall;

    /**
     * 用户关注
     */
    private RecallStrategy userFollowGraphicRecall;
    private RecallStrategy userFollowVideoRecall;

    /**
     * 热门
     */
    private RecallStrategy hotGraphicRecall;
    private RecallStrategy hotVideoRecall;

    /**
     * 双塔
     */
    private RecallStrategy DSSMGraphicRecall;
    private RecallStrategy DSSMVideoRecall;

    /**
     * 用户理解
     */
    private RecallStrategy userUnderstandGraphicRecall;
    private RecallStrategy userUnderstandVideoRecall;

    /**
     * 频道标签召回
     */
    private TagRecallStrategy channelTagRecall;

    public static void init() {
        RecommendContext context = RecommendContextHolder.getContext();
        String strategyWay = context.getStrategyWay();
        String recallConf = context.getAbTestConfig().getRecall();

        List<String> typeList = Arrays.asList(recallConf.split(","));
        RecallStrategyInstance.RecallStrategyInstanceBuilder builder = RecallStrategyInstance.builder();
        switch (strategyWay) {
            case "label":
                builder.interestTagGraphicRecall(MultiRecallFactory.getRecallStrategy("interest-tag-graphic"))
                        .interestTagVideoRecall(MultiRecallFactory.getRecallStrategy("interest-tag-video"));
                break;
            case "channel-tag":
                builder.channelTagRecall(MultiRecallFactory.getTagRecallStrategy("channel-tag"));
                break;
            case "full":
                if (typeList.contains("usercf_recall")) {
                    builder.userCFGraphicRecall(MultiRecallFactory.getRecallStrategy("userCF-graphic"))
                            .userCFVideoRecall(MultiRecallFactory.getRecallStrategy("userCF-video"));
                }
                if (typeList.contains("itemcf_recall")) {
                    builder.itemCFGraphicRecall(MultiRecallFactory.getRecallStrategy("itemCF-graphic"))
                            .itemCFVideoRecall(MultiRecallFactory.getRecallStrategy("itemCF-video"));
                }
                if (typeList.contains("content_recall")) {
                    builder.contentGraphicRecall(MultiRecallFactory.getRecallStrategy("content-graphic"))
                            .contentVideoRecall(MultiRecallFactory.getRecallStrategy("content-video"));
                }
                if (typeList.contains("user_interest_recall")) {
                    builder.interestTagGraphicRecall(MultiRecallFactory.getRecallStrategy("interest-tag-graphic"))
                            .interestTagVideoRecall(MultiRecallFactory.getRecallStrategy("interest-tag-video"));
                }
                if (typeList.contains("follow_users_recall")) {
                    builder.userFollowVideoRecall(MultiRecallFactory.getRecallStrategy("userfollow-video"))
                            .userFollowGraphicRecall(MultiRecallFactory.getRecallStrategy("userfollow-graphic"));
                }
                if (typeList.contains("hot_recall")) {
                    builder.hotGraphicRecall(MultiRecallFactory.getRecallStrategy("hot-graphic"))
                            .hotVideoRecall(MultiRecallFactory.getRecallStrategy("hot-video"));
                }
                if (typeList.contains("user_understanding_recall")) {
                    builder.userUnderstandGraphicRecall(MultiRecallFactory.getRecallStrategy("user-understand-graphic"))
                            .userUnderstandVideoRecall(MultiRecallFactory.getRecallStrategy("user-understand-video"));
                }
                // TODO 尚未开发
//        if (typeList.contains("dssm_recall")) {
//            builder.DSSMGraphicRecall(MultiRecallFactory.getRecallStrategy("DSSM-graphic"))
//                   .DSSMVideoRecall(MultiRecallFactory.getRecallStrategy("DSSM-video"));
//        }
                break;
            default:
                builder.hotGraphicRecall(MultiRecallFactory.getRecallStrategy("hot-graphic"))
                        .hotVideoRecall(MultiRecallFactory.getRecallStrategy("hot-video"));
                break;
        }
        context.setRecallStrategy(builder.build());
        RecommendContextHolder.setContext(context);
    }

}
