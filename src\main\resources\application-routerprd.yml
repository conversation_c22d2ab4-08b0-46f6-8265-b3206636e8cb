env: routerprd
mysql:
  url: *******************************************************************************************************************************************************************************************************************
  username: dataservice
  password: 71IYOGNC1PzZm8bLNtoO
spring:
  application:
    name: app-merge-recommend-router-service
  data:
    redis:
      host: redis-cnlf5iphhjroriiiz.redis.ivolces.com
      port: 6379
      database: 0
      password: nGjVCSCD!jBufhwkq6eg
      connect-timeout: 10s
      timeout: 5s
      lettuce:
        pool:
          min-idle: 10
          max-idle: 2000
          max-active: 2000
          max-wait: -1ms
  cloud:
    nacos:
      discovery:
        server-addr: nacos-headless:8848
opensearch:
  hosts: opensearch-o-00c0c8rs5m0c.escloud.ivolces.com:9200
  username: admin
  password: FcRQkSKSK!Y!y7&$LnZG
server:
  servlet:
    context-path: /app-recommend