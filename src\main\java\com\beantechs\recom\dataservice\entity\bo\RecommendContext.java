package com.beantechs.recom.dataservice.entity.bo;

import com.beantechs.recom.dataservice.config.RecommendContextHolder;
import com.beantechs.recom.dataservice.entity.req.ABTestConfig;
import com.beantechs.recom.dataservice.entity.req.MainReqParams;
import com.beantechs.recom.dataservice.entity.req.RecommendReq;
import com.beantechs.recom.dataservice.entity.resp.RecommendResp;
import com.beantechs.recom.dataservice.entity.resp.RecommendRespContentInfo;
import com.beantechs.recom.dataservice.service.handle.ModuleHandle;
import com.beantechs.recom.dataservice.service.handle.recall.RecallHandleResult;
import com.beantechs.recom.dataservice.service.handle.recall.RecallStrategyInstance;
import com.beantechs.recom.dataservice.service.strategy.RecommendStrategy;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Set;
import java.util.UUID;


/**
 * 推荐中间对象
 */
@Slf4j
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RecommendContext {

    /**
     * 唯一识别码
     */
    private String uniqueId;

    /**
     * 推荐策略对象
     */
    private ABTestConfig abTestConfig;

    /**
     * 推荐接口参数
     */
    private MainReqParams mainReqParams;

    /**
     * 推荐响应结果对象
     */
    private RecommendResp recommendResp;

    /**
     * 过滤内容对象
     */
    private FilterCollections filterCollections;

    /**
     * 是否有兴趣标签
     */
    private boolean hasInterestTag;

    /**
     * 频道配置信息
     */
    private String channelConfig;

    /**
     * 具体推荐策略路线: popular-热门, label-兴趣标签, channel-tag-频道标签, full-热启动,
     */
    private String strategyWay;

    /**
     * 推荐策略流程信息
     */
    private RecommendStrategy recommendStrategy;

    /**
     * 推荐策略模块信息
     */
    private ModuleHandle recommendStrategyModuleFirstHandle;

    /**
     * 召回策略
     */
    private RecallStrategyInstance recallStrategy;

    /**
     * 召回结果
     */
    private RecallHandleResult resultResult;

    /**
     * 图文召回结果融合ID列表
     */
    private List<String> fusionGraphicIds;

    /**
     * 视频召回结果融合ID列表
     */
    private List<String> fusionVideoIds;

    /**
     * CTR排序后的图文列表
     */
    private List<String> CTRSortGraphicList;

    /**
     * CTR排序后的视频列表
     */
    private List<String> CTRSortVideoList;

    /**
     * CTR排序后的视频列表
     */
    private List<String> contentTypeListBackUp;

    /**
     * 推荐输出结果
     */
    private List<RecommendRespContentInfo> resp;

    /**
     * 是否有错误项
     */
    private Boolean hasErrorEnum = Boolean.FALSE;


    public RecommendContext(MainReqParams mainReqParams, ABTestConfig abTestConfig, RecommendResp recommendResp) {
        this.mainReqParams = mainReqParams;
        this.abTestConfig = abTestConfig;
        this.recommendResp = recommendResp;
        this.uniqueId = UUID.randomUUID().toString().replace("-", "");
    }


    /**
     * 初始化全局对象
     */
    public static void init(RecommendReq requestBody) {
        RecommendContextHolder.setContext(new RecommendContext(requestBody.getMainReqParams(), requestBody.getAbTestConfig(), RecommendResp.ofDefault(requestBody.getMainReqParams().getUserId())));

    }


    public static void mergeFilterCollections(
            Set<String> t1GraphicFilterCollections,
            Set<String> t1VideoFilterCollections,
            Set<String> offlineCollections,
            Set<String> dislikeCollections,
            Set<String> exposureCollections,
            Set<String> userBlockCollections,
            Set<String> alreadyRecommendCollections
    ) {
        FilterCollections filterCollections = new FilterCollections(
                t1GraphicFilterCollections,
                t1VideoFilterCollections,
                offlineCollections,
                dislikeCollections,
                exposureCollections,
                userBlockCollections,
                alreadyRecommendCollections
        );
        RecommendContext context = RecommendContextHolder.getContext();
        context.setFilterCollections(filterCollections);
        RecommendContextHolder.setContext(context);
    }

}
