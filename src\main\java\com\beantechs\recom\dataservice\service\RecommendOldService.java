package com.beantechs.recom.dataservice.service;

import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.beantechs.recom.dataservice.config.AsyncHandle;
import com.beantechs.recom.dataservice.config.Constants;
import com.beantechs.recom.dataservice.config.DIYException;
import com.beantechs.recom.dataservice.entity.bo.*;
import com.beantechs.recom.dataservice.entity.req.MainReqParams;
import com.beantechs.recom.dataservice.entity.resp.RecommendResp;
import com.beantechs.recom.dataservice.entity.resp.RecommendRespContentInfo;
import com.beantechs.recom.dataservice.enums.RecallWayEnum;
import com.beantechs.recom.dataservice.service.recall.RecallHandle;
import com.beantechs.recom.dataservice.service.recall.RecallType;
import com.beantechs.recom.dataservice.service.recall.RecallTypeHandleImpl;
import com.beantechs.recom.dataservice.service.recall.RecallUserInterestHandle;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.HttpEntity;
import org.jetbrains.annotations.NotNull;
import org.opensearch.client.Request;
import org.opensearch.client.Response;
import org.opensearch.client.RestClient;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 推荐服务核心实现类
 * 负责处理推荐请求，根据不同场景选择合适的推荐策略
 * 支持多路召回、用户行为分析、兴趣标签分析等功能
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Slf4j
@Service
@EnableAsync
public class RecommendOldService {
    // 用于redis缓存操作的redisTemplate
    private final RedisTemplate<String, String> redisTemplate;
    // 用于多路召回的线程池
    private final AsyncHandle asyncHandle;
    private final RestClient restClient;
    private final JdbcTemplate jdbcTemplate;

    // 构造函数注入依赖
    public RecommendOldService(
            RedisTemplate<String, String> redisTemplate,
            AsyncHandle asyncHandle, RestClient restClient, JdbcTemplate jdbcTemplate) {
        this.redisTemplate = redisTemplate;
        this.asyncHandle = asyncHandle;
        this.restClient = restClient;
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * 根据召回方式获取对应的召回处理器
     *
     * @param recallWay 召回方式
     * @return 召回处理器
     * @throws DIYException 当召回方式不支持时抛出异常
     */
    public RecallHandle getRecallHandle(String recallWay) throws DIYException {
        // 将字符串转换为枚举类型
        RecallWayEnum recallWayEnum = RecallWayEnum.getByCode(recallWay);
        if (recallWayEnum == null) {
            throw new DIYException("不支持的召回方式: " + recallWay);
        }

        // 根据召回方式获取对应的处理器
        return switch (recallWayEnum) {
            case USER_CF -> getRecallHandleByType(Constants.RECALL_WAY_USER_CF);
            case ITEM_CF -> getRecallHandleByType(Constants.RECALL_WAY_ITEM_CF);
            case CONTENT -> getRecallHandleByType(Constants.RECALL_WAY_CONTENT);
            case USER_INTEREST -> getRecallHandleByType(Constants.RECALL_WAY_USER_INTEREST);
            case USER_FOLLOW -> getRecallHandleByType(Constants.RECALL_WAY_USER_FOLLOW);
            case HOT -> getRecallHandleByType(Constants.RECALL_WAY_HOT);
        };
    }

    /**
     * 根据召回类型获取对应的处理器实例
     *
     * @param recallWay 召回方式
     * @return 召回处理器
     * @throws DIYException 当找不到匹配的处理器时抛出异常
     */
    private RecallHandle getRecallHandleByType(String recallWay) throws DIYException {
        // 创建召回类型处理器
        RecallType recallType = new RecallTypeHandleImpl(recallWay);
        TypeMatchService typeMatchService = SpringUtil.getBean(TypeMatchService.class);
        RecallHandle recallHandle = typeMatchService.getRecallTypeHandleMapInstance().get(recallType);
        if (null == recallHandle) {
            throw new DIYException("未找到匹配的搜索类型, 请确认[品牌标识]与[搜索类型]正确无误!");
        }
        return recallHandle;
    }

    /**
     * 处理推荐请求
     *
     * @param mainReqParams 推荐请求参数
     * @return 推荐响应结果
     * @throws DIYException 处理异常时抛出
     */
    public RecommendResp handle(MainReqParams mainReqParams) throws Exception {
        String userId = mainReqParams.getUserId();
        String channelId = mainReqParams.getChannelId();
        Integer pageSize = mainReqParams.getPageSize();
        List<String> tagList = mainReqParams.getContentTagList();

        long l1 = System.currentTimeMillis();

        // 创建响应对象
        RecommendResp recommendResp = new RecommendResp();
        // 计算好的推荐池
        String contentPoolCacheKey = Constants.REDIS_KEY_RECOM_CONTENTS_BEFORE_RESORT + channelId + ":" + userId;
        // 用户已经推过的内容
        String contentRecommendedSetKey = Constants.REDIS_KEY_RECOM_CONTENTS_ALREADY_RECOM + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + ":" + userId;
        String contentPool = null;
        Set<String> contentRecommended = new HashSet<>();

        try {
            contentPool = redisTemplate.opsForValue().get(contentPoolCacheKey);
        } catch (Exception e) {
            String errorMsg = "查询用户预先计算好的推荐池失败，userId:【%s】".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }

        try {
            contentRecommended = redisTemplate.opsForSet().members(contentRecommendedSetKey);
        } catch (Exception e) {
            String errorMsg = "获取redis已推内容失败，userId:【%s】".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }

        List<String> t1FilterIds = new ArrayList<>(getT1GraphicFilterIds(userId));
        t1FilterIds.addAll(getT1VideoFilterIds(userId));

        if (!channelId.equals("-1")) {
            // 如果参数中的频道没有配置, 代表走冷启动热门召回
            boolean isHotRecallWay = false;
            String channelConfigKey = Constants.REDIS_KEY_RECOM_CHANNEL_IDS_CONFIG_PREFIX;
            try {
                String channelConfig = redisTemplate.opsForValue().get(channelConfigKey);
                if (StringUtils.hasText(channelConfig)) {
                    JSONObject jsonObject = JSON.parseObject(channelConfig);
                    if (jsonObject.containsKey(channelId)) {
                        List<String> list = jsonObject.getJSONArray(channelId).stream().map(String::valueOf).toList();
                        if (!CollectionUtils.isEqualCollection(list, tagList)) {
                            isHotRecallWay = true;
                        }
                    } else {
                        isHotRecallWay = true;
                    }
                } else {
                    isHotRecallWay = true;
                }
            } catch (Exception e) {
                String errorMsg = "读取redis频道配置 => 未获取到redis频道配置, userId:【%s】".formatted(userId);
                log.error(errorMsg, e);
                e.printStackTrace();
                isHotRecallWay = true;
            }

            if (isHotRecallWay) {
                return RecommendResp.of(userId, channelOthColdRecall(userId, tagList, pageSize, contentRecommended, contentRecommendedSetKey, t1FilterIds));
            }
        }

        MultiRecallResultBo multiRecallResultBo = new MultiRecallResultBo();
        if (StringUtils.hasText(contentPool)) {
            ContentRecomPoolListBo contentRecomPoolListBo = JSON.parseObject(contentPool, ContentRecomPoolListBo.class);
            BeanUtils.copyProperties(contentRecomPoolListBo, multiRecallResultBo);

            // 判断缓存池余额
            if (ContentRecomPoolListBo.balanceIsNotEnough(contentRecomPoolListBo)) {
                multiRecallResultBo = mainStrategy(userId, channelId, t1FilterIds, contentRecommended);

                if (null != multiRecallResultBo) {
                    // 查询图文内容详情
                    queryContentDetailFromHot(multiRecallResultBo, userId, channelId);
                }
            } else {
                // 过滤已推和下线
                MultiRecallResultBo.filterExposure(userId, channelId, multiRecallResultBo, redisTemplate, contentRecommended, t1FilterIds);
            }
        } else {
            multiRecallResultBo = mainStrategy(userId, channelId, t1FilterIds, contentRecommended);

            // 查询图文内容详情
            if (null != multiRecallResultBo) {
                // 查询图文内容详情
                queryContentDetailFromHot(multiRecallResultBo, userId, channelId);
            }
        }

        if (null != multiRecallResultBo) {
            ContentRecomPoolListBo contentRecomPoolListBoNew = new ContentRecomPoolListBo();
            BeanUtils.copyProperties(multiRecallResultBo, contentRecomPoolListBoNew);
            if (ContentRecomPoolListBo.balanceIsNotEnough(contentRecomPoolListBoNew)) {
                try {
                    redisTemplate.delete(contentPoolCacheKey);
                } catch (Exception e) {
                    String errorMsg = "redis删除推荐池内容失败, userId:【%s】".formatted(userId);
                    log.error(errorMsg, e);
                    e.printStackTrace();
                }
                multiRecallResultBo = queryGraphicDetailFromHotByBottomFinal(userId, channelId);
            }
        } else {
            try {
                redisTemplate.delete(contentPoolCacheKey);
            } catch (Exception e) {
                String errorMsg = "redis删除推荐池内容失败, userId:【%s】".formatted(userId);
                log.error(errorMsg, e);
                e.printStackTrace();
            }
            multiRecallResultBo = queryGraphicDetailFromHotByBottomFinal(userId, channelId);
        }

        resortStrategy(multiRecallResultBo, userId, channelId, pageSize, redisTemplate);

        List<String> recomResult = multiRecallResultBo.getResult().stream().map(RecommendRespContentInfo::getContentId).toList();
        try {
            // 缓存本次推荐内容id
            asyncHandle.saveCacheRecommend(contentRecommendedSetKey, recomResult, userId);

            List<HotContentInfoBo> ctrSortGraphicDetails = multiRecallResultBo.getCtrSortGraphicDetails();
            List<HotContentInfoBo> ctrSortVideoDetails = multiRecallResultBo.getCtrSortVideoDetails();
            List<HotContentInfoBo> newestGraphicDetails = multiRecallResultBo.getNewestGraphicDetails();
            List<HotContentInfoBo> newestVideoDetails = multiRecallResultBo.getNewestVideoDetails();
            Set<String> filterList;
            if (!CollectionUtils.isEmpty(multiRecallResultBo.getFilterList())) {
                filterList = multiRecallResultBo.getFilterList();
            } else {
                filterList = Set.of();
            }
            List<HotContentInfoBo> ctrSortGraphicDetailsNew = ctrSortGraphicDetails.stream().filter(item -> !recomResult.contains(item.getContentId()) && !filterList.contains(item.getContentId())).toList();
            List<HotContentInfoBo> ctrSortVideoDetailsNew = ctrSortVideoDetails.stream().filter(item -> !recomResult.contains(item.getContentId()) && !filterList.contains(item.getContentId())).toList();
            List<HotContentInfoBo> newestGraphicDetailsNew = newestGraphicDetails.stream().filter(item -> !recomResult.contains(item.getContentId()) && !filterList.contains(item.getContentId())).toList();
            List<HotContentInfoBo> newestVideoDetailsNew = newestVideoDetails.stream().filter(item -> !recomResult.contains(item.getContentId()) && !filterList.contains(item.getContentId())).toList();
            multiRecallResultBo.setCtrSortGraphicDetails(ctrSortGraphicDetailsNew);
            multiRecallResultBo.setCtrSortVideoDetails(ctrSortVideoDetailsNew);
            multiRecallResultBo.setNewestGraphicDetails(newestGraphicDetailsNew);
            multiRecallResultBo.setNewestVideoDetails(newestVideoDetailsNew);
            asyncHandle.saveCacheCTR(multiRecallResultBo, userId, channelId);
        } catch (Exception e) {
            String errorMsg = "缓存已推内容至redis失败, userId:【%s】".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }

        recommendResp.setUserId(userId);
        recommendResp.setContents(multiRecallResultBo.getResult());
        log.warn("userId:【{}】, 全链路耗时:{}ms", userId, System.currentTimeMillis() - l1);
        return recommendResp;
    }


    private List<String> getT1GraphicFilterIds(String userId) {
        String graphicKey = Constants.REDIS_KEY_RECOM_USER_T1_GRAPHIC_FILTER_PREFIX;

        String userGraphicFilter = null;
        List<String> list = List.of();
        try {
            userGraphicFilter = (String) redisTemplate.opsForHash().get(graphicKey, userId);
        } catch (Exception e) {
            String errorMsg = "查询redis获取T1图文过滤内容失败, userIdL:【%s】".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }
        if (StringUtils.hasText(userGraphicFilter)) {
            list = Arrays.stream(userGraphicFilter.split(",")).filter(StringUtils::hasText).toList();
        }
        return list;
    }


    private List<String> getT1VideoFilterIds(String userId) {
        String videoKey = Constants.REDIS_KEY_RECOM_USER_T1_VIDEO_FILTER_PREFIX;

        String userVideoFilter = null;
        List<String> list = List.of();
        try {
            userVideoFilter = (String) redisTemplate.opsForHash().get(videoKey, userId);
        } catch (Exception e) {
            String errorMsg = "查询redis获取T1视频过滤内容失败, userIdL:【%s】".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }
        if (StringUtils.hasText(userVideoFilter)) {
            list = Arrays.stream(userVideoFilter.split(",")).filter(StringUtils::hasText).toList();
        }
        return list;
    }

    private MultiRecallResultBo mainStrategy(
            String userId,
            String channelId,
            List<String> t1FilterIds,
            Set<String> contentRecommended
    ) {
        MultiRecallResultBo multiRecallResultBo = strategyChoose(userId, channelId);

        if (null == multiRecallResultBo || MultiRecallResultBo.isEmpty(multiRecallResultBo)) {
            return null;
        }

        MultiRecallResultBo.mergeAndSortResults(multiRecallResultBo, redisTemplate, t1FilterIds, userId, channelId, contentRecommended);

        // 模型预测分数内容对象
        List<String> graphicCTRSortIds = new ArrayList<>();
        List<String> videosCTRSortIds = new ArrayList<>();

        // 在handle方法中，获取特征并进行预测
        modelPredictHandle(userId, multiRecallResultBo.getGraphicIds(), multiRecallResultBo.getVideoIds(), graphicCTRSortIds, videosCTRSortIds);

        // 根据预测分数对图文内容重新排序
        multiRecallResultBo.setGraphicIds(graphicCTRSortIds);

        multiRecallResultBo.setVideoIds(videosCTRSortIds);
        return multiRecallResultBo;
    }


    public static List<Integer> distributeTextAndImages(int a, int b, int totalElements) {
        List<Integer> result = new ArrayList<>(totalElements);

        // 初始化列表为占位符
        for (int i = 0; i < totalElements; i++) {
            result.add(0); // 占位符
        }

        // 文本和图片的分布间隔
        double textInterval = (double) totalElements / a;
        double imageInterval = (double) totalElements / b;

        // 均匀分布文本
        double currentTextPosition = 0;
        for (int i = 0; i < a; i++) {
            int index = (int) Math.round(currentTextPosition);
            while (index < totalElements && !(result.get(index) == 0)) {
                index++; // 找下一个空位
            }
            if (index < totalElements) {
                result.set(index, 1);
            }
            currentTextPosition += textInterval;
        }

        // 均匀分布图片
        double currentImagePosition = 0;
        for (int i = 0; i < b; i++) {
            int index = (int) Math.round(currentImagePosition);
            while (index < totalElements && !(result.get(index) == 0)) {
                index++; // 找下一个空位
            }
            if (index < totalElements) {
                result.set(index, 2);
            }
            currentImagePosition += imageInterval;
        }

        return result;
    }


    /**
     * 根据比例 a:b 和总数量 totalElements 计算文本和图片的数量
     *
     * @param a             文本的比例
     * @param b             图片的比例
     * @param totalElements 总元素数量
     * @return 一个 Map，键 "text" 表示文本数量，键 "image" 表示图片数量
     */
    public static int calculateTextAndImageCount(int a, int b, int totalElements) {
        // 总比例
        int totalRatio = a + b;
        // 计算文本和图片数量
        return (int) Math.round((double) a / totalRatio * totalElements);
    }

    private static void resortStrategy(
            MultiRecallResultBo multiRecallResultBo,
            String userId,
            String channelId,
            Integer pageSize,
            RedisTemplate<String, String> redisTemplate
    ) {

        long l = System.currentTimeMillis();
        List<ResortCommonBo> result = new ArrayList<>();

        calResortProp(
                userId,
                pageSize,
                channelId,
                multiRecallResultBo.getCtrSortGraphicDetails(),
                multiRecallResultBo.getCtrSortVideoDetails(),
                multiRecallResultBo.getFilterList(),
                result,
                redisTemplate
        );

        if (result.size() < pageSize) {
            log.warn("重排策略 => 重排之后结果不足, 抛出转到最终最终兜底!");
            throw new DIYException();
        }

        // 检查是否需要插入新内容
        checkAndInsertNewContent(
                result,
                userId,
                channelId,
                multiRecallResultBo.getNewestGraphicDetails(),
                multiRecallResultBo.getNewestVideoDetails()
        );

        List<RecommendRespContentInfo> list = result.stream().limit(pageSize).map(r -> RecommendRespContentInfo.build(r.getContentId(), r.getType())).toList();
        multiRecallResultBo.setResult(list);
        log.warn("推荐重排策略 => userId:【{}】, 总耗时:【{}】ms, 推荐结果:【{}】", userId, System.currentTimeMillis() - l, JSON.toJSONString(list));
    }

    private static void checkAndInsertNewContent(
            List<ResortCommonBo> result,
            String userId,
            String channelId,
            List<HotContentInfoBo> newestGraphicDetails,
            List<HotContentInfoBo> newestVideoDetails
    ) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime threeDaysAgo = LocalDateTime.now().minusDays(3);
        int recentCount = (int) Math.round(result.size() * 0.1);
        long currentNewCount = result.stream().filter(o -> LocalDateTime.parse(o.getPublishTime(), formatter).isAfter(threeDaysAgo)).count();
        if (currentNewCount > recentCount) {
            return;
        }

        List<String> resultIdList = new ArrayList<>(result.stream().map(ResortCommonBo::getContentId).toList());
        newestGraphicDetails = newestGraphicDetails.stream().filter(o -> !resultIdList.contains(o.getContentId())).toList();
        newestVideoDetails = newestVideoDetails.stream().filter(o -> !resultIdList.contains(o.getContentId())).toList();


        if (newestGraphicDetails.size() + newestVideoDetails.size() == 0) {
            return;
        }

        // 计算近3天内容的比例
        for (int i = result.size() - 1; i >= 0; i--) {
            if (currentNewCount >= recentCount) {
                break;
            }
            ResortCommonBo resortCommonBo = result.get(i);
            LocalDateTime publishTime = LocalDateTime.parse(resortCommonBo.getPublishTime(), formatter);
            if (!publishTime.isAfter(threeDaysAgo)) {
                String type = resortCommonBo.getType();
                if ("graphic".equals(type)) {
                    Optional<HotContentInfoBo> first = newestGraphicDetails.stream().filter(g -> {
                        // 结果中不包含
                        boolean bool1 = !resultIdList.contains(g.getContentId());
                        // 标签要是一样的
                        boolean bool2 = ("-1".equals(channelId) ? g.getCarBrandNameNew() : g.getTopicKmeansLabel()).equals(resortCommonBo.getTag());
                        return bool1 && bool2;
                    }).findFirst();
                    if (first.isPresent()) {
                        HotContentInfoBo hotGraphicResultInfoT0t1 = first.get();
                        String label = "-1".equals(channelId) ? hotGraphicResultInfoT0t1.getCarBrandNameNew() : hotGraphicResultInfoT0t1.getTopicKmeansLabel();
                        ResortCommonBo graphic = ResortCommonBo.of(hotGraphicResultInfoT0t1.getContentId(), "graphic", hotGraphicResultInfoT0t1.getPublishTime(), label);
                        result.add(i, graphic);
                        resultIdList.add(hotGraphicResultInfoT0t1.getContentId());
                        log.warn("插新逻辑 => userId:【{}】, 在索引【{}】位置插入图文:{}", userId, i, JSON.toJSONString(graphic));
                        currentNewCount++;
                    }
                } else {
                    Optional<HotContentInfoBo> first = newestVideoDetails.stream().filter(g -> {
                        // 结果中不包含
                        boolean bool1 = !resultIdList.contains(g.getContentId());
                        // 标签要是一样的
                        boolean bool2 = ("-1".equals(channelId) ? g.getCarBrandNameNew() : g.getTopicKmeansLabel()).equals(resortCommonBo.getTag());
                        return bool1 && bool2;
                    }).findFirst();
                    if (first.isPresent()) {
                        HotContentInfoBo hotVideoResultInfoT0t = first.get();
                        String label = "-1".equals(channelId) ? hotVideoResultInfoT0t.getCarBrandNameNew() : hotVideoResultInfoT0t.getTopicKmeansLabel();
                        ResortCommonBo video = ResortCommonBo.of(hotVideoResultInfoT0t.getContentId(), "video", hotVideoResultInfoT0t.getPublishTime(), label);
                        result.add(i, video);
                        resultIdList.add(hotVideoResultInfoT0t.getContentId());
                        log.warn("插新逻辑 => userId:【{}】, 在索引【{}】位置插入视频:{}", userId, i, JSON.toJSONString(video));
                        currentNewCount++;
                    }
                }
            }
        }
    }

    private static void calResortProp(
            String userId,
            Integer pageSize,
            String channelId,
            List<HotContentInfoBo> ctrSortGraphicDetails,
            List<HotContentInfoBo> ctrSortVideoDetails,
            Set<String> filterList,
            List<ResortCommonBo> result,
            RedisTemplate<String, String> redisTemplate
    ) {

        if (null == ctrSortGraphicDetails) {
            ctrSortGraphicDetails = new ArrayList<>();
        }

        if (null == ctrSortVideoDetails) {
            ctrSortVideoDetails = new ArrayList<>();
        }

        // 计算图文和视频的数量配比
        int graphicSize = calculateTextAndImageCount(4, 1, pageSize);
        int videoSize = pageSize - graphicSize;
        List<Integer> typeList = distributeTextAndImages(graphicSize, videoSize, pageSize);

        // 创建已选内容的标签队列，用于检查连续性
        Queue<String> selectedLabels = new LinkedList<>();

        // 记录每6个内容中的标签数量
        List<String> labelInSixContentIdList = new ArrayList<>();
        // 记录已经添加进列表的内容
        Set<String> alreadySetIds = new HashSet<>();

        int currentIndex = 0;

        int sideNum = 1;

        while (result.size() < pageSize) {
            if (sideNum >= 30) {
                log.warn("重排策略 => 到达查询次数限制");
                break;
            }
            sideNum++;
            Integer i = typeList.get(currentIndex);
            ResortCommonBo content = null;
            String selectedLabel;
            // 当前位置是图文
            if (i == 1) {
                for (HotContentInfoBo ctrSortGraphicDetail : ctrSortGraphicDetails) {
                    if (!alreadySetIds.contains(ctrSortGraphicDetail.getContentId())) {
                        // 首页选车系, 频道选话题
                        selectedLabel = "-1".equals(channelId) ? ctrSortGraphicDetail.getCarBrandNameNew() : ctrSortGraphicDetail.getTopicKmeansLabel();
                        // 如果不会形成连续的标签就插入
                        if (!isModelConsecutive(selectedLabels, selectedLabel)) {
                            content = ResortCommonBo.of(ctrSortGraphicDetail.getContentId(), "graphic", ctrSortGraphicDetail.getPublishTime(), selectedLabel);
                            // 更新6内容组的标签统计
                            labelInSixContentIdList.add(selectedLabel);

                            // 检查每6个内容是否满足至少3个不同标签的要求
                            if (labelInSixContentIdList.size() == 6) {
                                HashSet<String> set = new HashSet<>(labelInSixContentIdList);
                                if (set.size() < 3) {
                                    content = null;
                                    labelInSixContentIdList = labelInSixContentIdList.subList(0, labelInSixContentIdList.size() - 1);
                                } else {
                                    result.add(content);
                                    alreadySetIds.add(content.getContentId());

                                    labelInSixContentIdList = labelInSixContentIdList.subList(1, labelInSixContentIdList.size());

                                    // 更新标签队列
                                    selectedLabels.offer(selectedLabel);
                                    if (selectedLabels.size() > 3) {
                                        selectedLabels.poll();
                                    }
                                    currentIndex++;
                                    break;
                                }
                            } else {
                                result.add(content);
                                alreadySetIds.add(content.getContentId());

                                // 更新标签队列
                                selectedLabels.offer(selectedLabel);
                                if (selectedLabels.size() > 3) {
                                    selectedLabels.poll();
                                }
                                currentIndex++;
                                break;
                            }
                        }
                    }
                }
                boolean isQuery = true;
                int limitStart = 0;
                int limitEnd = 100;
                int queryNum = 1;
                while (isQuery) {
                    if (queryNum > 10) {
                        break;
                    }
                    if (null == content) {
                        String graphicKey = Constants.REDIS_KEY_RECOM_GRAPHIC_HOT_ZSET_PREFIX + channelId;
                        Set<String> rangeGraphic;
                        List<HotContentInfoBo> graphicDetailList = null;
                        try {
                            queryNum++;
                            rangeGraphic = redisTemplate.opsForZSet().range(graphicKey, limitStart, limitEnd);
                            if (!CollectionUtils.isEmpty(rangeGraphic)) {
                                graphicDetailList = rangeGraphic.stream()
                                        .filter(Objects::nonNull)
                                        .map(o -> {
                                            JSONObject json = JSON.parseObject(o);
                                            if (filterList.contains(json.getString("content_id"))) {
                                                return null;
                                            }
                                            if (!CommonHandel.isWithinOneYear(json.getString("publish_time"))) {
                                                return null;
                                            }
                                            return HotContentInfoBo.ofJSON(json);
                                        })
                                        .filter(Objects::nonNull)
                                        .toList();
                                if (CollectionUtils.isEmpty(graphicDetailList)) {
                                    limitStart = limitEnd;
                                    limitEnd += 100;
                                }
                            }
                        } catch (Exception e) {
                            String errorMsg = "userId:【%s】, 图文热度表补足, 查询图文热度表".formatted(userId);
                            log.error(errorMsg, e);
                            e.printStackTrace();
                        }

                        if (!CollectionUtils.isEmpty(graphicDetailList)) {
                            for (HotContentInfoBo graphicDetail : graphicDetailList) {
                                if (!alreadySetIds.contains(graphicDetail.getContentId())) {
                                    // 首页选车系, 频道选话题
                                    selectedLabel = "-1".equals(channelId) ? graphicDetail.getCarBrandNameNew() : graphicDetail.getTopicKmeansLabel();

                                    // 如果不会形成连续的标签就插入
                                    if (!isModelConsecutive(selectedLabels, selectedLabel)) {
                                        content = ResortCommonBo.of(graphicDetail.getContentId(), "graphic", graphicDetail.getPublishTime(), selectedLabel);

                                        // 更新6内容组的标签统计
                                        labelInSixContentIdList.add(selectedLabel);

                                        // 检查每6个内容是否满足至少3个不同标签的要求
                                        if (labelInSixContentIdList.size() == 6) {
                                            HashSet<String> set = new HashSet<>(labelInSixContentIdList);
                                            if (set.size() < 3) {
                                                content = null;
                                                labelInSixContentIdList = labelInSixContentIdList.subList(0, labelInSixContentIdList.size() - 1);
                                            } else {
                                                result.add(content);
                                                alreadySetIds.add(content.getContentId());

                                                labelInSixContentIdList = labelInSixContentIdList.subList(1, labelInSixContentIdList.size());

                                                // 更新标签队列
                                                selectedLabels.offer(selectedLabel);
                                                if (selectedLabels.size() > 3) {
                                                    selectedLabels.poll();
                                                }
                                                currentIndex++;
                                                isQuery = false;
                                                break;
                                            }
                                        } else {
                                            result.add(content);
                                            alreadySetIds.add(content.getContentId());

                                            // 更新标签队列
                                            selectedLabels.offer(selectedLabel);
                                            if (selectedLabels.size() > 3) {
                                                selectedLabels.poll();
                                            }
                                            currentIndex++;
                                            isQuery = false;
                                            break;
                                        }
                                    }
                                }
                            }
                            limitStart = limitEnd;
                            limitEnd += 100;
                        }
                    } else {
                        isQuery = false;
                    }
                }
            } else {
                for (HotContentInfoBo ctrSortVideoDetail : ctrSortVideoDetails) {
                    if (!alreadySetIds.contains(ctrSortVideoDetail.getContentId())) {
                        // 首页选车系, 频道选话题
                        selectedLabel = "-1".equals(channelId) ? ctrSortVideoDetail.getCarBrandNameNew() : ctrSortVideoDetail.getTopicKmeansLabel();

                        // 如果不会形成连续的标签就插入
                        if (!isModelConsecutive(selectedLabels, selectedLabel)) {
                            content = ResortCommonBo.of(ctrSortVideoDetail.getContentId(), "video", ctrSortVideoDetail.getPublishTime(), selectedLabel);

                            // 更新6内容组的标签统计
                            labelInSixContentIdList.add(selectedLabel);

                            // 检查每6个内容是否满足至少3个不同标签的要求
                            if (labelInSixContentIdList.size() == 6) {
                                HashSet<String> set = new HashSet<>(labelInSixContentIdList);
                                if (set.size() < 3) {
                                    content = null;
                                    labelInSixContentIdList = labelInSixContentIdList.subList(0, labelInSixContentIdList.size() - 1);
                                } else {
                                    result.add(content);
                                    alreadySetIds.add(content.getContentId());

                                    labelInSixContentIdList = labelInSixContentIdList.subList(1, labelInSixContentIdList.size());

                                    // 更新标签队列
                                    selectedLabels.offer(selectedLabel);
                                    if (selectedLabels.size() > 3) {
                                        selectedLabels.poll();
                                    }
                                    currentIndex++;
                                    break;
                                }
                            } else {
                                result.add(content);
                                alreadySetIds.add(content.getContentId());

                                // 更新标签队列
                                selectedLabels.offer(selectedLabel);
                                if (selectedLabels.size() > 3) {
                                    selectedLabels.poll();
                                }
                                currentIndex++;
                                break;
                            }
                        }
                    }
                }
                if (null == content) {
                    typeList.set(currentIndex, 1);
                }
            }
        }
    }


    // 检查标签是否会造成连续
    private static boolean isModelConsecutive(Queue<String> selectedLabels, String newLabel) {
        if (selectedLabels.size() < 2) return false;

        // 检查是否会形成连续3个相同标签
        List<String> lastLabels = new ArrayList<>(selectedLabels);
        return lastLabels.get(lastLabels.size() - 1).equals(newLabel)
               && lastLabels.get(lastLabels.size() - 2).equals(newLabel);
    }

    private void queryContentDetailFromHot(MultiRecallResultBo multiRecallResultBo, String userId, String channelId) {
        List<String> graphicIds = multiRecallResultBo.getGraphicIds();
        // 查询图文内容详情
        if (!CollectionUtils.isEmpty(multiRecallResultBo.getGraphicIds())) {

            String graphicKey = Constants.REDIS_KEY_RECOM_GRAPHIC_HOT_HASH_PREFIX + channelId;

            List<Object> graphics;
            List<HotContentInfoBo> graphicDetailList = new ArrayList<>();
            try {
                graphics = redisTemplate.opsForHash().multiGet(graphicKey, graphicIds.stream().map(o -> (Object) o).toList());
                if (!CollectionUtils.isEmpty(graphics)) {
                    graphicDetailList = graphics.stream()
                            .filter(Objects::nonNull)
                            .map(String::valueOf)
                            .map(o -> o.replaceAll("\"nan\"", "null"))
                            .map(JSONObject::parseObject)
                            .filter(o -> CommonHandel.isWithinOneYear(o.getString("publish_time")))
                            .map(HotContentInfoBo::ofJSON).toList();
                }
            } catch (Exception e) {
                String errorMsg = "查询图文内容详情失败, userId:【%s】".formatted(userId);
                log.error(errorMsg, e);
                e.printStackTrace();
            }

            if (!CollectionUtils.isEmpty(graphicDetailList)) {
                graphicDetailList.forEach(o -> {
                    if (!StringUtils.hasText(o.getCarBrandNameNew())) {
                        o.setCarBrandNameNew("");
                    }
                    if (!StringUtils.hasText(o.getTopicKmeansLabel())) {
                        o.setTopicKmeansLabel("");
                    }
                });
                multiRecallResultBo.setCtrSortGraphicDetails(graphicDetailList);
                multiRecallResultBo.setNewestGraphicDetails(graphicDetailList.stream().filter(HotContentInfoBo::isNewest).toList());
            }
        }


        List<String> videoIds = multiRecallResultBo.getVideoIds();
        // 查询视频内容详情
        if (!CollectionUtils.isEmpty(multiRecallResultBo.getVideoIds())) {

            String videoKey = Constants.REDIS_KEY_RECOM_VIDEO_HOT_HASH_PREFIX + channelId;

            List<Object> videos;
            List<HotContentInfoBo> videoDetailList = new ArrayList<>();
            try {
                videos = redisTemplate.opsForHash().multiGet(videoKey, videoIds.stream().map(o -> (Object) o).toList());
                if (!CollectionUtils.isEmpty(videos)) {
                    videoDetailList = videos.stream()
                            .filter(Objects::nonNull)
                            .map(String::valueOf)
                            .map(o -> o.replaceAll("\"nan\"", "null"))
                            .map(JSONObject::parseObject)
                            .filter(o -> CommonHandel.isWithinOneYear(o.getString("publish_time")))
                            .map(HotContentInfoBo::ofJSON).toList();
                }
            } catch (Exception e) {
                String errorMsg = "查询视频内容详情失败, userId:【%s】".formatted(userId);
                log.error(errorMsg, e);
                e.printStackTrace();
            }


            if (!CollectionUtils.isEmpty(videoDetailList)) {
                videoDetailList.forEach(o -> {
                    if (!StringUtils.hasText(o.getCarBrandNameNew())) {
                        o.setCarBrandNameNew("");
                    }
                    if (!StringUtils.hasText(o.getTopicKmeansLabel())) {
                        o.setTopicKmeansLabel("");
                    }
                });
                multiRecallResultBo.setCtrSortVideoDetails(videoDetailList);
                multiRecallResultBo.setNewestVideoDetails(videoDetailList.stream().filter(HotContentInfoBo::isNewest).toList());
            }
        }
    }


    private MultiRecallResultBo queryGraphicDetailFromHotByBottomFinal(String userId, String channelId) {
        MultiRecallResultBo multiRecallResultBo = new MultiRecallResultBo();

        String graphicKey = Constants.REDIS_KEY_RECOM_GRAPHIC_SET_TOP_PREFIX + channelId;
        String videoKey = Constants.REDIS_KEY_RECOM_VIDEO_SET_TOP_PREFIX + channelId;
        List<String> randomGraphic;
        List<HotContentInfoBo> graphicDetailList = new ArrayList<>();
        try {
            randomGraphic = redisTemplate.opsForSet().randomMembers(graphicKey, 250);
            if (!CollectionUtils.isEmpty(randomGraphic)) {
                graphicDetailList = randomGraphic.stream()
                        .filter(Objects::nonNull)
                        .map(o -> o.replaceAll("\"nan\"", "null"))
                        .map(JSONObject::parseObject)
                        .map(HotContentInfoBo::ofJSON)
                        .limit(150)
                        .toList();
            }
        } catch (Exception e) {
            String errorMsg = "图文兜底策略, redis随机获取图文热度表失败, userId:【%s】".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }

        List<String> randomVideo;
        List<HotContentInfoBo> videoDetailList = new ArrayList<>();
        try {
            randomVideo = redisTemplate.opsForSet().randomMembers(videoKey, 250);
            if (!CollectionUtils.isEmpty(randomVideo)) {
                videoDetailList = randomVideo
                        .stream()
                        .filter(Objects::nonNull)
                        .map(o -> o.replaceAll("\"nan\"", "null"))
                        .map(JSONObject::parseObject)
                        .map(HotContentInfoBo::ofJSON)
                        .limit(150)
                        .toList();
            }
        } catch (Exception e) {
            String errorMsg = "视频兜底策略, redis随机获取视频热度表失败, userId:【%s】".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }

        if (!CollectionUtils.isEmpty(graphicDetailList)) {
            graphicDetailList.forEach(o -> {
                if (!StringUtils.hasText(o.getCarBrandNameNew())) {
                    o.setCarBrandNameNew("");
                }
                if (!StringUtils.hasText(o.getTopicKmeansLabel())) {
                    o.setTopicKmeansLabel("");
                }
            });
            multiRecallResultBo.setCtrSortGraphicDetails(graphicDetailList);
            multiRecallResultBo.setNewestGraphicDetails(graphicDetailList.stream().filter(HotContentInfoBo::isNewest).toList());
        }

        if (!CollectionUtils.isEmpty(videoDetailList)) {
            videoDetailList.forEach(o -> {
                if (!StringUtils.hasText(o.getCarBrandNameNew())) {
                    o.setCarBrandNameNew("");
                }
                if (!StringUtils.hasText(o.getTopicKmeansLabel())) {
                    o.setTopicKmeansLabel("");
                }
            });
            multiRecallResultBo.setCtrSortVideoDetails(videoDetailList);
            multiRecallResultBo.setNewestVideoDetails(videoDetailList.stream().filter(HotContentInfoBo::isNewest).toList());
        }
        log.warn("兜底逻辑 => userId:【{}】, 召回结果不可用, 进行热度表随机100兜底", userId);
        return multiRecallResultBo;
    }


    private void modelPredictHandle(
            String userId,
            List<String> graphicIds,
            List<String> videosIds,
            List<String> graphicCTRSortIds,
            List<String> videosCTRSortIds
    ) {
        long l1 = System.currentTimeMillis();
        List<String> graphicContentIds = new ArrayList<>();
        List<String> videoContentIds = new ArrayList<>();

        List<List<Double>> graphicFeature = new ArrayList<>();
        List<List<Double>> videoFeature = new ArrayList<>();
        List<List<Double>> features = new ArrayList<>();


        // 转换为模型特征进行模型排序
        if (!CollectionUtils.isEmpty(graphicIds)) {
            Map<String, ArrayList<Double>> graphicFeatures = getGraphicFeatures(userId, graphicIds);
            graphicFeatures.forEach((k, v) -> {
                graphicContentIds.add(k);
                graphicFeature.add(v);
            });
        }

        if (!CollectionUtils.isEmpty(videosIds)) {
            Map<String, ArrayList<Double>> videoFeatures = getVideoFeatures(userId, videosIds);
            videoFeatures.forEach((k, v) -> {
                videoContentIds.add(k);
                videoFeature.add(v);
            });
        }

        if (!CollectionUtils.isEmpty(graphicFeature)) {
            features.addAll(graphicFeature);
        }

        if (!CollectionUtils.isEmpty(videoFeature)) {
            features.addAll(videoFeature);
        }

        List<Double> array = null;
        if (!CollectionUtils.isEmpty(features)) {
            JSONObject json = new JSONObject();
            json.put("features", features);
            json.put("userId", userId);

            Map<String, List<String>> header = new HashMap<>();
            header.put("Content-Type", Collections.singletonList("application/json"));

            String post = null;
            try {
                post = HttpRequest
                        .post("http://gwm-app-recom-ctr-model-service:5000/ctr-sort/predict")
//                        .post("http://127.0.0.1:5000/ctr-sort/predict")
                        .header(header, true)
                        .body(JSON.toJSONString(json))
                        .execute()
                        .body();
                if (StringUtils.hasText(post)) {
                    array = JSON.parseArray(JSON.parseObject(post).getString("data"), Double.class);
                }
            } catch (Exception e) {
                String errorMsg = "老接口CTR排序异常, userId:【%s】".formatted(userId);
                log.error(errorMsg, e);
                e.printStackTrace();
            }

            List<Double> graphicSort;
            List<Double> videoSort;
            if (!CollectionUtils.isEmpty(array)) {
                graphicSort = array.subList(0, graphicFeature.size());
                videoSort = array.subList(graphicFeature.size(), array.size());
            } else {
                graphicSort = List.of();
                videoSort = List.of();
            }

            List<ContentScoreVo> graphicScoreVoList = new ArrayList<>();
            List<ContentScoreVo> videoScoreVoList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(graphicContentIds)) {
                for (int i = 0; i < graphicContentIds.size(); i++) {
                    ContentScoreVo contentScoreVo = new ContentScoreVo();
                    contentScoreVo.setContentId(graphicContentIds.get(i));
                    contentScoreVo.setScore(String.valueOf(Objects.isNull(graphicSort.get(i)) ? 0.00 : graphicSort.get(i)));
                    graphicScoreVoList.add(contentScoreVo);
                }
            }

            if (!CollectionUtils.isEmpty(videoContentIds)) {
                for (int i = 0; i < videoContentIds.size(); i++) {
                    ContentScoreVo contentScoreVo = new ContentScoreVo();
                    contentScoreVo.setContentId(videoContentIds.get(i));
                    contentScoreVo.setScore(String.valueOf(Objects.isNull(videoSort.get(i)) ? 0.00 : videoSort.get(i)));
                    videoScoreVoList.add(contentScoreVo);
                }
            }

            graphicCTRSortIds.addAll(graphicScoreVoList.stream().sorted((o1, o2) -> {
                Double o1v = Double.parseDouble(o1.getScore());
                Double o2v = Double.parseDouble(o2.getScore());
                return o2v.compareTo(o1v);
            }).map(ContentScoreVo::getContentId).limit(250).toList());

            videosCTRSortIds.addAll(videoScoreVoList.stream().sorted((o1, o2) -> {
                Double o1v = Double.parseDouble(o1.getScore());
                Double o2v = Double.parseDouble(o2.getScore());
                return o2v.compareTo(o1v);
            }).map(ContentScoreVo::getContentId).limit(250).toList());
        } else {
            graphicCTRSortIds.addAll(graphicIds.stream().limit(250).toList());
            videosCTRSortIds.addAll(videosIds.stream().limit(250).toList());
        }

        log.warn("CTR重排策略 => userId:【{}】, 总耗时:【{}】ms", userId, System.currentTimeMillis() - l1);
    }


    /**
     * 根据用户ID和渠道ID选择推荐策略
     *
     * @param userId    用户ID
     * @param channelId 渠道ID
     * @return 多路召回结果
     */
    private MultiRecallResultBo strategyChoose(String userId, String channelId) {
        MultiRecallResultBo multiRecallResultBo;
        if (!StringUtils.hasText(userId)) {
            RecallHandle hotRecallHandle = getRecallHandle(RecallWayEnum.HOT.getCode());
            multiRecallResultBo = hotRecallHandle.handle(userId, channelId);
            log.warn("召回线路 => userId:【{}】, 无用户id, 热门召回策略!", userId);
        } else {
            boolean hasUserBehavior = hasUserBehavior(userId, channelId);
            if (!hasUserBehavior) {
                String labelKey = Constants.REDIS_KEY_RECOM_USER_SELECT_INTEREST_LABEL + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + ":" + userId;
                String label = null;
                try {
                    label = redisTemplate.opsForValue().get(labelKey);
                } catch (Exception e) {
                    String errorMsg = "查询用户当天兴趣标签失败, userId:【%s】".formatted(userId);
                    log.error(errorMsg, e);
                    e.printStackTrace();
                }
                if (StringUtils.hasText(label)) {
                    log.warn("召回线路 => userId:【{}】, 无用户行为 => 有兴趣标签 => 兴趣标签匹配热度表策略!", userId);
                    RecallHandle interestRecallHandle = getRecallHandle(RecallWayEnum.USER_INTEREST.getCode());
                    multiRecallResultBo = interestRecallHandle.handle(userId, channelId);
                } else {
                    log.warn("召回线路 => userId:【{}】, 无用户行为 => 无兴趣标签 => 离线兴趣标签表策略!", userId);
                    RecallUserInterestHandle interestRecallHandle = SpringUtil.getBean(RecallUserInterestHandle.class);
                    multiRecallResultBo = interestRecallHandle.notExistOnlineInterestRecallHandle(userId, channelId);
                }
            } else {
                log.warn("召回线路 => userId:【{}】, 有用户行为, 多路召回策略!", userId);
                multiRecallResultBo = multiRecall(userId, channelId);
            }
        }
        return multiRecallResultBo;
    }


    /**
     * 多路召回实现
     * 同时使用多种召回策略并行处理，最后合并结果
     *
     * @param userId    用户ID
     * @param channelId 渠道ID
     * @return 多路召回结果
     */
    private MultiRecallResultBo multiRecall(String userId, String channelId) {
        MultiRecallResultBo multiRecallResultBo = new MultiRecallResultBo();
        try {
            CompletableFuture<MultiRecallResultBo> hotFuture = asyncHandle.asyncHotHandle(userId, channelId);
            CompletableFuture<MultiRecallResultBo> contentFuture = asyncHandle.asyncContentHandle(userId, channelId);
            CompletableFuture<MultiRecallResultBo> userCFFuture = asyncHandle.asyncUserCFHandle(userId, channelId);
            CompletableFuture<MultiRecallResultBo> itemCFFuture = asyncHandle.asyncItemCFHandle(userId, channelId);
            CompletableFuture<MultiRecallResultBo> interestFuture = asyncHandle.asyncInterestHandle(userId, channelId);
            CompletableFuture<MultiRecallResultBo> followFuture = asyncHandle.asyncUserFollowHandle(userId, channelId);
            // 等待所有异步任务完成
            CompletableFuture.allOf(hotFuture, contentFuture, userCFFuture, itemCFFuture, interestFuture, followFuture).join();

            // 合并各路召回结果
            MultiRecallResultBo hotResult = hotFuture.get();
            multiRecallResultBo.setHotGraphics(hotResult.getHotGraphics());
            multiRecallResultBo.setHotVideos(hotResult.getHotVideos());

            MultiRecallResultBo contentResult = contentFuture.get();
            multiRecallResultBo.setContentGraphics(contentResult.getContentGraphics());
            multiRecallResultBo.setContentVideos(contentResult.getContentVideos());

            MultiRecallResultBo userCFResult = userCFFuture.get();
            multiRecallResultBo.setUserCfGraphics(userCFResult.getUserCfGraphics());
            multiRecallResultBo.setUserCfVideos(userCFResult.getUserCfVideos());

            MultiRecallResultBo itemCFResult = itemCFFuture.get();
            multiRecallResultBo.setItemCfGraphics(itemCFResult.getItemCfGraphics());
            multiRecallResultBo.setItemCfVideos(itemCFResult.getItemCfVideos());

            MultiRecallResultBo interestResult = interestFuture.get();
            multiRecallResultBo.setUserInterestGraphics(interestResult.getUserInterestGraphics());
            multiRecallResultBo.setUserInterestVideos(interestResult.getUserInterestVideos());

            MultiRecallResultBo followResult = followFuture.get();
            multiRecallResultBo.setUserFollowGraphics(followResult.getUserFollowGraphics());
            multiRecallResultBo.setUserFollowVideos(interestResult.getUserFollowVideos());


        } catch (Exception e) {
            String errorMsg = "多线程获取创建多路召回任务失败, userId:【%s】".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
            return null;
        }
        return multiRecallResultBo;
    }

    /**
     * 检查用户是否有行为数据
     * 通过查询用户最近的行为记录来判断
     *
     * @param userId 用户ID
     * @return 是否有行为数据
     */
    private boolean hasUserBehavior(String userId, String channelId) {
        String dateStringT0 = LocalDateTime.now().minusHours(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH"));
        String behaviourT1 = Constants.REDIS_KEY_RECOM_T1_BEHAVIOUR_PREFIX + channelId;
        String behaviourT0 = Constants.REDIS_KEY_RECOM_T0_BEHAVIOUR_PREFIX + dateStringT0 + ":" + channelId;
        boolean hasKeyT1 = false;
        try {
            hasKeyT1 = redisTemplate.opsForHash().hasKey(behaviourT1, userId);
        } catch (Exception e) {
            String errorMsg = "userId不为空, 查询redis检查T1是否有用户行为失败, userId:【%s】".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }

        if (hasKeyT1) {
            return true;
        }

        boolean hasKeyT0 = false;
        try {
            hasKeyT0 = redisTemplate.opsForHash().hasKey(behaviourT0, userId);
        } catch (Exception e) {
            String errorMsg = "userId不为空, 查询redis检查T0是否有用户行为失败, userId:【%s】".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }

        return hasKeyT0;
    }


    /**
     * 获取图文内容特征
     */
    private Map<String, ArrayList<Double>> getGraphicFeatures(String userId, List<String> graphicIds) {
        String userFeatureKey = Constants.REDIS_KEY_RECOM_USER_GRAPHIC_FEATURE_PREFIX;
        String contentFeatureKey = Constants.REDIS_KEY_RECOM_CONTENT_FEATURE_PREFIX;

        ArrayList<Double> userFeature = new ArrayList<>();
        if (StringUtils.hasText(userId)) {
            String userFeatureStr;
            try {
                userFeatureStr = redisTemplate.opsForValue().get(userFeatureKey + ":" + userId);
                if (StringUtils.hasText(userFeatureStr)) {
                    JSONObject jsonObject = JSONObject.parseObject(userFeatureStr.replaceAll("NaN", "null"));
                    userFeature.add(null == jsonObject.getDouble("register_day") ? 0.00 : jsonObject.getDouble("register_day"));
                    userFeature.add(null == jsonObject.getDouble("age") ? 33.00 : jsonObject.getDouble("age"));
                    userFeature.add(null == jsonObject.getDouble("fans_cnt") ? 0.00 : jsonObject.getDouble("fans_cnt"));
                    userFeature.add(null == jsonObject.getDouble("user_select_app_tag") ? 0.00 : jsonObject.getDouble("user_select_app_tag"));
                    userFeature.add(null == jsonObject.getDouble("sex") ? 2.00 : jsonObject.getDouble("sex"));
                    userFeature.add(null == jsonObject.getDouble("user_select_car_scene") ? 0.00 : jsonObject.getDouble("user_select_car_scene"));
                    userFeature.add(null == jsonObject.getDouble("user_select_price_range") ? 0.00 : jsonObject.getDouble("user_select_price_range"));
                    userFeature.add(null == jsonObject.getDouble("user_select_drive_mode") ? 0.00 : jsonObject.getDouble("user_select_drive_mode"));
                    userFeature.add(null == jsonObject.getDouble("user_recent_3d_behaviours_ctr") ? 0.00 : jsonObject.getDouble("user_recent_3d_behaviours_ctr"));
                    userFeature.add(null == jsonObject.getDouble("user_recent_7d_behaviours_ctr") ? 0.00 : jsonObject.getDouble("user_recent_7d_behaviours_ctr"));
                    userFeature.add(null == jsonObject.getDouble("user_recent_7d_behaviours_brand_name_set") ? 0.00 : jsonObject.getDouble("user_recent_7d_behaviours_brand_name_set"));
                    userFeature.add(null == jsonObject.getDouble("user_recent_7d_most_ctr_brand_name") ? 0.00 : jsonObject.getDouble("user_recent_7d_most_ctr_brand_name"));
                    userFeature.add(null == jsonObject.getDouble("user_recent_7d_behaviours_cnt") ? 0.00 : jsonObject.getDouble("user_recent_7d_behaviours_cnt"));
                    userFeature.add(null == jsonObject.getDouble("user_recent_7d_behaviours_topic_id_set") ? 0.00 : jsonObject.getDouble("user_recent_7d_behaviours_topic_id_set"));
                } else {
                    userFeature.add(0.00);
                    userFeature.add(33.00);
                    userFeature.add(0.00);
                    userFeature.add(0.00);
                    userFeature.add(2.00);
                    userFeature.add(0.00);
                    userFeature.add(0.00);
                    userFeature.add(0.00);
                    userFeature.add(0.00);
                    userFeature.add(0.00);
                    userFeature.add(0.00);
                    userFeature.add(0.00);
                    userFeature.add(0.00);
                    userFeature.add(0.00);
                }
            } catch (Exception e) {
                String errorMsg = "获取图文用户特征失败, userId:【%s】".formatted(userId);
                log.error(errorMsg, e);
                e.printStackTrace();
            }

        }

        Map<String, ArrayList<Double>> featureMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(graphicIds)) {
            List<String> contentFeaturesObj;
            try {
                contentFeaturesObj = redisTemplate.opsForValue().multiGet(graphicIds.stream().map(o -> contentFeatureKey + ":" + o).toList());
                if (!CollectionUtils.isEmpty(contentFeaturesObj)) {
                    for (int i = 0; i < graphicIds.size(); i++) {
                        if (Objects.nonNull(contentFeaturesObj.get(i))) {
                            JSONObject jsonObject = JSONObject.parseObject(contentFeaturesObj.get(i).replaceAll("NaN", "null"));
                            ArrayList<Double> contentFeature = new ArrayList<>(userFeature);
                            contentFeature.add(jsonObject.getDouble("content_recent_3d_behaviours_ctr"));
                            contentFeature.add(jsonObject.getDouble("content_recent_3d_behaviours_cnt"));
                            contentFeature.add(jsonObject.getDouble("content_recent_3d_click_cnt"));
                            contentFeature.add(jsonObject.getDouble("content_recent_3d_like_cnt"));
                            contentFeature.add(jsonObject.getDouble("content_recent_7d_behaviours_ctr"));
                            contentFeature.add(jsonObject.getDouble("content_recent_7d_behaviours_cnt"));
                            contentFeature.add(jsonObject.getDouble("content_recent_7d_click_cnt"));
                            contentFeature.add(jsonObject.getDouble("content_recent_7d_like_cnt"));
                            contentFeature.add(jsonObject.getDouble("topic_kmeans_label"));
                            contentFeature.add(jsonObject.getDouble("publisher"));
                            contentFeature.add(jsonObject.getDouble("publish_day"));
                            contentFeature.add(jsonObject.getDouble("car_model_name_new"));
                            contentFeature.add(jsonObject.getDouble("topic_cnt"));
                            contentFeature.add(jsonObject.getDouble("post_app_tag"));
                            contentFeature.add(jsonObject.getDouble("content_type"));
                            contentFeature.add(jsonObject.getDouble("car_brand_name_new"));
                            featureMap.put(graphicIds.get(i), contentFeature);
                        } else {
                            ArrayList<Double> contentFeature = new ArrayList<>(userFeature);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            featureMap.put(graphicIds.get(i), contentFeature);
                        }
                    }
                }
            } catch (Exception e) {
                String errorMsg = "redis获取内容特征失败, userId:【%s】".formatted(userId);
                log.error(errorMsg, e);
                e.printStackTrace();
            }
        }
        // 组合特征
        return featureMap;
    }

    /**
     * 获取视频内容特征
     */
    private Map<String, ArrayList<Double>> getVideoFeatures(String userId, List<String> videosIds) {
        String userFeatureKey = Constants.REDIS_KEY_RECOM_USER_VIDEO_FEATURE_PREFIX;
        String contentFeatureKey = Constants.REDIS_KEY_RECOM_CONTENT_FEATURE_PREFIX;

        ArrayList<Double> userFeature = new ArrayList<>();
        if (StringUtils.hasText(userId)) {
            String userFeatureStr;
            try {
                userFeatureStr = redisTemplate.opsForValue().get(userFeatureKey + ":" + userId);
                if (StringUtils.hasText(userFeatureStr)) {
                    JSONObject jsonObject = JSONObject.parseObject(userFeatureStr.replaceAll("NaN", "null"));
                    userFeature.add(null == jsonObject.getDouble("register_day") ? 0.00 : jsonObject.getDouble("register_day"));
                    userFeature.add(null == jsonObject.getDouble("age") ? 33.00 : jsonObject.getDouble("age"));
                    userFeature.add(null == jsonObject.getDouble("fans_cnt") ? 0.00 : jsonObject.getDouble("fans_cnt"));
                    userFeature.add(null == jsonObject.getDouble("user_select_app_tag") ? 0.00 : jsonObject.getDouble("user_select_app_tag"));
                    userFeature.add(null == jsonObject.getDouble("sex") ? 2.00 : jsonObject.getDouble("sex"));
                    userFeature.add(null == jsonObject.getDouble("user_select_car_scene") ? 0.00 : jsonObject.getDouble("user_select_car_scene"));
                    userFeature.add(null == jsonObject.getDouble("user_select_price_range") ? 0.00 : jsonObject.getDouble("user_select_price_range"));
                    userFeature.add(null == jsonObject.getDouble("user_select_drive_mode") ? 0.00 : jsonObject.getDouble("user_select_drive_mode"));
                    userFeature.add(null == jsonObject.getDouble("user_recent_3d_behaviours_ctr") ? 0.00 : jsonObject.getDouble("user_recent_3d_behaviours_ctr"));
                    userFeature.add(null == jsonObject.getDouble("user_recent_7d_behaviours_ctr") ? 0.00 : jsonObject.getDouble("user_recent_7d_behaviours_ctr"));
                    userFeature.add(null == jsonObject.getDouble("user_recent_7d_behaviours_brand_name_set") ? 0.00 : jsonObject.getDouble("user_recent_7d_behaviours_brand_name_set"));
                    userFeature.add(null == jsonObject.getDouble("user_recent_7d_most_ctr_brand_name") ? 0.00 : jsonObject.getDouble("user_recent_7d_most_ctr_brand_name"));
                    userFeature.add(null == jsonObject.getDouble("user_recent_7d_behaviours_cnt") ? 0.00 : jsonObject.getDouble("user_recent_7d_behaviours_cnt"));
                    userFeature.add(null == jsonObject.getDouble("user_recent_7d_behaviours_topic_id_set") ? 0.00 : jsonObject.getDouble("user_recent_7d_behaviours_topic_id_set"));
                } else {
                    userFeature.add(0.00);
                    userFeature.add(33.00);
                    userFeature.add(0.00);
                    userFeature.add(0.00);
                    userFeature.add(2.00);
                    userFeature.add(0.00);
                    userFeature.add(0.00);
                    userFeature.add(0.00);
                    userFeature.add(0.00);
                    userFeature.add(0.00);
                    userFeature.add(0.00);
                    userFeature.add(0.00);
                    userFeature.add(0.00);
                    userFeature.add(0.00);
                }
            } catch (Exception e) {
                String errorMsg = "获取图文用户特征失败, userId:【%s】".formatted(userId);
                log.error(errorMsg, e);
                e.printStackTrace();
            }
        }

        Map<String, ArrayList<Double>> featureMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(videosIds)) {
            List<String> contentFeaturesObj;
            try {
                contentFeaturesObj = redisTemplate.opsForValue().multiGet(videosIds.stream().map(o -> contentFeatureKey + ":" + o).toList());
                if (!CollectionUtils.isEmpty(contentFeaturesObj)) {
                    for (int i = 0; i < videosIds.size(); i++) {
                        if (Objects.nonNull(contentFeaturesObj.get(i))) {
                            JSONObject jsonObject = JSONObject.parseObject(contentFeaturesObj.get(i).replaceAll("NaN", "null"));
                            ArrayList<Double> contentFeature = new ArrayList<>(userFeature);
                            contentFeature.add(null == jsonObject.getDouble("content_recent_3d_behaviours_ctr") ? 0.00 : jsonObject.getDouble("content_recent_3d_behaviours_ctr"));
                            contentFeature.add(null == jsonObject.getDouble("content_recent_3d_behaviours_cnt") ? 0.00 : jsonObject.getDouble("content_recent_3d_behaviours_cnt"));
                            contentFeature.add(null == jsonObject.getDouble("content_recent_3d_click_cnt") ? 0.00 : jsonObject.getDouble("content_recent_3d_click_cnt"));
                            contentFeature.add(null == jsonObject.getDouble("content_recent_3d_like_cnt") ? 0.00 : jsonObject.getDouble("content_recent_3d_like_cnt"));
                            contentFeature.add(null == jsonObject.getDouble("content_recent_7d_behaviours_ctr") ? 0.00 : jsonObject.getDouble("content_recent_7d_behaviours_ctr"));
                            contentFeature.add(null == jsonObject.getDouble("content_recent_7d_behaviours_cnt") ? 0.00 : jsonObject.getDouble("content_recent_7d_behaviours_cnt"));
                            contentFeature.add(null == jsonObject.getDouble("content_recent_7d_click_cnt") ? 0.00 : jsonObject.getDouble("content_recent_7d_click_cnt"));
                            contentFeature.add(null == jsonObject.getDouble("content_recent_7d_like_cnt") ? 0.00 : jsonObject.getDouble("content_recent_7d_like_cnt"));
                            contentFeature.add(null == jsonObject.getDouble("topic_kmeans_label") ? 0.00 : jsonObject.getDouble("topic_kmeans_label"));
                            contentFeature.add(null == jsonObject.getDouble("publisher") ? 0.00 : jsonObject.getDouble("publisher"));
                            contentFeature.add(null == jsonObject.getDouble("publish_day") ? 0.00 : jsonObject.getDouble("publish_day"));
                            contentFeature.add(null == jsonObject.getDouble("car_model_name_new") ? 0.00 : jsonObject.getDouble("car_model_name_new"));
                            contentFeature.add(null == jsonObject.getDouble("topic_cnt") ? 0.00 : jsonObject.getDouble("topic_cnt"));
                            contentFeature.add(null == jsonObject.getDouble("post_app_tag") ? 0.00 : jsonObject.getDouble("post_app_tag"));
                            contentFeature.add(null == jsonObject.getDouble("content_type") ? 0.00 : jsonObject.getDouble("content_type"));
                            contentFeature.add(null == jsonObject.getDouble("car_brand_name_new") ? 0.00 : jsonObject.getDouble("car_brand_name_new"));
                            featureMap.put(videosIds.get(i), contentFeature);
                        } else {
                            ArrayList<Double> contentFeature = new ArrayList<>(userFeature);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            featureMap.put(videosIds.get(i), contentFeature);
                        }
                    }
                }
            } catch (Exception e) {
                String errorMsg = "redis获取内容特征失败, userId:【%s】".formatted(userId);
                log.error(errorMsg, e);
                e.printStackTrace();
            }
        }
        // 组合特征
        return featureMap;
    }


    /**
     * es查询脚本
     * <p>
     * ```java
     * {
     * "query": {
     * "bool": {
     * "must": [
     * {
     * "exists": {
     * "field": "car_brand_name_new"
     * }
     * },
     * {
     * "exists": {
     * "field": "car_model_name_new"
     * }
     * },
     * {
     * "bool": {
     * "must_not": [
     * {
     * "term": {
     * "car_brand_name_new.keyword": ""
     * }
     * }
     * ]
     * }
     * },
     * {
     * "bool": {
     * "must_not": [
     * {
     * "term": {
     * "car_model_name_new.keyword": ""
     * }
     * }
     * ]
     * }
     * }
     * ]
     * }
     * },
     * "sort": [
     * {
     * "score_rank": {
     * "order": "asc"
     * }
     * }
     * ],
     * "size": 100,
     * "_source": [
     * "content_id",
     * "post_type",
     * "score_rank"
     * ]
     * }
     * <p>
     * ```
     */
    public List<RecommendRespContentInfo> recommendHomePageContentFinal(String userId, Integer size) {
        try {
            // 构建 must 子句
            List<Map<String, Object>> mustClauses = new ArrayList<>();

            // 构建 car_brand_name_new 字段存在查询
            Map<String, Object> carBrandExists = new HashMap<>();
            carBrandExists.put("field", "car_brand_name_new");
            Map<String, Object> carBrandExistsQuery = new HashMap<>();
            carBrandExistsQuery.put("exists", carBrandExists);
            mustClauses.add(carBrandExistsQuery);

            // 构建 car_model_name_new 字段存在查询
            Map<String, Object> carModelExists = new HashMap<>();
            carModelExists.put("field", "car_model_name_new");
            Map<String, Object> carModelExistsQuery = new HashMap<>();
            carModelExistsQuery.put("exists", carModelExists);
            mustClauses.add(carModelExistsQuery);

            // 构建 car_brand_name_new 非空 must_not 子句
            List<Map<String, Object>> carBrandMustNotClauses = new ArrayList<>();
            Map<String, Object> carBrandTerm = new HashMap<>();
            carBrandTerm.put("car_brand_name_new.keyword", "");
            Map<String, Object> carBrandTermQuery = new HashMap<>();
            carBrandTermQuery.put("term", carBrandTerm);
            carBrandMustNotClauses.add(carBrandTermQuery);
            Map<String, Object> carBrandInnerBoolQuery = new HashMap<>();
            carBrandInnerBoolQuery.put("must_not", carBrandMustNotClauses);
            mustClauses.add(Collections.singletonMap("bool", carBrandInnerBoolQuery));

            // 构建 car_model_name_new 非空 must_not 子句
            List<Map<String, Object>> carModelMustNotClauses = new ArrayList<>();
            Map<String, Object> carModelTerm = new HashMap<>();
            carModelTerm.put("car_model_name_new.keyword", "");
            Map<String, Object> carModelTermQuery = new HashMap<>();
            carModelTermQuery.put("term", carModelTerm);
            carModelMustNotClauses.add(carModelTermQuery);
            Map<String, Object> carModelInnerBoolQuery = new HashMap<>();
            carModelInnerBoolQuery.put("must_not", carModelMustNotClauses);
            mustClauses.add(Collections.singletonMap("bool", carModelInnerBoolQuery));

            // 构建外部 bool 查询（must）
            Map<String, Object> outerBoolQuery = new HashMap<>();
            outerBoolQuery.put("must", mustClauses);

            // 构建主查询
            Map<String, Object> mainQuery = new HashMap<>();
            mainQuery.put("bool", outerBoolQuery);

            // 构建排序
            Map<String, Object> scoreRankSort = new HashMap<>();
            Map<String, String> scoreRankOrder = new HashMap<>();
            scoreRankOrder.put("order", "asc");
            scoreRankSort.put("score_rank", scoreRankOrder);
            List<Map<String, Object>> sortList = new ArrayList<>();
            sortList.add(scoreRankSort);

            // 构建 _source
            List<String> sourceFields = Arrays.asList("content_id", "post_type", "score_rank");

            // 构建完整的查询体
            Map<String, Object> queryBody = new HashMap<>();
            queryBody.put("query", mainQuery);
            queryBody.put("sort", sortList);
            queryBody.put("size", 100);
            queryBody.put("_source", sourceFields);

            // 将查询体转换为 JSON 字符串
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonBody = objectMapper.writeValueAsString(queryBody);

            // 创建请求
            Request request = new Request("GET", "/post_hot_data/_search");
            request.setJsonEntity(jsonBody);

            // 执行请求
            Response response = restClient.performRequest(request);
            JSONObject responseObj = new ObjectMapper().readValue(response.getEntity().getContent(), JSONObject.class);
            List<Object> jsonArray = responseObj.getJSONObject("hits").getJSONArray("hits").stream().toList();

            // 打乱顺序随机取10条
            List<Object> copyList = new ArrayList<>(jsonArray);
            Collections.shuffle(copyList);

            return copyList.stream()
                    .limit(size)
                    .map(o -> JSONObject.parseObject(JSON.toJSONString(o)).getJSONObject("_source"))
                    .map(o -> RecommendRespContentInfo.build(o.getString("content_id"), o.getString("post_type").equals("1") ? "graphic" : "video"))
                    .toList();

        } catch (Exception e) {
            String errorMsg = "最终最终兜底 => 兜底失败, 查询es失败, userId:【%s】".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 查询语句详情
     * ```java
     * {
     * "query": {
     * "bool": {
     * "must": [
     * {
     * "terms": {
     * "content_tag_id": [
     * // 这里需要替换为 contentTagIds 的实际值
     * ]
     * }
     * }
     * ]
     * }
     * },
     * "size": 100,
     * "sort": [
     * {
     * "hot_score": {
     * "order": "desc"
     * }
     * }
     * ],
     * "_source": [
     * "content_id",
     * "post_type",
     * "score_rank"
     * ]
     * }
     * ```
     *
     * @param size          返回条数
     * @param contentTagIds 频道标签id列表
     */
    public List<RecommendRespContentInfo> recommendOtherPageContentFinal(String userId, Integer size, List<String> contentTagIds) {
        try {
            // 构建 terms 查询
            Map<String, Object> termsQuery = new HashMap<>();
            termsQuery.put("content_tag_id", contentTagIds);
            Map<String, Object> termsClause = new HashMap<>();
            termsClause.put("terms", termsQuery);

            // 构建内部 bool 查询（must）
            List<Map<String, Object>> mustClauses = new ArrayList<>();
            mustClauses.add(termsClause);
            Map<String, Object> innerBoolQuery = new HashMap<>();
            innerBoolQuery.put("must", mustClauses);

            // 构建主查询
            Map<String, Object> mainQuery = new HashMap<>();
            mainQuery.put("bool", innerBoolQuery);

            // 构建排序
            Map<String, Object> sortField = new HashMap<>();
            sortField.put("order", "desc");
            List<Map<String, Object>> sort = new ArrayList<>();
            sort.add(Collections.singletonMap("hot_score", sortField));

            // 构建 _source
            List<String> sourceFields = Arrays.asList("content_id", "post_type", "score_rank");

            // 构建完整的查询体
            Map<String, Object> queryBody = new HashMap<>();
            queryBody.put("query", mainQuery);
            queryBody.put("size", 100);
            queryBody.put("sort", sort);
            queryBody.put("_source", sourceFields);

            // 将查询体转换为 JSON 字符串
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonBody = objectMapper.writeValueAsString(queryBody);

            // 创建请求
            Request request = new Request("GET", "/hot_t0t1_result_es/_search");
            request.setJsonEntity(jsonBody);

            // 执行请求
            Response response = restClient.performRequest(request);
            HttpEntity entity = response.getEntity();
            JSONObject responseObj = new ObjectMapper().readValue(entity.getContent(), JSONObject.class);
            List<Object> jsonArray = responseObj.getJSONObject("hits").getJSONArray("hits").stream().toList();

            // 打乱顺序随机取10条
            List<Object> copyList = new ArrayList<>(jsonArray);
            Collections.shuffle(copyList);
            return copyList.stream()
                    .limit(size)
                    .map(o -> JSONObject.parseObject(JSON.toJSONString(o)).getJSONObject("_source"))
                    .sorted(Comparator.comparing(jsonObject -> jsonObject.getDouble("score_rank")))
                    .map(o -> RecommendRespContentInfo.build(o.getString("content_id"), o.getString("post_type").equals("1") ? "graphic" : "video"))
                    .toList();

        } catch (Exception e) {
            String errorMsg = "最终最终兜底 => 频道页兜底失败, 查询es失败, userId:【%s】".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }
        return null;
    }


    public Set<String> queryT0FilterIds(String userId) {
        Set<String> filterList = new HashSet<>();
        Set<String> offlineRecommend = null;
        String offlineKey = Constants.REDIS_KEY_TODAY_CONTENT_OFFLINE + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        try {
            offlineRecommend = redisTemplate.opsForSet().members(offlineKey);
        } catch (Exception e) {
            String errorMsg = "查询redis下线内容失败, userId:【%s】".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }

        if (!CollectionUtils.isEmpty(offlineRecommend)) {
            filterList.addAll(offlineRecommend);
        }
        return filterList;
    }


    public List<RecommendRespContentInfo> channelOthColdRecall(
            String userId,
            List<String> collect,
            int pageSize,
            Set<String> contentRecommended,
            String contentRecommendedSetKey,
            List<String> t1FilterIds
    ) throws Exception {

        long l1 = System.currentTimeMillis();

        Set<String> t0Filters = queryT0FilterIds(userId);

        long l2 = System.currentTimeMillis();
        log.warn("频道标签冷启动查询t0过滤内容耗时：{}ms", l2 - l1);

        List<String> filters = new ArrayList<>(t1FilterIds);
        filters.addAll(t0Filters);
        filters.addAll(contentRecommended);
        List<String> distinctFilters = new ArrayList<>(filters.stream().distinct().toList());

        // 执行请求
        Response response = restClient.performRequest(buildColdRequestBody(collect));
        JSONObject responseObj = new ObjectMapper().readValue(response.getEntity().getContent(), JSONObject.class);
        JSONArray responses = responseObj.getJSONArray("responses");
        Map<String, List<JSONObject>> tagResp = new HashMap<>();
        for (int i = 0; i < collect.size(); i++) {
            String tagId = collect.get(i);
            JSONArray tagContentList = responses.getJSONObject(i).getJSONObject("hits").getJSONArray("hits");
            if (!CollectionUtils.isEmpty(tagContentList)) {
                List<JSONObject> source = tagContentList.stream()
                        .map(o -> JSON.parseObject(JSON.toJSONString(o)).getJSONObject("_source"))
                        .map(o -> JSON.parseObject(JSON.toJSONString(o)))
                        .filter(o -> !(distinctFilters.contains(o.getString("content_id"))))
                        .toList();
                tagResp.put(tagId, source);
            }
        }

        log.warn("冷启动频道页标签召回 => userId:【{}】, 根据标签匹配es结果, 标签数量:【{}】, 未去重结果总数量:【{}】", userId, tagResp.size(), tagResp.values().stream().mapToInt(List::size).sum());

        long l3 = System.currentTimeMillis();
        log.warn("频道标签冷启动查询es耗时：{}ms", l3 - l2);


        List<JSONObject> resultJSONList = new ArrayList<>();
        if (!org.springframework.util.CollectionUtils.isEmpty(tagResp)) {
            // 剩余所需数量
            int leftNum = pageSize;
            // 剩余标签数量
            int leftTagNum = tagResp.size();
            // 已保存内容
            Set<String> alreadyIds = new HashSet<>();

            // 按每个标签下内容数量进行升序
            List<Map.Entry<String, List<JSONObject>>> tagEntryList = new ArrayList<>(tagResp.entrySet());
            tagEntryList.sort(Comparator.comparingInt(entry -> entry.getValue().size()));
            // 遍历排序后的 List
            for (Map.Entry<String, List<JSONObject>> entry : tagEntryList) {
                // 当前tagId需从tagEntryList中取出topK条
                int topK = leftNum / leftTagNum;
                List<JSONObject> tagValues = entry.getValue();
                // 实际取出列表
                List<JSONObject> list = tagValues.stream().filter(o -> !alreadyIds.contains(o.getString("content_id"))).limit(topK).toList();
                alreadyIds.addAll(list.stream().map(o -> o.getString("content_id")).toList());
                resultJSONList.addAll(list);
                leftNum = leftNum - list.size();
                leftTagNum--;
            }

            log.warn("冷启动频道页标签召回 => userId:【{}】, 按标签排序topk取内容, 取出数据：【{}】条, 还需：【{}】条", userId, resultJSONList.size(), pageSize - resultJSONList.size());


            // 取数完之后如果数量还不够
            if (resultJSONList.size() < pageSize) {
                // 还差N条
                int num = pageSize - resultJSONList.size();
                distinctFilters.addAll(alreadyIds);

                List<String> filtersBak = new ArrayList<>(distinctFilters);
                filtersBak.removeAll(contentRecommended);
                filtersBak = filtersBak.stream().distinct().toList();

                // 执行请求
                Response responseAgain = restClient.performRequest(buildESRequestBodyAgain(num, collect, filtersBak));
                JSONObject responseObjAgain = new ObjectMapper().readValue(responseAgain.getEntity().getContent(), JSONObject.class);
                List<Object> jsonArray = responseObjAgain.getJSONObject("hits").getJSONArray("hits").stream().toList();
                if (!CollectionUtils.isEmpty(jsonArray)) {
                    List<JSONObject> source = jsonArray.stream()
                            .map(o -> JSONObject.parseObject(JSON.toJSONString(o)).getJSONObject("_source"))
                            .map(o -> JSONObject.parseObject(JSON.toJSONString(o)))
                            .toList();

                    resultJSONList.addAll(source);
                    log.warn("冷启动频道页标签召回 => userId:【{}】, 二次查询结果共计：【{}】条", userId, resultJSONList.size());
                }
                long l4 = System.currentTimeMillis();
                log.warn("频道标签冷启动二次查询es耗时：{}ms", l4 - l3);
            }
        } else {
            List<String> filtersBak = new ArrayList<>(distinctFilters);
            filtersBak.removeAll(contentRecommended);
            filtersBak = filtersBak.stream().distinct().toList();
            // 执行请求
            Response responseAgain = restClient.performRequest(buildESRequestBodyAgain(pageSize, collect, filtersBak));
            JSONObject responseObjAgain = new ObjectMapper().readValue(responseAgain.getEntity().getContent(), JSONObject.class);
            List<Object> jsonArray = responseObjAgain.getJSONObject("hits").getJSONArray("hits").stream().toList();
            if (!CollectionUtils.isEmpty(jsonArray)) {
                List<JSONObject> source = jsonArray.stream()
                        .map(o -> JSONObject.parseObject(JSON.toJSONString(o)).getJSONObject("_source"))
                        .map(o -> JSONObject.parseObject(JSON.toJSONString(o)))
                        .toList();

                resultJSONList.addAll(source);
                log.warn("冷启动频道页标签召回 => userId:【{}】, 二次查询结果共计：【{}】条", userId, resultJSONList.size());
            }

            long l4 = System.currentTimeMillis();
            log.warn("频道标签冷启动二次查询es耗时：{}ms", l4 - l3);
        }

        List<RecommendRespContentInfo> list = resultJSONList.stream().map(o -> RecommendRespContentInfo.build(o.getString("content_id"), o.getString("post_type").equals("1") ? "graphic" : "video")).toList();

        if (!CollectionUtils.isEmpty(list)) {
            List<String> currentRecommend = list.stream().map(RecommendRespContentInfo::getContentId).toList();
            asyncHandle.saveCacheRecommend(contentRecommendedSetKey, currentRecommend, userId);
        }
        log.warn("冷启动频道页标签召回 => userId:【{}】, 推荐结果数量:【{}】", userId, list.size());

        return list;
    }


    /**
     * es 查询条件：
     * <p>
     * ```java
     * GET hot_t0t1_result_es/_search
     * {
     * "size": 500,
     * "query": {
     * "bool": {
     * "must": [
     * {
     * "term": {
     * "content_tag_id.keyword": "292"
     * }
     * },
     * {
     * "range": {
     * "publish_time": {
     * "gte": "2024-03-17 00:00:00"
     * }
     * }
     * }
     * ]
     * }
     * },
     * "sort": [
     * {
     * "hot_score": {
     * "order": "desc"
     * }
     * }
     * ],
     * "_source": [
     * "content_id",
     * "post_type",
     * "score_rank",
     * "content_tag_id"
     * ]
     * }
     * ```
     */
    private static @NotNull Request buildColdRequestBody(List<String> collect) throws JsonProcessingException {
        StringBuilder requestBody = new StringBuilder();
        String nearOneYearDateTime = LocalDateTime.now().minusYears(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        for (String tagId : collect) {
            Map<String, Object> query = new HashMap<>();
            Map<String, Object> boolQuery = new HashMap<>();
            List<Object> mustList = new ArrayList<>();


            Map<String, Object> termsQuery = new HashMap<>();
            Map<String, Object> termsMap = new HashMap<>();
            termsQuery.put("content_tag_id.keyword", tagId);
            termsMap.put("term", termsQuery);
            mustList.add(termsMap);

            Map<String, Object> rangeQuery = new HashMap<>();
            Map<String, Object> rangeMap = new HashMap<>();
            Map<String, Object> publishTimeRange = new HashMap<>();
            publishTimeRange.put("gte", nearOneYearDateTime);
            rangeQuery.put("publish_time", publishTimeRange);
            rangeMap.put("range", rangeQuery);
            mustList.add(rangeMap);

            boolQuery.put("must", mustList);
            query.put("bool", boolQuery);

            Map<String, Object> sort = new HashMap<>();
            Map<String, Object> hotScoreSort = new HashMap<>();
            hotScoreSort.put("order", "desc");
            sort.put("hot_score", hotScoreSort);

            List<String> sourceFields = Arrays.asList("content_id", "post_type", "score_rank", "content_tag_id");

            Map<String, Object> searchRequest = new HashMap<>();
            searchRequest.put("size", 500);
            searchRequest.put("query", query);
            searchRequest.put("sort", List.of(sort));
            searchRequest.put("_source", sourceFields);
            requestBody.append("{\"index\":\"").append("hot_t0t1_result_es").append("\"}\n");
            requestBody.append(new ObjectMapper().writeValueAsString(searchRequest)).append("\n");
        }

        // 执行bulk请求
        Request request = new Request("POST", "/_msearch");
        request.setJsonEntity(requestBody.toString());
        return request;
    }


    /**
     * 查询es的请求
     * <p>
     * ```java
     * GET hot_t0t1_result_es/_search
     * {
     * "size": 500,
     * "query": {
     * "bool": {
     * "must": [
     * {
     * "range": {
     * "publish_time": {
     * "gte": "2024-03-17 00:00:00"
     * }
     * }
     * },{
     * "terms": {
     * "content_tag_id.keyword": [
     * "292",
     * "181"
     * ]
     * }
     * }
     * ],"must_not": [
     * {
     * "terms": {
     * "content_id": [
     * "2089731713937817699"
     * ]
     * }
     * }
     * ]
     * }
     * },
     * "sort": [
     * {
     * "hot_score": {
     * "order": "desc"            * 			}
     * }
     * ],
     * "_source": [
     * "content_id",
     * "post_type",
     * "score_rank",
     * "content_tag_id"
     * ]
     * }
     * ```
     */
    private static Request buildESRequestBodyAgain(int num, List<String> tags, List<String> filters) throws JsonProcessingException {
        Map<String, Object> searchRequest = new HashMap<>();
        String nearOneYearDateTime = LocalDateTime.now().minusYears(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        // 设置查询返回的文档数量
        searchRequest.put("size", num);

        // 构建 bool 查询
        Map<String, Object> bool = new HashMap<>();

        // 构建 must 子句
        List<Map<String, Object>> mustClauses = new ArrayList<>();

        // 构建 publish_time 范围查询
        Map<String, Object> publishTimeRange = new HashMap<>();
        Map<String, Object> rangeParams = new HashMap<>();
        rangeParams.put("gte", nearOneYearDateTime);
        publishTimeRange.put("publish_time", rangeParams);
        mustClauses.add(Map.of("range", publishTimeRange));

        // 构建 content_tag_id 条件查询
        Map<String, Object> contentTagIdTerms = new HashMap<>();
        contentTagIdTerms.put("content_tag_id.keyword", tags);
        mustClauses.add(Map.of("terms", contentTagIdTerms));

        bool.put("must", mustClauses);

        // 构建 must_not 子句
        List<Map<String, Object>> mustNotClauses = new ArrayList<>();
        Map<String, Object> contentIdTerms = new HashMap<>();
        contentIdTerms.put("content_id", filters);
        mustNotClauses.add(Map.of("terms", contentIdTerms));
        bool.put("must_not", mustNotClauses);

        // 将 bool 查询添加到主查询中
        searchRequest.put("query", Map.of("bool", bool));

        // 构建排序规则
        List<Map<String, Object>> sortRules = new ArrayList<>();
        Map<String, Object> hotScoreSort = new HashMap<>();
        Map<String, String> order = new HashMap<>();
        order.put("order", "desc");
        hotScoreSort.put("hot_score", order);
        sortRules.add(hotScoreSort);
        searchRequest.put("sort", sortRules);

        // 指定返回的字段
        List<String> sourceFields = Arrays.asList("content_id", "post_type", "score_rank", "content_tag_id");
        searchRequest.put("_source", sourceFields);


        // 将查询体转换为 JSON 字符串
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonBody = objectMapper.writeValueAsString(searchRequest);

        // 创建请求
        Request request = new Request("GET", "/hot_t0t1_result_es/_search");
        request.setJsonEntity(jsonBody);
        return request;
    }


    /**
     * 从Redis获取推荐结果
     *
     * @param userId    用户ID
     * @param channelId 渠道ID
     * @return 推荐内容列表
     */
    public List<RecommendRespContentInfo> queryFromRedis(String userId, String channelId, Integer pageSize) {
        String graphicKey = Constants.REDIS_KEY_RECOM_GRAPHIC_HOT_ZSET_PREFIX + channelId;

        List<RecommendRespContentInfo> result = new ArrayList<>();

        // 获取热门图文
        try {
            Set<String> rangeGraphic = redisTemplate.opsForZSet().range(graphicKey, 0, 250);
            if (!org.springframework.util.CollectionUtils.isEmpty(rangeGraphic)) {
                List<RecommendRespContentInfo> graphics = rangeGraphic.stream()
                        .filter(Objects::nonNull)
                        .map(JSON::parseObject)
                        .map(json -> RecommendRespContentInfo.build(json.getString("content_id"), "graphic"))
                        .toList();

                List<RecommendRespContentInfo> copyList = new ArrayList<>(graphics);
                Collections.shuffle(copyList);
                result.addAll(copyList.stream().limit(pageSize).toList());
            }
        } catch (Exception e) {
            log.warn("Redis查询热门图文失败, userId:【{}】", userId, e);
        }

        return result;
    }

    /**
     * 从MySQL获取推荐结果 (伪方法，由用户补充实现)
     *
     * @param channelId 渠道ID
     * @param pageSize  页大小
     * @return 推荐内容列表
     */
    public List<RecommendRespContentInfo> queryFromMysql(String channelId, Integer pageSize) {

        String sql = """
                SELECT
                	content_id
                FROM
                	dwd_gwm_app_graphic_hot_result_info_t0t1_df
                WHERE
                	dt = (
                	SELECT
                		MAX(dt)
                	FROM
                		dwd_gwm_app_graphic_hot_result_info_t0t1_df)
                	AND hr = (
                	SELECT
                		MAX(hr)
                	FROM
                		dwd_gwm_app_graphic_hot_result_info_t0t1_df
                	WHERE
                		dt = (
                		SELECT
                			MAX(dt)
                		FROM
                			dwd_gwm_app_graphic_hot_result_info_t0t1_df))
                	AND channel_ids = ?
                ORDER BY
                	score_rank
                LIMIT 250
                """;

        List<String> contentIds = jdbcTemplate.query(sql, (r, s) -> r.getString("content_id"), channelId);
        List<RecommendRespContentInfo> list = contentIds.stream().distinct().map(s -> RecommendRespContentInfo.build(s, "graphic")).toList();

        List<RecommendRespContentInfo> copyList = new ArrayList<>(list);
        Collections.shuffle(copyList);
        return copyList.stream().limit(pageSize).toList();
    }


}
