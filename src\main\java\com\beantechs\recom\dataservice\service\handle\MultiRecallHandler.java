package com.beantechs.recom.dataservice.service.handle;

import com.beantechs.recom.dataservice.config.ConstantsEnum;
import com.beantechs.recom.dataservice.config.RecommendContextHolder;
import com.beantechs.recom.dataservice.entity.bo.ContentScoreVo;
import com.beantechs.recom.dataservice.entity.bo.RecommendContext;
import com.beantechs.recom.dataservice.entity.resp.RecommendRespContentInfo;
import com.beantechs.recom.dataservice.service.handle.recall.MultiRecallFactory;
import com.beantechs.recom.dataservice.service.handle.recall.RecallHandleResult;
import com.beantechs.recom.dataservice.service.handle.recall.RecallStrategyInstance;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * 多路召回处理器
 */
@Component
@Slf4j
public class MultiRecallHandler extends ModuleHandle {

    @Override
    public void handle() {
        RecommendContext context = RecommendContextHolder.getContext();
        String userId = context.getMainReqParams().getUserId();
        String channelId = context.getMainReqParams().getChannelId();
        RecallStrategyInstance recallStrategy = context.getRecallStrategy();
        Set<String> filterSet = context.getFilterCollections().getTotalFilterCollections();
        RecallHandleResult recallResult = new RecallHandleResult();

        List<CompletableFuture<?>> futures = new ArrayList<>();

        // 用户协同过滤召回
        CompletableFuture<List<ContentScoreVo>> userGraphicFuture = null;
        if (recallStrategy.getUserCFGraphicRecall() != null) {
            userGraphicFuture = createAsyncTask(context, ConstantsEnum.RECALL_ASYNC_TASK_USER_GRAPHIC.getValue());
            futures.add(userGraphicFuture);
        }

        CompletableFuture<List<ContentScoreVo>> userVideoFuture = null;
        if (recallStrategy.getUserCFVideoRecall() != null) {
            userVideoFuture = createAsyncTask(context, ConstantsEnum.RECALL_ASYNC_TASK_USER_VIDEO.getValue());
            futures.add(userVideoFuture);
        }

        // 物品协同过滤召回
        CompletableFuture<List<ContentScoreVo>> itemGraphicFuture = null;
        if (recallStrategy.getItemCFGraphicRecall() != null) {
            itemGraphicFuture = createAsyncTask(context, ConstantsEnum.RECALL_ASYNC_TASK_ITEM_GRAPHIC.getValue());
            futures.add(itemGraphicFuture);
        }
        CompletableFuture<List<ContentScoreVo>> itemVideoFuture = null;
        if (recallStrategy.getItemCFVideoRecall() != null) {
            itemVideoFuture = createAsyncTask(context, ConstantsEnum.RECALL_ASYNC_TASK_ITEM_VIDEO.getValue());
            futures.add(itemVideoFuture);
        }

        // 内容召回
        CompletableFuture<List<ContentScoreVo>> contentGraphicFuture = null;
        if (recallStrategy.getContentGraphicRecall() != null) {
            contentGraphicFuture = createAsyncTask(context, ConstantsEnum.RECALL_ASYNC_TASK_CONTENT_GRAPHIC.getValue());
            futures.add(contentGraphicFuture);
        }

        CompletableFuture<List<ContentScoreVo>> contentVideoFuture = null;
        if (recallStrategy.getContentVideoRecall() != null) {
            contentVideoFuture = createAsyncTask(context, ConstantsEnum.RECALL_ASYNC_TASK_CONTENT_VIDEO.getValue());
            futures.add(contentVideoFuture);
        }

        // 兴趣标签召回
        CompletableFuture<List<ContentScoreVo>> interestGraphicFuture = null;
        if (recallStrategy.getInterestTagGraphicRecall() != null) {
            interestGraphicFuture = createAsyncTask(context, ConstantsEnum.RECALL_ASYNC_TASK_INTEREST_GRAPHIC.getValue());
            futures.add(interestGraphicFuture);
        }
        CompletableFuture<List<ContentScoreVo>> interestVideoFuture = null;
        if (recallStrategy.getInterestTagVideoRecall() != null) {
            interestVideoFuture = createAsyncTask(context, ConstantsEnum.RECALL_ASYNC_TASK_INTEREST_VIDEO.getValue());
            futures.add(interestVideoFuture);
        }

        // 用户关注召回
        CompletableFuture<List<ContentScoreVo>> userfollowGraphicFuture = null;
        if (recallStrategy.getUserFollowGraphicRecall() != null) {
            userfollowGraphicFuture = createAsyncTask(context, ConstantsEnum.RECALL_ASYNC_TASK_USERFOLLOW_GRAPHIC.getValue());
            futures.add(userfollowGraphicFuture);
        }
        CompletableFuture<List<ContentScoreVo>> userfollowVideoFuture = null;
        if (recallStrategy.getUserFollowVideoRecall() != null) {
            userfollowVideoFuture = createAsyncTask(context, ConstantsEnum.RECALL_ASYNC_TASK_USERFOLLOW_VIDEO.getValue());
            futures.add(userfollowVideoFuture);
        }

        // 热门内容召回
        CompletableFuture<List<ContentScoreVo>> hotGraphicFuture = null;
        if (recallStrategy.getHotGraphicRecall() != null) {
            hotGraphicFuture = createAsyncTask(context, ConstantsEnum.RECALL_ASYNC_TASK_HOT_GRAPHIC.getValue());
            futures.add(hotGraphicFuture);
        }

        CompletableFuture<List<ContentScoreVo>> hotVideoFuture = null;
        if (recallStrategy.getHotVideoRecall() != null) {
            hotVideoFuture = createAsyncTask(context, ConstantsEnum.RECALL_ASYNC_TASK_HOT_VIDEO.getValue());
            futures.add(hotVideoFuture);
        }

        // 用户理解召回
        CompletableFuture<List<ContentScoreVo>> understandGraphicFuture = null;
        if (recallStrategy.getUserUnderstandGraphicRecall() != null) {
            understandGraphicFuture = createAsyncTask(context, ConstantsEnum.RECALL_ASYNC_TASK_USERUNDERSTAND_GRAPHIC.getValue());
            futures.add(understandGraphicFuture);
        }

        CompletableFuture<List<ContentScoreVo>> understandVideoFuture = null;
        if (recallStrategy.getUserUnderstandVideoRecall() != null) {
            understandVideoFuture = createAsyncTask(context, ConstantsEnum.RECALL_ASYNC_TASK_USERUNDERSTAND_VIDEO.getValue());
            futures.add(understandVideoFuture);
        }

        // 频道标签召回
        CompletableFuture<List<RecommendRespContentInfo>> channelTagFuture = null;
        if (recallStrategy.getChannelTagRecall() != null) {
            channelTagFuture = createTagAsyncTask(context, ConstantsEnum.RECALL_ASYNC_TASK_CHANNEL_TAG.getValue());
            futures.add(channelTagFuture);
        }

        // 等待所有异步任务完成
        if (!futures.isEmpty()) {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        }
        if (userGraphicFuture != null) {
            try {
                filter(userGraphicFuture.get(), filterSet, recallResult, "userCfGraphic");
            } catch (Exception e) {
                String errorMsg = "多线程获取用户图文召回任务失败, userId:【%s】, channelId：【%s】".formatted(userId, channelId);
                log.error(errorMsg, e);
                e.printStackTrace();
            }
        }
        if (userVideoFuture != null) {
            try {
                filter(userVideoFuture.get(), filterSet, recallResult, "userCfVideo");
            } catch (Exception e) {
                String errorMsg = "多线程获取用户视频召回任务失败, userId:【%s】, channelId：【%s】".formatted(userId, channelId);
                log.error(errorMsg, e);
                e.printStackTrace();
            }
        }
        if (itemGraphicFuture != null) {
            try {
                filter(itemGraphicFuture.get(), filterSet, recallResult, "itemCfGraphic");
            } catch (Exception e) {
                String errorMsg = "多线程获取物料图文召回任务失败, userId:【%s】, channelId：【%s】".formatted(userId, channelId);
                log.error(errorMsg, e);
                e.printStackTrace();
            }
        }
        if (itemVideoFuture != null) {
            try {
                filter(itemVideoFuture.get(), filterSet, recallResult, "itemCfVideo");
            } catch (Exception e) {
                String errorMsg = "多线程获取物料视频召回任务失败, userId:【%s】, channelId：【%s】".formatted(userId, channelId);
                log.error(errorMsg, e);
                e.printStackTrace();
            }
        }
        if (contentGraphicFuture != null) {
            try {
                filter(contentGraphicFuture.get(), filterSet, recallResult, "contentGraphic");
            } catch (Exception e) {
                String errorMsg = "多线程获取内容图文召回任务失败, userId:【%s】, channelId：【%s】".formatted(userId, channelId);
                log.error(errorMsg, e);
                e.printStackTrace();
            }
        }
        if (contentVideoFuture != null) {
            try {
                filter(contentVideoFuture.get(), filterSet, recallResult, "contentVideo");
            } catch (Exception e) {
                String errorMsg = "多线程获取内容视频召回任务失败, userId:【%s】, channelId：【%s】".formatted(userId, channelId);
                log.error(errorMsg, e);
                e.printStackTrace();
            }
        }
        if (interestGraphicFuture != null) {
            try {
                filter(interestGraphicFuture.get(), filterSet, recallResult, "interestTagGraphic");
            } catch (Exception e) {
                String errorMsg = "多线程获取兴趣标签图文召回任务失败, userId:【%s】, channelId：【%s】".formatted(userId, channelId);
                log.error(errorMsg, e);
                e.printStackTrace();
            }
        }
        if (interestVideoFuture != null) {
            try {
                filter(interestVideoFuture.get(), filterSet, recallResult, "interestTagVideo");
            } catch (Exception e) {
                String errorMsg = "多线程获取兴趣标签视频召回任务失败, userId:【%s】, channelId：【%s】".formatted(userId, channelId);
                log.error(errorMsg, e);
                e.printStackTrace();
            }
        }
        if (userfollowGraphicFuture != null) {
            try {
                filter(userfollowGraphicFuture.get(), filterSet, recallResult, "userFollowGraphic");
            } catch (Exception e) {
                String errorMsg = "多线程获取用户关注图文召回任务失败, userId:【%s】, channelId：【%s】".formatted(userId, channelId);
                log.error(errorMsg, e);
                e.printStackTrace();
            }
        }
        if (userfollowVideoFuture != null) {
            try {
                filter(userfollowVideoFuture.get(), filterSet, recallResult, "userFollowVideo");
            } catch (Exception e) {
                String errorMsg = "多线程获取用户关注视频召回任务失败, userId:【%s】, channelId：【%s】".formatted(userId, channelId);
                log.error(errorMsg, e);
                e.printStackTrace();
            }
        }
        if (hotGraphicFuture != null) {
            try {
                filter(hotGraphicFuture.get(), filterSet, recallResult, "hotGraphic");
            } catch (Exception e) {
                String errorMsg = "多线程获取热门图文召回任务失败, userId:【%s】, channelId：【%s】".formatted(userId, channelId);
                log.error(errorMsg, e);
                e.printStackTrace();
            }
        }
        if (hotVideoFuture != null) {
            try {
                filter(hotVideoFuture.get(), filterSet, recallResult, "hotVideo");
            } catch (Exception e) {
                String errorMsg = "多线程获取热门视频召回任务失败, userId:【%s】, channelId：【%s】".formatted(userId, channelId);
                log.error(errorMsg, e);
                e.printStackTrace();
            }
        }
        if (understandGraphicFuture != null) {
            try {
                filter(understandGraphicFuture.get(), filterSet, recallResult, "userUnderstandGraphic");
            } catch (Exception e) {
                String errorMsg = "多线程获取用户理解图文召回任务失败, userId:【%s】, channelId：【%s】".formatted(userId, channelId);
                log.error(errorMsg, e);
                e.printStackTrace();
            }
        }
        if (understandVideoFuture != null) {
            try {
                filter(understandVideoFuture.get(), filterSet, recallResult, "userUnderstandVideo");
            } catch (Exception e) {
                String errorMsg = "多线程获取用户理解视频召回任务失败, userId:【%s】, channelId：【%s】".formatted(userId, channelId);
                log.error(errorMsg, e);
                e.printStackTrace();
            }
        }
        if (channelTagFuture != null) {
            try {
                filter(channelTagFuture.get(), filterSet, recallResult, "channelTag");
            } catch (Exception e) {
                String errorMsg = "多线程获取频道标签召回任务失败, userId:【%s】, channelId：【%s】".formatted(userId, channelId);
                log.error(errorMsg, e);
                e.printStackTrace();
            }
        }

        // 更新上下文中的结果
        context.setResultResult(recallResult);
        RecommendContextHolder.setContext(context);

        if (nextHandler != null) {
            nextHandler.handle();
        }
    }


    public static void filter(List<?> recall, Set<String> filterSet, RecallHandleResult recallResult, String type) {
        if (recall == null || recall.isEmpty()) {
            return;
        }

        if (recall.get(0) instanceof RecommendRespContentInfo) {
            recallResult.setChannelTagResult((List<RecommendRespContentInfo>) recall);
        } else {
            List<ContentScoreVo> list = (List<ContentScoreVo>) recall;
            List<ContentScoreVo> filterResult = list.stream().filter(o -> !filterSet.contains(o.getContentId())).toList();
            switch (type) {
                case "userCfGraphic":
                    recallResult.setUserCfGraphicResult(filterResult);
                    break;
                case "userCfVideo":
                    recallResult.setUserCfVideoResult(filterResult);
                    break;
                case "itemCfGraphic":
                    recallResult.setItemCfGraphicResult((filterResult));
                    break;
                case "itemCfVideo":
                    recallResult.setItemCfVideoResult(filterResult);
                    break;
                case "contentGraphic":
                    recallResult.setContentGraphicResult(filterResult);
                    break;
                case "contentVideo":
                    recallResult.setContentVideoResult((filterResult));
                    break;
                case "interestTagGraphic":
                    recallResult.setInterestTagGraphicResult(filterResult);
                    break;
                case "interestTagVideo":
                    recallResult.setInterestTagVideoResult(filterResult);
                    break;
                case "userFollowGraphic":
                    recallResult.setUserFollowGraphicResult(filterResult);
                    break;
                case "userFollowVideo":
                    recallResult.setUserFollowVideoResult(filterResult);
                    break;
                case "hotGraphic":
                    recallResult.setHotGraphicResult(filterResult);
                    break;
                case "hotVideo":
                    recallResult.setHotVideoResult(filterResult);
                    break;
                case "userUnderstandGraphic":
                    recallResult.setUserUnderstandGraphicResult(filterResult);
                    break;
                case "userUnderstandVideo":
                    recallResult.setUserUnderstandVideoResult(filterResult);
                    break;
            }
        }
    }


    private CompletableFuture<List<ContentScoreVo>> createAsyncTask(RecommendContext context, String type) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return MultiRecallFactory.getRecallStrategy(type).execute(context);
            } catch (Exception e) {
                String errorMsg = "多路召回任务失败, userId：【%s】, channelId：【%s】, 召回链路:【%s】".formatted(
                        context.getMainReqParams().getUserId(), context.getMainReqParams().getChannelId(), type);
                log.error(errorMsg, e);
                e.printStackTrace();
                return new ArrayList<>();
            }
        });
    }

    /**
     * 创建标签异步召回任务
     *
     * @param context 推荐上下文
     * @param type    召回策略类型
     * @return 异步任务
     */
    private CompletableFuture<List<RecommendRespContentInfo>> createTagAsyncTask(RecommendContext context, String type) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return MultiRecallFactory.getTagRecallStrategy(type).execute(context);
            } catch (Exception e) {
                String errorMsg = "多路召回任务失败, userId：【%s】, channelId：【%s】, 召回链路:【%s】".formatted(
                        context.getMainReqParams().getUserId(), context.getMainReqParams().getChannelId(), type);
                log.error(errorMsg, e);
                e.printStackTrace();
                return new ArrayList<>();
            }
        });
    }

}
