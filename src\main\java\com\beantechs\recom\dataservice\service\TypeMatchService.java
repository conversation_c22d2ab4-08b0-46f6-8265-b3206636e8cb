package com.beantechs.recom.dataservice.service;

import com.beantechs.recom.dataservice.service.recall.RecallHandle;
import com.beantechs.recom.dataservice.service.recall.RecallType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class TypeMatchService {


    private Map<RecallType, RecallHandle> recallTypeHandleMap;


    /**
     * 自动注入召回处理类
     */
    @Autowired
    private void setRecallTypeHandleMap(List<RecallHandle> recallHandles) {
        recallTypeHandleMap = recallHandles.stream().collect(Collectors.toMap(recallHandle -> AnnotationUtils.findAnnotation(recallHandle.getClass(), RecallType.class), v -> v, (v1, v2) -> v1));
    }


    public Map<RecallType, RecallHandle> getRecallTypeHandleMapInstance() {
        return recallTypeHandleMap;
    }

}
