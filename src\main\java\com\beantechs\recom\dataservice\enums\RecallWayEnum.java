package com.beantechs.recom.dataservice.enums;

import com.beantechs.recom.dataservice.config.Constants;
import lombok.Getter;

/**
 * 召回方式枚举
 */
@Getter
public enum RecallWayEnum {

    USER_CF(Constants.RECALL_WAY_USER_CF, "用户协同过滤召回"),
    ITEM_CF(Constants.RECALL_WAY_ITEM_CF, "物品协同过滤召回"),
    CONTENT(Constants.RECALL_WAY_CONTENT, "内容召回"),
    USER_INTEREST(Constants.RECALL_WAY_USER_INTEREST, "用户兴趣召回"),
    USER_FOLLOW(Constants.RECALL_WAY_USER_FOLLOW, "用户关注召回"),
    HOT(Constants.RECALL_WAY_HOT, "热门召回");

    private final String code;
    private final String desc;

    RecallWayEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RecallWayEnum getByCode(String code) {
        for (RecallWayEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
} 