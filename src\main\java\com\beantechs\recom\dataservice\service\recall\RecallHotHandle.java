package com.beantechs.recom.dataservice.service.recall;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beantechs.recom.dataservice.config.Constants;
import com.beantechs.recom.dataservice.entity.bo.ContentScoreVo;
import com.beantechs.recom.dataservice.entity.bo.MultiRecallResultBo;
import com.beantechs.recom.dataservice.service.CommonHandel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;

/**
 * 热门内容召回处理器
 * 负责从热门内容池中召回图文和视频内容
 * 支持按评分排序的热门内容获取
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Slf4j
@Service
@RecallType(type = Constants.RECALL_WAY_HOT)
public class RecallHotHandle extends CommonHandel implements RecallHandle {

    private final RedisTemplate<String, String> redisTemplate;
    // 图文内容的召回数量限制
    public static final int GRAPHIC_LIMIT = 100;
    // 视频内容的召回数量限制
    public static final int VIDEO_LIMIT = 100;

    public RecallHotHandle(RedisTemplate<String, String> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    /**
     * 处理热门内容召回请求
     * 同时获取热门图文和视频内容
     *
     * @param userId    用户ID（热门召回不依赖用户ID）
     * @param channelId 渠道ID
     * @return 多路召回结果
     */
    @Override
    public MultiRecallResultBo handle(String userId, String channelId) {
        long l = System.currentTimeMillis();
        String graphicKey = Constants.REDIS_KEY_RECOM_GRAPHIC_HOT_ZSET_PREFIX + channelId;
        Set<String> rangeGraphic;
        List<ContentScoreVo> hotGraphics = List.of();
        try {
            rangeGraphic = redisTemplate.opsForZSet().range(graphicKey, 0, GRAPHIC_LIMIT);
            if (!CollectionUtils.isEmpty(rangeGraphic)) {
                hotGraphics = rangeGraphic.stream().map(o -> {
                    JSONObject json = JSON.parseObject(o);
                    String score = json.getString("hot_score");
                    String contentId = json.getString("content_id");
                    return ContentScoreVo.of(contentId, score);
                }).toList();
            }
        } catch (Exception e) {
            String errorMsg = "热门召回结果 => Redis查询热门图文异常 === userId:%s".formatted(userId);
            log.error(errorMsg, e);
                e.printStackTrace();
        }

        String videoKey = Constants.REDIS_KEY_RECOM_VIDEO_HOT_ZSET_PREFIX + channelId;
        Set<String> rangeVideo;
        List<ContentScoreVo> hotVideos = List.of();
        try {
            rangeVideo = redisTemplate.opsForZSet().range(videoKey, 0, GRAPHIC_LIMIT);
            if (!CollectionUtils.isEmpty(rangeVideo)) {
                hotVideos = rangeVideo.stream().map(o -> {
                    JSONObject json = JSON.parseObject(o);
                    String score = json.getString("hot_score");
                    String contentId = json.getString("content_id");
                    return ContentScoreVo.of(contentId, score);
                }).toList();
            }
        } catch (Exception e) {
            String errorMsg = "热门召回结果 => Redis查询热门视频异常 === userId:%s".formatted(userId);
            log.error(errorMsg, e);
                e.printStackTrace();
        }

        // 封装召回结果
        MultiRecallResultBo result = new MultiRecallResultBo();
        result.setHotGraphics(hotGraphics);
        result.setHotVideos(hotVideos);

        log.warn("热门召回结果 => userId:【{}】, 图文:【{}】条, 视频:【{}】条, 总耗时:【{}】ms", userId, hotGraphics.size(), hotVideos.size(), System.currentTimeMillis() - l);
        return result;
    }

}