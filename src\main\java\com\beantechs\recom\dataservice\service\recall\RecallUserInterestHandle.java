package com.beantechs.recom.dataservice.service.recall;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beantechs.recom.dataservice.config.Constants;
import com.beantechs.recom.dataservice.entity.bo.ContentScoreVo;
import com.beantechs.recom.dataservice.entity.bo.MultiRecallResultBo;
import com.beantechs.recom.dataservice.entity.bo.T0UserLabel;
import com.beantechs.recom.dataservice.service.CommonHandel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 用户兴趣标签召回处理器
 * 基于用户选择的兴趣标签进行内容召回
 * 支持价格区间、驾驶模式、用车场景、车型级别等多维度匹配
 * 当无匹配结果时会降级到热门召回
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Slf4j
@Service
@RecallType(type = Constants.RECALL_WAY_USER_INTEREST)
public class RecallUserInterestHandle extends CommonHandel implements RecallHandle {

    // 用于数据库操作的JdbcTemplate
    private final JdbcTemplate jdbcTemplate;

    private final RedisTemplate<String, String> redisTemplate;

    public RecallUserInterestHandle(JdbcTemplate jdbcTemplate, RedisTemplate<String, String> redisTemplate) {
        this.jdbcTemplate = jdbcTemplate;
        this.redisTemplate = redisTemplate;
    }

    /**
     * 处理用户兴趣标签召回请求
     *
     * @param userId    用户ID
     * @param channelId 渠道ID
     * @return 多路召回结果
     */
    @Override
    public MultiRecallResultBo handle(String userId, String channelId) {
        long l = System.currentTimeMillis();

        MultiRecallResultBo result = new MultiRecallResultBo();

        String userTodaySelectLabel = null;
        String userTodaySelectLabelKey = Constants.REDIS_KEY_RECOM_USER_SELECT_INTEREST_LABEL + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + ":" + userId;
        try {
            userTodaySelectLabel = redisTemplate.opsForValue().get(userTodaySelectLabelKey);
        } catch (Exception e) {
            String errorMsg = "兴趣标签召回 => userId:%s, 获取用户今日选择标签失败: redis key:%s".formatted(userId, userTodaySelectLabelKey);
            log.error(errorMsg, e);
            e.printStackTrace();
        }

        List<ContentScoreVo> graphicList = List.of();
        List<ContentScoreVo> videoList = List.of();
        if (StringUtils.hasText(userTodaySelectLabel)) {
            T0UserLabel p = JSON.parseObject(userTodaySelectLabel, T0UserLabel.class);
            List<ContentScoreVo> graphicScoreVoList = recallByEvent(userId, channelId, p, "graphic");
            List<ContentScoreVo> videoScoreVoList = recallByEvent(userId, channelId, p, "video");

            graphicList = graphicScoreVoList.subList(0, Math.min(graphicScoreVoList.size(), 100));
            videoList = videoScoreVoList.subList(0, Math.min(videoScoreVoList.size(), 100));

            result.setUserInterestGraphics(graphicList);
            result.setUserInterestVideos(videoList);
        } else {
            log.warn("兴趣标签召回-用户未选择标签");
        }

        log.warn("实时兴趣标签召回结果 => userId:【{}】, 图文:【{}】条, 视频:【{}】条, 总耗时:【{}】ms", userId, graphicList.size(), videoList.size(), System.currentTimeMillis() - l);
        return result;
    }


    public MultiRecallResultBo notExistOnlineInterestRecallHandle(String userId, String channelId) {
        long l = System.currentTimeMillis();
        MultiRecallResultBo result = new MultiRecallResultBo();

        String selectTagsGraphicKey = Constants.REDIS_KEY_RECOM_GRAPHIC_SELECTTAGS_PREFIX + channelId + ":" + userId;
        String selectTagsVideoKey = Constants.REDIS_KEY_RECOM_VIDEO_SELECTTAGS_PREFIX + channelId + ":" + userId;
        String recallGraphicSqlResult = null;
        String recallVideoSqlResult = null;
        try {
            recallGraphicSqlResult = (String) redisTemplate.opsForValue().get(selectTagsGraphicKey);
        } catch (Exception e) {
            log.warn("离线兴趣标签召回-redis获取图文为空");
        }

        try {
            recallVideoSqlResult = (String) redisTemplate.opsForValue().get(selectTagsVideoKey);
        } catch (Exception e) {
            log.warn("离线兴趣标签召回-redis获取视频为空");
        }

        List<ContentScoreVo> graphicScoreVoList = parseContentScoreVoRecallResult(recallGraphicSqlResult);
        List<ContentScoreVo> videoScoreVoList = parseContentScoreVoRecallResult(recallVideoSqlResult);

        List<ContentScoreVo> interestGraphicList = List.of();
        List<ContentScoreVo> hotGraphics = List.of();
        if (!CollectionUtils.isEmpty(graphicScoreVoList)) {
            interestGraphicList = graphicScoreVoList.subList(0, Math.min(graphicScoreVoList.size(), 100));
            result.setUserInterestGraphics(interestGraphicList);
        } else {
            String graphicKey = Constants.REDIS_KEY_RECOM_GRAPHIC_HOT_ZSET_PREFIX + channelId;
            Set<String> rangeGraphic;
            try {
                rangeGraphic = redisTemplate.opsForZSet().range(graphicKey, 0, RecallHotHandle.GRAPHIC_LIMIT);
                if (!CollectionUtils.isEmpty(rangeGraphic)) {
                    hotGraphics = rangeGraphic.stream().map(o -> {
                        JSONObject json = JSON.parseObject(o);
                        String score = json.getString("hot_score");
                        String contentId = json.getString("content_id");
                        return ContentScoreVo.of(contentId, score);
                    }).toList();
                }
            } catch (Exception e) {
                log.warn("离线兴趣标签召回-图文离线召回无结果");
            }
            result.setHotGraphics(hotGraphics);
        }

        List<ContentScoreVo> interestVideoList = List.of();
        List<ContentScoreVo> hotVideo = List.of();
        if (!CollectionUtils.isEmpty(videoScoreVoList)) {
            interestVideoList = videoScoreVoList.subList(0, Math.min(videoScoreVoList.size(), 100));
            result.setUserInterestVideos(interestVideoList);
        } else {
            String videoKey = Constants.REDIS_KEY_RECOM_VIDEO_HOT_ZSET_PREFIX + channelId;
            Set<String> rangeVideo;
            try {
                rangeVideo = redisTemplate.opsForZSet().range(videoKey, 0, RecallHotHandle.VIDEO_LIMIT);
                if (!CollectionUtils.isEmpty(rangeVideo)) {
                    hotVideo = rangeVideo.stream().map(o -> {
                        JSONObject json = JSON.parseObject(o);
                        String score = json.getString("hot_score");
                        String contentId = json.getString("content_id");
                        return ContentScoreVo.of(contentId, score);
                    }).limit(100).toList();
                }
            } catch (Exception e) {
                log.warn("离线兴趣标签召回-视频离线召回无结果");
            }
            result.setHotVideos(hotVideo);
        }

        log.warn("离线兴趣标签召回结果 => userId:【{}】, 图文:【{}】条, 视频:【{}】条, 总耗时:【{}】ms", userId, interestGraphicList.size(), interestVideoList.size(), System.currentTimeMillis() - l);
        log.warn("离线兴趣标签召回热门补充召回结果 => userId:【{}】, 图文:【{}】条, 视频:【{}】条, 总耗时:【{}】ms", userId, hotGraphics.size(), hotVideo.size(), System.currentTimeMillis() - l);
        return result;
    }

    private List<ContentScoreVo> recallByEvent(String userId, String channelId, T0UserLabel p, String type) {
        Set<ContentScoreVo> priceRangeList = new HashSet<>();
        Set<ContentScoreVo> drivingModeList = new HashSet<>();
        Set<ContentScoreVo> vehicleSceneList = new HashSet<>();
        Set<ContentScoreVo> modelLevelList = new HashSet<>();
        Set<ContentScoreVo> allList = new HashSet<>();

        List<JSONObject> objs = null;
        String sql = null;
        try {
            if (type.equals("graphic")) {
                sql = graphicInterestQuerySQL(
                        channelId,
                        p.getPriceRange(),
                        p.getDrivingMode(),
                        p.getVehicleScene(),
                        p.getModelLevel()
                );
                objs = jdbcTemplate.query(sql, (r, s) -> {
                            JSONObject json = new JSONObject();
                            json.put("param_value", r.getString("param_value"));
                            json.put("content_id", r.getString("content_id"));
                            json.put("hot_score", r.getString("hot_score"));
                            return json;
                        }
                );
            } else {
                sql = videoInterestQuerySQL(
                        channelId,
                        p.getPriceRange(),
                        p.getDrivingMode(),
                        p.getVehicleScene(),
                        p.getModelLevel()
                );

                objs = jdbcTemplate.query(sql, (r, s) -> {
                            JSONObject json = new JSONObject();
                            json.put("param_value", r.getString("param_value"));
                            json.put("content_id", r.getString("content_id"));
                            json.put("hot_score", r.getString("hot_score"));
                            return json;
                        }
                );
            }

        } catch (Exception e) {
            String errorMsg = "兴趣标签召回 => userId:%s, 查询原始数据结果失败, SQL:%s".formatted(userId, sql);
            log.error(errorMsg, e);
            e.printStackTrace();
        }

        if (!CollectionUtils.isEmpty(objs)) {
            objs.forEach(json -> {
                ContentScoreVo vo = new ContentScoreVo();
                vo.setContentId(json.getString("content_id"));
                vo.setScore(json.getString("hot_score"));
                if (json.getString("param_value").equals(p.getPriceRange())) {
                    priceRangeList.add(vo);
                }

                if (json.getString("param_value").equals(p.getDrivingMode())) {
                    drivingModeList.add(vo);
                }

                if (json.getString("param_value").equals(p.getVehicleScene())) {
                    vehicleSceneList.add(vo);
                }

                if (json.getString("param_value").equals(p.getModelLevel())) {
                    modelLevelList.add(vo);
                }

                if (json.containsKey("all")) {
                    allList.add(vo);
                }
            });
        }


        int labelNum = 0;
        if (StringUtils.hasText(p.getPriceRange())) {
            labelNum++;
        }
        if (StringUtils.hasText(p.getDrivingMode())) {
            labelNum++;
        }
        if (StringUtils.hasText(p.getVehicleScene())) {
            labelNum++;
        }
        if (StringUtils.hasText(p.getModelLevel())) {
            labelNum++;
        }

        List<ContentScoreVo> contentScoreVoList = new ArrayList<>();
        if (labelNum == 0) {
            contentScoreVoList.addAll(allList);
        } else {
            // TODO 加注释
            int resultNum = 100 / (labelNum + 1);
            if (priceRangeList.size() > resultNum) {
                contentScoreVoList.addAll(priceRangeList.stream().limit(resultNum).toList());
            } else {
                priceRangeList.addAll(allList.stream().filter(o -> !priceRangeList.contains(o)).limit(resultNum - priceRangeList.size()).toList());
                contentScoreVoList.addAll(priceRangeList);
            }
            if (drivingModeList.size() > resultNum) {
                contentScoreVoList.addAll(drivingModeList.stream().filter(o -> !contentScoreVoList.contains(o)).limit(resultNum).toList());
            } else {
                drivingModeList.addAll(allList.stream().filter(o -> !drivingModeList.contains(o) && !contentScoreVoList.contains(o)).limit(resultNum - drivingModeList.size()).toList());
                contentScoreVoList.addAll(drivingModeList);
            }

            if (vehicleSceneList.size() > resultNum) {
                contentScoreVoList.addAll(vehicleSceneList.stream().filter(o -> !contentScoreVoList.contains(o)).limit(resultNum).toList());
            } else {
                vehicleSceneList.addAll(allList.stream().filter(o -> !vehicleSceneList.contains(o) && !contentScoreVoList.contains(o)).limit(resultNum - vehicleSceneList.size()).toList());
                contentScoreVoList.addAll(vehicleSceneList);
            }

            if (modelLevelList.size() > resultNum) {
                contentScoreVoList.addAll(modelLevelList.stream().filter(o -> !contentScoreVoList.contains(o)).limit(resultNum).toList());
            } else {
                modelLevelList.addAll(allList.stream().filter(o -> !modelLevelList.contains(o) && !contentScoreVoList.contains(o)).limit(resultNum - modelLevelList.size()).toList());
                contentScoreVoList.addAll(modelLevelList);
            }
        }

        return contentScoreVoList;
    }


    private static String videoInterestQuerySQL(String channelId, String priceRange, String drivingMode, String vehicleScene, String modelLevel) {
        return """
                (
                SELECT
                    post_price_range as param_value,
                    hot_score,
                    content_id
                FROM
                    dwd_gwm_app_video_hot_result_info_t0t1_df
                WHERE
                    post_price_range = '%s'
                AND channel_ids = '%s'
                AND hdfs_create_hr = (
                    SELECT MAX(hdfs_create_hr)
                    FROM dwd_gwm_app_video_hot_result_info_t0t1_df
                )
                ORDER BY
                    hot_score DESC
                LIMIT 100
                )
                UNION ALL
                (
                SELECT
                        post_drive_mode as param_value,
                        hot_score,
                        content_id
                FROM
                dwd_gwm_app_video_hot_result_info_t0t1_df
                WHERE
                post_drive_mode = '%s'
                AND channel_ids = '%s'
                AND hdfs_create_hr = (
                    SELECT MAX(hdfs_create_hr)
                    FROM dwd_gwm_app_video_hot_result_info_t0t1_df
                )
                ORDER BY
                hot_score DESC
                LIMIT 100
                )
                UNION ALL
                (
                SELECT
                        post_car_scene as param_value,
                        hot_score,
                        content_id
                FROM
                dwd_gwm_app_video_hot_result_info_t0t1_df
                WHERE
                post_car_scene = '%s'
                AND channel_ids = '%s'
                AND hdfs_create_hr = (
                    SELECT MAX(hdfs_create_hr)
                    FROM dwd_gwm_app_video_hot_result_info_t0t1_df
                )
                ORDER BY
                hot_score DESC
                LIMIT 100
                )
                UNION ALL
                (
                SELECT
                        post_car_level as param_value,
                        hot_score,
                        content_id
                FROM
                dwd_gwm_app_video_hot_result_info_t0t1_df
                WHERE
                post_car_level = '%s'
                AND channel_ids = '%s'
                AND hdfs_create_hr = (
                    SELECT MAX(hdfs_create_hr)
                    FROM dwd_gwm_app_video_hot_result_info_t0t1_df
                )
                ORDER BY
                hot_score DESC
                LIMIT 100
                )
                UNION ALL
                (
                SELECT
                        'all' as param_value,
                        hot_score,
                        content_id
                FROM
                dwd_gwm_app_video_hot_result_info_t0t1_df
                WHERE hdfs_create_hr = (
                    SELECT MAX(hdfs_create_hr)
                    FROM dwd_gwm_app_video_hot_result_info_t0t1_df
                )
                ORDER BY
                hot_score DESC
                LIMIT 100
                )
                """.formatted(priceRange, channelId, drivingMode, channelId, vehicleScene, channelId, modelLevel, channelId);
    }


    private static String graphicInterestQuerySQL(String channelId, String priceRange, String drivingMode, String vehicleScene, String modelLevel) {
        return """
                (
                SELECT
                    post_price_range as param_value,
                    hot_score,
                    content_id
                FROM
                    dwd_gwm_app_graphic_hot_result_info_t0t1_df
                WHERE
                    post_price_range = '%s'
                AND channel_ids = '%s'
                AND hdfs_create_hr = (
                    SELECT MAX(hdfs_create_hr)
                    FROM dwd_gwm_app_graphic_hot_result_info_t0t1_df
                )
                ORDER BY
                    hot_score DESC
                LIMIT 100
                )
                UNION ALL
                (
                SELECT
                        post_drive_mode as param_value,
                        hot_score,
                        content_id
                FROM
                dwd_gwm_app_graphic_hot_result_info_t0t1_df
                WHERE
                post_drive_mode = '%s'
                AND channel_ids = '%s'
                AND hdfs_create_hr = (
                    SELECT MAX(hdfs_create_hr)
                    FROM dwd_gwm_app_graphic_hot_result_info_t0t1_df
                )
                ORDER BY
                hot_score DESC
                LIMIT 100
                )
                UNION ALL
                (
                SELECT
                        post_car_scene as param_value,
                        hot_score,
                        content_id
                FROM
                dwd_gwm_app_graphic_hot_result_info_t0t1_df
                WHERE
                post_car_scene = '%s'
                AND channel_ids = '%s'
                AND hdfs_create_hr = (
                    SELECT MAX(hdfs_create_hr)
                    FROM dwd_gwm_app_graphic_hot_result_info_t0t1_df
                )
                ORDER BY
                hot_score DESC
                LIMIT 100
                )
                UNION ALL
                (
                SELECT
                        post_car_level as param_value,
                        hot_score,
                        content_id
                FROM
                dwd_gwm_app_graphic_hot_result_info_t0t1_df
                WHERE
                post_car_level = '%s'
                AND channel_ids = '%s'
                AND hdfs_create_hr = (
                    SELECT MAX(hdfs_create_hr)
                    FROM dwd_gwm_app_graphic_hot_result_info_t0t1_df
                )
                ORDER BY
                hot_score DESC
                LIMIT 100
                )
                UNION ALL
                (
                SELECT
                        'all' as param_value,
                        hot_score,
                        content_id
                FROM
                dwd_gwm_app_graphic_hot_result_info_t0t1_df
                WHERE hdfs_create_hr = (
                    SELECT MAX(hdfs_create_hr)
                    FROM dwd_gwm_app_graphic_hot_result_info_t0t1_df
                )
                ORDER BY
                hot_score DESC
                LIMIT 100
                )
                """.formatted(priceRange, channelId, drivingMode, channelId, vehicleScene, channelId, modelLevel, channelId);
    }

}
