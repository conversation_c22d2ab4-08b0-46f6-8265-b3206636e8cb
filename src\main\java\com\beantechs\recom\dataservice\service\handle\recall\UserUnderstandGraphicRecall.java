package com.beantechs.recom.dataservice.service.handle.recall;

import cn.hutool.extra.spring.SpringUtil;
import com.beantechs.recom.dataservice.config.Constants;
import com.beantechs.recom.dataservice.entity.bo.ContentScoreVo;
import com.beantechs.recom.dataservice.entity.bo.RecommendContext;
import com.beantechs.recom.dataservice.service.CommonHandel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@MultiRecallType(type = "user-understand-graphic")
public class UserUnderstandGraphicRecall implements RecallStrategy {
    @Override
    public List<ContentScoreVo> execute(RecommendContext context) {
        String userId = context.getMainReqParams().getUserId();
        String channelId = context.getMainReqParams().getChannelId();
        RedisTemplate<String, String> redisTemplate = SpringUtil.getBean("stringRedisTemplate");


        String key = Constants.REDIS_KEY_RECOM_GRAPHIC_USER_UNDERSTAND_PREFIX + channelId;
        List<ContentScoreVo> recall = new ArrayList<>();
        try {
            Object value = redisTemplate.opsForHash().get(key, userId);
            if (!Objects.isNull(value)) {
                String s = String.valueOf(value);
                if (StringUtils.hasText(s)) {
                    recall = CommonHandel.parseContentScoreVoRecallResult(s);
                }
            }
        } catch (Exception e) {
            String errorMsg = "用户理解图文召回 => 查询用户理解图文召回redis异常, userId:【%s】".formatted(userId);
            log.error(errorMsg, e);
                e.printStackTrace();
        }
        return recall;
    }
}
