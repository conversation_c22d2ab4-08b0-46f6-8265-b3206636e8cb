package com.beantechs.recom.dataservice.entity.bo;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 选择车型标签事件
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class T0UserLabel extends UserActionEventBo {

    /**
     * 价格区间
     */
    private String priceRange;

    /**
     * 驱动方式
     */
    private String drivingMode;


    /**
     * 用车场景
     */
    private String vehicleScene;


    /**
     * 车型级别
     */
    private String modelLevel;
} 