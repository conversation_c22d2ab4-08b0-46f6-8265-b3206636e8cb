package com.beantechs.recom.dataservice.service;


import com.alibaba.fastjson.JSON;
import com.beantechs.recom.dataservice.config.DIYException;
import com.beantechs.recom.dataservice.config.RecommendContextHolder;
import com.beantechs.recom.dataservice.entity.bo.RecommendContext;
import com.beantechs.recom.dataservice.entity.req.RecommendReq;
import com.beantechs.recom.dataservice.entity.resp.RecommendResp;
import com.beantechs.recom.dataservice.service.handle.recall.RecallStrategyInstance;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.RestClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
public class RecommendNewService extends ConstantsService {

    public RecommendNewService(RedisTemplate<String, String> redisTemplate, RestClient restClient) {
        super(redisTemplate, restClient);
    }


    public RecommendResp handle(RecommendReq requestBody) throws Exception {
        try {
            // 全局上下文管理器初始化
            RecommendContext.init(requestBody);

            // 过滤集合初始化
            filterCollectionsInit();

            // 频道页配置信息初始化
            channelConfigInit();

            // 推荐流程链路初始化
            recommendStrategyInit();

            // 召回处理器初始化
            RecallStrategyInstance.init();

            RecommendContextHolder.getContext().getRecommendStrategy().execute();

            if (requestBody.getMainReqParams().getShowDetail() == 1) {
                RecommendContextHolder.setRecomDetail();
            }

            if (CollectionUtils.isEmpty(RecommendContextHolder.getContext().getResp())) {
                log.warn("无推荐结果, 主动抛出异常走兜底路线");
                throw new DIYException("无推荐结果, 主动抛出异常走兜底路线");
            }

        } catch (Exception e) {
            String errorMsg = "推荐链路流程失败, 入参:【%s】, 上下文管理器详情：【%s】".formatted(JSON.toJSONString(requestBody), JSON.toJSONString(RecommendContextHolder.getContext()));
            throw new DIYException(errorMsg, e);
        }

        return RecommendContextHolder.getContext().getRecommendResp();
    }


}
