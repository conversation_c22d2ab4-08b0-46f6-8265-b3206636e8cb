variables:
  APP_NAME: "app-merge-recommend-service"
  APP_NAME_PRE: "app-merge-recommend-pre-service"
  APP_ROUTER_NAME: "app-merge-recommend-router-service"
  APP_ROUTER_PRE_NAME: "app-merge-recommend-router-pre-service"
  APP_VERSION: "v1.0"

# 本次构建的阶段
stages:
  - package
  - build-image
  - deploy
  - done

maven_package:
  stage: package
  image: maven:3.8-openjdk-17
  tags:
    - gwm-app
  only:
    - develop
    - master
  before_script:
    - echo "===============  开始编译打包任务  ==============="
    - rm -rf ${ARTIFACTS_PATH} && mkdir ${ARTIFACTS_PATH}
  script:
    - mvn -settings /home/<USER>/.m2/settings.xml clean package -DskipTests
  after_script:
    - cp Dockerfile ${ARTIFACTS_PATH}
    - cp target/${APP_NAME}.jar ${ARTIFACTS_PATH}


maven_package_pre:
  stage: package
  image: maven:3.8-openjdk-17
  tags:
    - gwm-app
  only:
    - premaster
  before_script:
    - echo "===============  开始编译打包任务  ==============="
    - rm -rf ${ARTIFACTS_PATH} && mkdir ${ARTIFACTS_PATH}
  script:
    - mvn -settings /home/<USER>/.m2/settings.xml clean package -DskipTests
  after_script:
    - cp Dockerfile ${ARTIFACTS_PATH}
    - mv target/${APP_NAME}.jar target/${APP_NAME_PRE}.jar
    - cp target/${APP_NAME_PRE}.jar ${ARTIFACTS_PATH}


maven_package_router:
  stage: package
  image: maven:3.8-openjdk-17
  tags:
    - gwm-app
  only:
    - routerdev
    - routermaster
  before_script:
    - echo "===============  开始编译打包任务  ==============="
    - rm -rf ${ARTIFACTS_PATH} && mkdir ${ARTIFACTS_PATH}
  script:
    - mvn -settings /home/<USER>/.m2/settings.xml clean package -DskipTests
  after_script:
    - cp Dockerfile ${ARTIFACTS_PATH}
    - mv target/${APP_NAME}.jar target/${APP_ROUTER_NAME}.jar
    - cp target/${APP_ROUTER_NAME}.jar ${ARTIFACTS_PATH}


maven_package_pre_router:
  stage: package
  image: maven:3.8-openjdk-17
  tags:
    - gwm-app
  only:
    - routerpremaster
  before_script:
    - echo "===============  开始编译打包任务  ==============="
    - rm -rf ${ARTIFACTS_PATH} && mkdir ${ARTIFACTS_PATH}
  script:
    - mvn -settings /home/<USER>/.m2/settings.xml clean package -DskipTests
  after_script:
    - cp Dockerfile ${ARTIFACTS_PATH}
    - mv target/${APP_NAME}.jar target/${APP_ROUTER_PRE_NAME}.jar
    - cp target/${APP_ROUTER_PRE_NAME}.jar ${ARTIFACTS_PATH}


# 构建镜像
build_docker_image:
  stage: build-image
  image: docker:20.10.5
  tags:
    - gwm-app
  only:
    - develop
    - master
  before_script:
    - echo "===============  开始构建docker镜像任务  ==============="
  script:
    - cp ${ARTIFACTS_PATH}/${APP_NAME}.jar ./
    - sed -i "s/APP_NAME/${APP_NAME}/g" Dockerfile
    - docker build -t ${GWM_APP_IMAGE} .
    - docker login --username $VOLCES_HUB_USERNAME --password $VOLCES_HUB_PASSWORD ailab-cn-beijing.cr.volces.com
    - docker push ${GWM_APP_IMAGE}

build_docker_image_pre:
  stage: build-image
  image: docker:20.10.5
  tags:
    - gwm-app
  only:
    - premaster
  before_script:
    - echo "===============  开始构建docker镜像任务  ==============="
  script:
    - cp ${ARTIFACTS_PATH}/${APP_NAME_PRE}.jar ./
    - sed -i "s/APP_NAME/${APP_NAME_PRE}/g" Dockerfile
    - docker build -t ${GWM_APP_IMAGE} .
    - docker login --username $VOLCES_HUB_USERNAME --password $VOLCES_HUB_PASSWORD ailab-cn-beijing.cr.volces.com
    - docker push ${GWM_APP_IMAGE}


# 构建镜像
build_router_docker_image:
  stage: build-image
  image: docker:20.10.5
  tags:
    - gwm-app
  only:
    - routerdev
    - routermaster
  before_script:
    - echo "===============  开始构建docker镜像任务  ==============="
  script:
    - cp ${ARTIFACTS_PATH}/${APP_ROUTER_NAME}.jar ./
    - sed -i "s/APP_NAME/${APP_ROUTER_NAME}/g" Dockerfile
    - docker build -t ${GWM_APP_IMAGE} .
    - docker login --username $VOLCES_HUB_USERNAME --password $VOLCES_HUB_PASSWORD ailab-cn-beijing.cr.volces.com
    - docker push ${GWM_APP_IMAGE}

build_router_pre_docker_image:
  stage: build-image
  image: docker:20.10.5
  tags:
    - gwm-app
  only:
    - routerpremaster
  before_script:
    - echo "===============  开始构建docker镜像任务  ==============="
  script:
    - cp ${ARTIFACTS_PATH}/${APP_ROUTER_PRE_NAME}.jar ./
    - sed -i "s/APP_NAME/${APP_ROUTER_PRE_NAME}/g" Dockerfile
    - docker build -t ${GWM_APP_IMAGE} .
    - docker login --username $VOLCES_HUB_USERNAME --password $VOLCES_HUB_PASSWORD ailab-cn-beijing.cr.volces.com
    - docker push ${GWM_APP_IMAGE}



# k8s 发布
k8s_deploy_qa:
  image: ailab-cn-beijing.cr.volces.com/default/kubectl:alpine-v1.28
  stage: deploy
  variables:
    BASE64_KUBE_CONFIG: ${GWM_APP_TEST_KUBE_CONFIG}
  tags:
    - gwm-app
  only:
    - develop
  when: always
  before_script:
    - init-kube-config
  script:
    - echo "===============  开始发布Kubernetes Deployment任务  ==============="
    - sed -i "s/IMG_VERSION/${ARTIFACT_IMAGE_VERSION}/g" deploy.yaml
    - sed -i "s/APP_NAME/${APP_NAME}/g" deploy.yaml
    - sed -i "s/APP_NAME/${APP_NAME}/g" service.yaml
    - sed -i "s/IMAGE_NAME/${APP_NAME}/g" deploy.yaml
    - sed -i "s/APP-PROFILE/dev/g" deploy.yaml
    - sed -i "s/POD_LIMIT_CPU/2500m/g" deploy.yaml
    - sed -i "s/POD_LIMIT_MEMORY/4G/g" deploy.yaml
    - sed -i "s/POD_HA_MAX_NUM/1/g" deploy.yaml
    - sed -i "s/POD_HA_MIN_NUM/1/g" deploy.yaml
    - cat deploy.yaml
    - kubectl apply -f deploy.yaml --record=true
    - kubectl apply -f service.yaml --record=true


# k8s 重启
k8s_restart_qa:
  image: ailab-cn-beijing.cr.volces.com/default/kubectl:alpine-v1.28
  stage: deploy
  variables:
    BASE64_KUBE_CONFIG: ${GWM_APP_TEST_KUBE_CONFIG}
  tags:
    - gwm-app
  only:
    - develop
  when: manual
  before_script:
    - init-kube-config
  script:
    - echo "===============  开始重启Kubernetes Deployment任务  ==============="
    - kubectl rollout restart deployment ${APP_NAME}

# k8s 回滚
k8s_undo_qa:
  image: ailab-cn-beijing.cr.volces.com/default/kubectl:alpine-v1.28
  stage: deploy
  variables:
    BASE64_KUBE_CONFIG: ${GWM_APP_TEST_KUBE_CONFIG}
  tags:
    - gwm-app
  only:
    - develop
  when: manual
  before_script:
    - init-kube-config
  script:
    - echo "===============  开始回滚Kubernetes Deployment镜像任务  ==============="
    - kubectl rollout history deployment ${APP_NAME}
    # 回滚镜像到上一个版本
    - kubectl rollout undo deployment ${APP_NAME}


# k8s 下线
k8s_del_qa:
  image: ailab-cn-beijing.cr.volces.com/default/kubectl:alpine-v1.28
  stage: deploy
  variables:
    BASE64_KUBE_CONFIG: ${GWM_APP_TEST_KUBE_CONFIG}
  tags:
    - gwm-app
  only:
    - develop
  when: manual
  before_script:
    - init-kube-config
  script:
    - echo "===============  下线Kubernetes Deployment镜像任务  ==============="
    - kubectl delete deployment ${APP_NAME}






# k8s 发布
k8s_deploy_router_qa:
  image: ailab-cn-beijing.cr.volces.com/default/kubectl:alpine-v1.28
  stage: deploy
  variables:
    BASE64_KUBE_CONFIG: ${GWM_APP_TEST_KUBE_CONFIG}
  tags:
    - gwm-app
  only:
    - routerdev
  when: always
  before_script:
    - init-kube-config
  script:
    - echo "===============  开始发布Kubernetes Deployment任务  ==============="
    - sed -i "s/IMG_VERSION/${ARTIFACT_IMAGE_VERSION}/g" deploy.yaml
    - sed -i "s/APP_NAME/${APP_ROUTER_NAME}/g" deploy.yaml
    - sed -i "s/IMAGE_NAME/${APP_NAME}/g" deploy.yaml
    - sed -i "s/APP-PROFILE/routerdev/g" deploy.yaml
    - sed -i "s/POD_LIMIT_CPU/1500m/g" deploy.yaml
    - sed -i "s/POD_LIMIT_MEMORY/2G/g" deploy.yaml
    - sed -i "s/POD_HA_MAX_NUM/1/g" deploy.yaml
    - sed -i "s/POD_HA_MIN_NUM/1/g" deploy.yaml
    - cat deploy.yaml
    - kubectl apply -f deploy.yaml --record=true


# k8s 重启
k8s_restart_router_qa:
  image: ailab-cn-beijing.cr.volces.com/default/kubectl:alpine-v1.28
  stage: deploy
  variables:
    BASE64_KUBE_CONFIG: ${GWM_APP_TEST_KUBE_CONFIG}
  tags:
    - gwm-app
  only:
    - routerdev
  when: manual
  before_script:
    - init-kube-config
  script:
    - echo "===============  开始重启Kubernetes Deployment任务  ==============="
    - kubectl rollout restart deployment ${APP_ROUTER_NAME}

# k8s 回滚
k8s_undo_router_qa:
  image: ailab-cn-beijing.cr.volces.com/default/kubectl:alpine-v1.28
  stage: deploy
  variables:
    BASE64_KUBE_CONFIG: ${GWM_APP_TEST_KUBE_CONFIG}
  tags:
    - gwm-app
  only:
    - routerdev
  when: manual
  before_script:
    - init-kube-config
  script:
    - echo "===============  开始回滚Kubernetes Deployment镜像任务  ==============="
    - kubectl rollout history deployment ${APP_ROUTER_NAME}
    # 回滚镜像到上一个版本
    - kubectl rollout undo deployment ${APP_ROUTER_NAME}


# k8s 下线
k8s_del_router_qa:
  image: ailab-cn-beijing.cr.volces.com/default/kubectl:alpine-v1.28
  stage: deploy
  variables:
    BASE64_KUBE_CONFIG: ${GWM_APP_TEST_KUBE_CONFIG}
  tags:
    - gwm-app
  only:
    - routerdev
  when: manual
  before_script:
    - init-kube-config
  script:
    - echo "===============  下线Kubernetes Deployment镜像任务  ==============="
    - kubectl delete deployment ${APP_ROUTER_NAME}


# k8s 发布
k8s_deploy_pre:
  image: ailab-cn-beijing.cr.volces.com/default/kubectl:alpine-v1.28
  stage: deploy
  variables:
    BASE64_KUBE_CONFIG: ${GWM_APP_PROD_KUBE_CONFIG}
  tags:
    - gwm-app
  only:
    - premaster
  when: manual
  before_script:
    - init-kube-config
  script:
    - echo "===============  开始发布Kubernetes Deployment任务  ==============="
    - sed -i "s/IMG_VERSION/${ARTIFACT_IMAGE_VERSION}/g" deploy.yaml
    - sed -i "s/APP_NAME/${APP_NAME_PRE}/g" deploy.yaml
    - sed -i "s/APP_NAME/${APP_NAME_PRE}/g" service.yaml
    - sed -i "s/IMAGE_NAME/${APP_NAME}/g" deploy.yaml
    - sed -i "s/APP-PROFILE/pre/g" deploy.yaml
    - sed -i "s/POD_LIMIT_CPU/2000m/g" deploy.yaml
    - sed -i "s/POD_LIMIT_MEMORY/2G/g" deploy.yaml
    - sed -i "s/POD_HA_MAX_NUM/1/g" deploy.yaml
    - sed -i "s/POD_HA_MIN_NUM/1/g" deploy.yaml
    - cat deploy.yaml
    - kubectl apply -f deploy.yaml --record=true
    - kubectl apply -f service.yaml --record=true

# k8s 重启
k8s_restart_pre:
  image: ailab-cn-beijing.cr.volces.com/default/kubectl:alpine-v1.28
  stage: deploy
  variables:
    BASE64_KUBE_CONFIG: ${GWM_APP_PROD_KUBE_CONFIG}
  tags:
    - gwm-app
  only:
    - premaster
  when: manual
  before_script:
    - init-kube-config
  script:
    - echo "===============  开始重启Kubernetes Deployment任务  ==============="
    - kubectl rollout restart deployment ${APP_NAME_PRE}

# k8s 回滚
k8s_undo_pre:
  image: ailab-cn-beijing.cr.volces.com/default/kubectl:alpine-v1.28
  stage: deploy
  variables:
    BASE64_KUBE_CONFIG: ${GWM_APP_PROD_KUBE_CONFIG}
  tags:
    - gwm-app
  only:
    - premaster
  when: manual
  before_script:
    - init-kube-config
  script:
    - echo "===============  开始回滚Kubernetes Deployment镜像任务  ==============="
    - kubectl rollout history deployment ${APP_NAME_PRE}
    # 回滚镜像到上一个版本
    - kubectl rollout undo deployment ${APP_NAME_PRE}


# k8s 下线
k8s_del_pre:
  image: ailab-cn-beijing.cr.volces.com/default/kubectl:alpine-v1.28
  stage: deploy
  variables:
    BASE64_KUBE_CONFIG: ${GWM_APP_PROD_KUBE_CONFIG}
  tags:
    - gwm-app
  only:
    - premaster
  when: manual
  before_script:
    - init-kube-config
  script:
    - echo "===============  下线Kubernetes Deployment镜像任务  ==============="
    # 回滚镜像到上一个版本
    - kubectl delete deployment ${APP_NAME_PRE}



# k8s 发布
k8s_deploy_router_pre:
  image: ailab-cn-beijing.cr.volces.com/default/kubectl:alpine-v1.28
  stage: deploy
  variables:
    BASE64_KUBE_CONFIG: ${GWM_APP_PROD_KUBE_CONFIG}
  tags:
    - gwm-app
  only:
    - routerpremaster
  when: manual
  before_script:
    - init-kube-config
  script:
    - echo "===============  开始发布Kubernetes Deployment任务  ==============="
    - sed -i "s/IMG_VERSION/${ARTIFACT_IMAGE_VERSION}/g" deploy.yaml
    - sed -i "s/APP_NAME/${APP_ROUTER_PRE_NAME}/g" deploy.yaml
    - sed -i "s/IMAGE_NAME/${APP_NAME}/g" deploy.yaml
    - sed -i "s/APP-PROFILE/routerpre/g" deploy.yaml
    - sed -i "s/POD_LIMIT_CPU/2000m/g" deploy.yaml
    - sed -i "s/POD_LIMIT_MEMORY/2G/g" deploy.yaml
    - sed -i "s/POD_HA_MAX_NUM/1/g" deploy.yaml
    - sed -i "s/POD_HA_MIN_NUM/1/g" deploy.yaml
    - cat deploy.yaml
    - kubectl apply -f deploy.yaml --record=true

# k8s 重启
k8s_restart_router_pre:
  image: ailab-cn-beijing.cr.volces.com/default/kubectl:alpine-v1.28
  stage: deploy
  variables:
    BASE64_KUBE_CONFIG: ${GWM_APP_PROD_KUBE_CONFIG}
  tags:
    - gwm-app
  only:
    - routerpremaster
  when: manual
  before_script:
    - init-kube-config
  script:
    - echo "===============  开始重启Kubernetes Deployment任务  ==============="
    - kubectl rollout restart deployment ${APP_ROUTER_PRE_NAME}

# k8s 回滚
k8s_undo_router_pre:
  image: ailab-cn-beijing.cr.volces.com/default/kubectl:alpine-v1.28
  stage: deploy
  variables:
    BASE64_KUBE_CONFIG: ${GWM_APP_PROD_KUBE_CONFIG}
  tags:
    - gwm-app
  only:
    - routerpremaster
  when: manual
  before_script:
    - init-kube-config
  script:
    - echo "===============  开始回滚Kubernetes Deployment镜像任务  ==============="
    - kubectl rollout history deployment ${APP_ROUTER_PRE_NAME}
    # 回滚镜像到上一个版本
    - kubectl rollout undo deployment ${APP_ROUTER_PRE_NAME}


# k8s 下线
k8s_del_router_pre:
  image: ailab-cn-beijing.cr.volces.com/default/kubectl:alpine-v1.28
  stage: deploy
  variables:
    BASE64_KUBE_CONFIG: ${GWM_APP_PROD_KUBE_CONFIG}
  tags:
    - gwm-app
  only:
    - routerpremaster
  when: manual
  before_script:
    - init-kube-config
  script:
    - echo "===============  下线Kubernetes Deployment镜像任务  ==============="
    # 回滚镜像到上一个版本
    - kubectl delete deployment ${APP_ROUTER_PRE_NAME}



# k8s 发布
k8s_deploy_prd:
  image: ailab-cn-beijing.cr.volces.com/default/kubectl:alpine-v1.28
  stage: deploy
  variables:
    BASE64_KUBE_CONFIG: ${GWM_APP_PROD_KUBE_CONFIG}
  tags:
    - gwm-app
  only:
    - master
  when: manual
  before_script:
    - init-kube-config
  script:
    - echo "===============  开始发布Kubernetes Deployment任务  ==============="
    - sed -i "s/IMG_VERSION/${ARTIFACT_IMAGE_VERSION}/g" deploy.yaml
    - sed -i "s/APP_NAME/${APP_NAME}/g" deploy.yaml
    - sed -i "s/APP_NAME/${APP_NAME}/g" service.yaml
    - sed -i "s/IMAGE_NAME/${APP_NAME}/g" deploy.yaml
    - sed -i "s/APP-PROFILE/prd/g" deploy.yaml
    - sed -i "s/POD_LIMIT_CPU/4000m/g" deploy.yaml
    - sed -i "s/POD_LIMIT_MEMORY/4G/g" deploy.yaml
    - sed -i "s/POD_HA_MAX_NUM/3/g" deploy.yaml
    - sed -i "s/POD_HA_MIN_NUM/3/g" deploy.yaml
    - cat deploy.yaml
    - kubectl apply -f deploy.yaml --record=true
    - kubectl apply -f service.yaml --record=true

# k8s 重启
k8s_restart_prd:
  image: ailab-cn-beijing.cr.volces.com/default/kubectl:alpine-v1.28
  stage: deploy
  variables:
    BASE64_KUBE_CONFIG: ${GWM_APP_PROD_KUBE_CONFIG}
  tags:
    - gwm-app
  only:
    - master
  when: manual
  before_script:
    - init-kube-config
  script:
    - echo "===============  开始重启Kubernetes Deployment任务  ==============="
    - kubectl rollout restart deployment ${APP_NAME}

# k8s 回滚
k8s_undo_prd:
  image: ailab-cn-beijing.cr.volces.com/default/kubectl:alpine-v1.28
  stage: deploy
  variables:
    BASE64_KUBE_CONFIG: ${GWM_APP_PROD_KUBE_CONFIG}
  tags:
    - gwm-app
  only:
    - master
  when: manual
  before_script:
    - init-kube-config
  script:
    - echo "===============  开始回滚Kubernetes Deployment镜像任务  ==============="
    - kubectl rollout history deployment ${APP_NAME}
    # 回滚镜像到上一个版本
    - kubectl rollout undo deployment ${APP_NAME}


# k8s 下线
k8s_del_prd:
  image: ailab-cn-beijing.cr.volces.com/default/kubectl:alpine-v1.28
  stage: deploy
  variables:
    BASE64_KUBE_CONFIG: ${GWM_APP_PROD_KUBE_CONFIG}
  tags:
    - gwm-app
  only:
    - master
  when: manual
  before_script:
    - init-kube-config
  script:
    - echo "===============  下线Kubernetes Deployment镜像任务  ==============="
    # 回滚镜像到上一个版本
    - kubectl delete deployment ${APP_NAME}



# k8s 发布
k8s_deploy_router_prd:
  image: ailab-cn-beijing.cr.volces.com/default/kubectl:alpine-v1.28
  stage: deploy
  variables:
    BASE64_KUBE_CONFIG: ${GWM_APP_PROD_KUBE_CONFIG}
  tags:
    - gwm-app
  only:
    - routermaster
  when: manual
  before_script:
    - init-kube-config
  script:
    - echo "===============  开始发布Kubernetes Deployment任务  ==============="
    - sed -i "s/IMG_VERSION/${ARTIFACT_IMAGE_VERSION}/g" deploy.yaml
    - sed -i "s/APP_NAME/${APP_ROUTER_NAME}/g" deploy.yaml
    - sed -i "s/IMAGE_NAME/${APP_NAME}/g" deploy.yaml
    - sed -i "s/APP-PROFILE/routerprd/g" deploy.yaml
    - sed -i "s/POD_LIMIT_CPU/2000m/g" deploy.yaml
    - sed -i "s/POD_LIMIT_MEMORY/2G/g" deploy.yaml
    - sed -i "s/POD_HA_MAX_NUM/5/g" deploy.yaml
    - sed -i "s/POD_HA_MIN_NUM/3/g" deploy.yaml
    - cat deploy.yaml
    - kubectl apply -f deploy.yaml --record=true

# k8s 重启
k8s_restart_router_prd:
  image: ailab-cn-beijing.cr.volces.com/default/kubectl:alpine-v1.28
  stage: deploy
  variables:
    BASE64_KUBE_CONFIG: ${GWM_APP_PROD_KUBE_CONFIG}
  tags:
    - gwm-app
  only:
    - routermaster
  when: manual
  before_script:
    - init-kube-config
  script:
    - echo "===============  开始重启Kubernetes Deployment任务  ==============="
    - kubectl rollout restart deployment ${APP_ROUTER_NAME}

# k8s 回滚
k8s_undo_router_prd:
  image: ailab-cn-beijing.cr.volces.com/default/kubectl:alpine-v1.28
  stage: deploy
  variables:
    BASE64_KUBE_CONFIG: ${GWM_APP_PROD_KUBE_CONFIG}
  tags:
    - gwm-app
  only:
    - routermaster
  when: manual
  before_script:
    - init-kube-config
  script:
    - echo "===============  开始回滚Kubernetes Deployment镜像任务  ==============="
    - kubectl rollout history deployment ${APP_ROUTER_NAME}
    # 回滚镜像到上一个版本
    - kubectl rollout undo deployment ${APP_ROUTER_NAME}


# k8s 下线
k8s_del_router_prd:
  image: ailab-cn-beijing.cr.volces.com/default/kubectl:alpine-v1.28
  stage: deploy
  variables:
    BASE64_KUBE_CONFIG: ${GWM_APP_PROD_KUBE_CONFIG}
  tags:
    - gwm-app
  only:
    - routermaster
  when: manual
  before_script:
    - init-kube-config
  script:
    - echo "===============  下线Kubernetes Deployment镜像任务  ==============="
    # 回滚镜像到上一个版本
    - kubectl delete deployment ${APP_ROUTER_NAME}



# Pipeline完成通知
pipeline_done_notify:
  image: curlimages/curl
  stage: done
  tags:
    - gwm-app
  when: always
  script:
    - echo "===============  开始通知任务  ==============="
    - DONE_MSG=`echo $DONE_MSG | sed 's/[[:space:]]//g'`
    - echo ${DONE_MSG}
    - curl -X POST -H 'Content-Type:application/json' -d ${DONE_MSG}  https://open.feishu.cn/open-apis/bot/v2/hook/${HOOK_ID}