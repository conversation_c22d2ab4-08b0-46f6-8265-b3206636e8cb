package com.beantechs.recom.dataservice.entity.resp;


import com.beantechs.recom.dataservice.config.Constants;

import java.io.Serializable;

public class ResponseEntity<T> implements Serializable {

    public Integer code;
    public T data;
    public String msg;

    public ResponseEntity(int code) {
        this.code = code;
    }

    public ResponseEntity(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public ResponseEntity(int code, T data) {
        this.code = code;
        this.data = data;
    }

    public ResponseEntity(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public Integer getCode() {
        return this.code;
    }

    public T getData() {
        return this.data;
    }

    public String getMsg() {
        return this.msg;
    }

    public static <T> ResponseEntity<T> response(int i, String msg) {
        return new ResponseEntity<>(i, msg);
    }

    public static <T> ResponseEntity<T> responseBySucceed() {
        return new ResponseEntity<>(Constants.SUCCESS_CODE);
    }

    public static <T> ResponseEntity<T> responseBySucceed(String msg) {
        return new ResponseEntity<>(Constants.SUCCESS_CODE, msg);
    }

    public static <T> ResponseEntity<T> responseBySucceed(T data) {
        return new ResponseEntity<>(Constants.SUCCESS_CODE, data);
    }

    public static <T> ResponseEntity<T> responseByFailed(String msg) {
        return new ResponseEntity<>(Constants.ERROR_CODE, msg);
    }

    public static <T> ResponseEntity<T> responseBySucceedData(String msg, T data) {
        return new ResponseEntity<>(Constants.SUCCESS_CODE, msg, data);
    }

    public static <T> ResponseEntity<T> responseByErrorData(String msg, T data) {
        return new ResponseEntity<>(Constants.ERROR_CODE, msg, data);
    }

    public static <T> ResponseEntity<T> responseBySucceedData(T data) {
        return new ResponseEntity<>(Constants.SUCCESS_CODE, data);
    }

    public static <T> ResponseEntity<T> responseByErrorMsg(String msg) {
        return new ResponseEntity<>(Constants.ERROR_CODE, msg);
    }

    public static <T> ResponseEntity<T> responseBySucceedMsg(String msg) {
        return new ResponseEntity<>(Constants.SUCCESS_CODE, msg);
    }

    public static <T> ResponseEntity<T> responseBySucceedMsgAndData(String msg, T data) {
        return new ResponseEntity<>(Constants.SUCCESS_CODE, msg, data);
    }

    public static <T> ResponseEntity<T> responseByErrorMsgAndData(String msg, T data) {
        return new ResponseEntity<>(Constants.SUCCESS_CODE, msg, data);
    }

    public static <T> ResponseEntity<T> success() {
        return new ResponseEntity<>(Constants.SUCCESS_CODE, Constants.SUCCESS_MESSAGE);
    }

    public static <T> ResponseEntity<T> failed() {
        return new ResponseEntity<>(Constants.ERROR_CODE, Constants.ERROR_MESSAGE);
    }


    public static <T> ResponseEntity<T> errorInfo(BaseBusinessStatus baseBusinessStatus) {
        return new ResponseEntity<>(baseBusinessStatus.getCode(), baseBusinessStatus.getMessage());
    }


    public static ResponseEntity<RecommendResp> of(RecommendResp recommendResp) {
        return new ResponseEntity<>(200, "success", recommendResp);
    }

}
