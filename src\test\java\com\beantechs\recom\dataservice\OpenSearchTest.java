package com.beantechs.recom.dataservice;

import com.beantechs.recom.dataservice.config.Constants;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

@SpringBootTest
@Slf4j
public class OpenSearchTest {


    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Test
    public void test() {
        List<String> uidList = Arrays.asList(readUid().split(","));
        List<String> cidList = Arrays.asList(readCid().split(",")).subList(0,300);



        int num = 0;
        for (String uid : uidList) {
            String contentRecommendedSetKey = Constants.REDIS_KEY_RECOM_CONTENTS_ALREADY_RECOM + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + ":" + uid;
//            redisTemplate.delete(contentRecommendedSetKey);
            redisTemplate.opsForSet().add(
                    contentRecommendedSetKey,
                    cidList.toArray(new String[0])
            );
            redisTemplate.expire(contentRecommendedSetKey, 24, TimeUnit.HOURS);
            num++;
            System.out.println(num);
        }
    }


    public String readUid() {
        // 获取项目根目录
        String projectRoot = System.getProperty("user.dir");
        // 拼接文件路径
        String filePath = projectRoot + java.io.File.separator + "uid.txt";
        Path path = Paths.get(filePath);

        try {
            // 读取文件内容为字符串
            return Files.readString(path);
        } catch (IOException e) {
            System.err.println("读取文件时发生错误: " + e.getMessage());
        }
        return null;
    }


    public String readCid() {
        // 获取项目根目录
        String projectRoot = System.getProperty("user.dir");
        // 拼接文件路径
        String filePath = projectRoot + java.io.File.separator + "cid.txt";
        Path path = Paths.get(filePath);

        try {
            // 读取文件内容为字符串
            return Files.readString(path);
        } catch (IOException e) {
            System.err.println("读取文件时发生错误: " + e.getMessage());
        }
        return null;
    }

}
