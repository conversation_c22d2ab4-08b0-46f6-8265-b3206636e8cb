package com.beantechs.recom.dataservice.entity.ddo;

import lombok.Data;
import org.springframework.data.relational.core.mapping.Column;

/**
 * 视频热度结果t0t1表
 */
@Data
public class HotVideoResultInfoT0t1 {

    /**
     * 区分t0,t1
     */
    @Column("t_type")
    private String tType;

    /**
     * 创建时间
     */
    @Column("created_at")
    private String createdAt;

    /**
     * 帖子id
     */
    @Column("content_id")
    private String contentId;

    /**
     * 话题id
     */
    @Column("topic_id")
    private String topicId;

    /**
     * 帖子带的视频
     */
    @Column("video_id")
    private String videoId;

    /**
     * 帖子发布时间
     */
    @Column("publish_time")
    private String publishTime;

    /**
     * 发布人
     */
    private String publisher;

    /**
     * 发布者id
     */
    @Column("create_by")
    private String createBy;

    /**
     * 话题标签
     */
    @Column("topic_kmeans_label")
    private String topicKmeansLabel;

    /**
     * 帖子内容标签
     */
    @Column("content_tag_name")
    private String contentTagName;

    /**
     * 视频时长
     */
    @Column("video_duration")
    private String videoDuration;

    /**
     * 车品牌
     */
    @Column("car_brand_name")
    private String carBrandName;

    /**
     * 内容所属车型名称
     */
    @Column("car_model_name")
    private String carModelName;

    /**
     * 新的内容所属车型名称
     */
    @Column("car_model_name_new")
    private String carModelNameNew;

    /**
     * 新的内容所属品牌名称
     */
    @Column("car_brand_name_new")
    private String carBrandNameNew;

    /**
     * 价格区间
     */
    @Column("post_price_range")
    private String postPriceRange;

    /**
     * 驾驶模式
     */
    @Column("post_drive_mode")
    private String postDriveMode;

    /**
     * 车场景
     */
    @Column("post_car_scene")
    private String postCarScene;

    /**
     * 车级别
     */
    @Column("post_car_level")
    private String postCarLevel;

    /**
     * 车标签
     */
    @Column("post_app_tag")
    private String postAppTag;

    /**
     * 帖子所属频道id
     */
    @Column("content_belong_channels")
    private String contentBelongChannels;

    /**
     * 帖子类型（长图文、轻发布）
     */
    @Column("content_type")
    private String contentType;

    /**
     * 热度得分
     */
    @Column("hot_score")
    private Double hotScore;

    /**
     * 热度排名
     */
    @Column("score_rank")
    private Integer scoreRank;

    /**
     * 日期分区
     */
    private String dt;

    /**
     * 小时分区
     */
    private String hr;


}