package com.beantechs.recom.dataservice.service.handle.recall;

import com.beantechs.recom.dataservice.config.Constants;
import com.beantechs.recom.dataservice.entity.bo.UserScoreVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class CommonRecall {


    protected List<UserScoreVo> getSimilarUsers(String userId, RedisTemplate<String, String> redisTemplate) {
        String similarKey = Constants.REDIS_KEY_RECOM_SIMILARIT_USERCF_PREFIX;
        String similarUsers = null;
        try {
            similarUsers = (String) redisTemplate.opsForHash().get(similarKey, userId);
        } catch (Exception e) {
            String errorMsg = "查询相似用户 = > 查询redis获取相似用户失败, 用户ID:【%s】".formatted(userId);
            log.error(errorMsg, e);
                e.printStackTrace();
        }
        return parseSimilarUsers(similarUsers);
    }


    protected List<UserScoreVo> parseSimilarUsers(String similarUsers) {
        if (!StringUtils.hasText(similarUsers)) {
            return new ArrayList<>();
        }
        return Arrays.stream(similarUsers.split(",")).map(item -> {
            String[] parts = item.split(":");
            UserScoreVo userScoreVo = new UserScoreVo();
            userScoreVo.setUserId(parts[0]);
            userScoreVo.setScore(parts[1]);
            return userScoreVo;
        }).collect(Collectors.toList());
    }

}
