package com.beantechs.recom.dataservice.service.strategy;

import com.beantechs.recom.dataservice.service.handle.ModuleHandle;
import com.beantechs.recom.dataservice.service.handle.ModuleHandleChainBuild;
import com.beantechs.recom.dataservice.service.handle.MultiRecallHandler;
import com.beantechs.recom.dataservice.service.handle.ResponseHandler;

/**
 * 热门策略
 */
@RecommendStrategyAnnotation(way = "channel-tag")
public class ChannelTagStrategy implements RecommendStrategy {

    private final ModuleHandle handlerChain;

    public ChannelTagStrategy() {
        this.handlerChain = new ModuleHandleChainBuild()
                .addHandler(new MultiRecallHandler())
                .addHandler(new ResponseHandler())
                .build();
    }


    @Override
    public void execute() {
        handlerChain.handle();
    }
}
