package com.beantechs.recom.dataservice.service.handle.recall;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beantechs.recom.dataservice.config.Constants;
import com.beantechs.recom.dataservice.entity.bo.ContentScoreVo;
import com.beantechs.recom.dataservice.entity.bo.RecommendContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Slf4j
@MultiRecallType(type = "hot-graphic")
public class HotGraphicRecall implements RecallStrategy {
    @Override
    public List<ContentScoreVo> execute(RecommendContext context) {
        String userId = context.getMainReqParams().getUserId();
        String channelId = context.getMainReqParams().getChannelId();
        RedisTemplate<String, String> redisTemplate = SpringUtil.getBean("stringRedisTemplate");


        String key = Constants.REDIS_KEY_RECOM_GRAPHIC_HOT_ZSET_PREFIX + channelId;
        List<ContentScoreVo> recall = new ArrayList<>();
        try {
            Set<String> value = redisTemplate.opsForZSet().range(key, 0, 150);
            if (!CollectionUtils.isEmpty(value)) {
                recall = value.stream().map(o -> {
                    JSONObject json = JSON.parseObject(o);
                    String score = json.getString("hot_score");
                    String contentId = json.getString("content_id");
                    return ContentScoreVo.of(contentId, score);
                }).toList();
            }
        } catch (Exception e) {
            String errorMsg = "图文热门召回 => 查询热门图文异常, userId:【%s】".formatted(userId);
            log.error(errorMsg, e);
                e.printStackTrace();
        }
        return recall;
    }
}
