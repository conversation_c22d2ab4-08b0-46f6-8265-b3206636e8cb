package com.beantechs.recom.dataservice.config;

public class Constants {

    public final static Integer SUCCESS_CODE = 200;
    public final static Integer ERROR_CODE = 10001;
    public final static String SUCCESS_MESSAGE = "成功";
    public final static String ERROR_MESSAGE = "失败";

    // ====================================================================== 多路协同路数

    /**
     * 用户协同过滤召回
     */
    public static final String RECALL_WAY_USER_CF = "usercf";

    /**
     * 物品协同过滤召回
     */
    public static final String RECALL_WAY_ITEM_CF = "itemcf";

    /**
     * 内容召回
     */
    public static final String RECALL_WAY_CONTENT = "content";

    /**
     * 用户兴趣召回
     */
    public static final String RECALL_WAY_USER_INTEREST = "user-interest";

    /**
     * 用户关注召回
     */
    public static final String RECALL_WAY_USER_FOLLOW = "user-follow";

    /**
     * 热门召回
     */
    public static final String RECALL_WAY_HOT = "hot";

    // ====================================================================== Redis
    // key

    /**
     * 当天已下线内容过滤
     * 参数：当天日期
     * 格式：GWMAPP:TODAY:CONTENT:OFFLINE:[yyyy-MM-dd]
     */
    public static final String REDIS_KEY_TODAY_CONTENT_OFFLINE = "GWMAPP:TODAY:CONTENT:OFFLINE:";

    /**
     * 用户推荐缓存列表-缓存计算结果
     * 参数：频道id、用户id
     * 格式：GWMAPP:RECOM:CONTENTS:BEFORE:RESORT:[channelId]:[userId]
     */
    public static final String REDIS_KEY_RECOM_CONTENTS_BEFORE_RESORT = "GWMAPP:RECOM:CONTENTS:BEFORE:RESORT:";

    /**
     * 用户已推荐列表-过滤已推荐结果
     * 参数：当天日期、用户id
     * 格式：GWMAPP:RECOM:CONTENTS:ALREADY:RECOM:[yyyy-MM-dd]:[userId]
     */
    public static final String REDIS_KEY_RECOM_CONTENTS_ALREADY_RECOM = "GWMAPP:RECOM:CONTENTS:ALREADY:RECOM:";

    /**
     * 用户当天选择兴趣标签-选择召回策略
     * 参数：当天日期、用户id
     * 格式：GWMAPP:RECOM:USER:SELECT:INTEREST:LABEL:[yyyy-MM-dd]:[userId]
     */
    public static final String REDIS_KEY_RECOM_USER_SELECT_INTEREST_LABEL = "GWMAPP:RECOM:USER:SELECT:INTEREST:LABEL:";

    /**
     * 当天用户不喜欢-内容过滤
     * 参数：用户id、当天日期
     * 格式：GWMAPP:TODAY:CONTENT:USER:DISLIKE:[userId]:[yyyy-MM-dd]
     */
    public static final String REDIS_KEY_TODAY_CONTENT_USER_DISLIKE = "GWMAPP:TODAY:CONTENT:USER:DISLIKE:";

    /**
     * 当天用户曝光-内容过滤
     * 参数：用户id、当天日期
     * 格式：GWMAPP:TODAY:CONTENT:USER:EXPOSURE:[userId]:[yyyy-MM-dd]
     */
    public static final String REDIS_KEY_TODAY_CONTENT_USER_EXPOSURE = "GWMAPP:TODAY:CONTENT:USER:EXPOSURE:";

    /**
     * 当天用户拉黑的用户-根据用户id查找用户的发帖
     * 参数：用户id、当天日期
     * 格式：GWMAPP:TODAY:CONTENT:USER:HACK:[userId]:[yyyy-MM-dd]
     */
    public static final String REDIS_KEY_TODAY_CONTENT_USER_HACK = "GWMAPP:TODAY:CONTENT:USER:HACK:";

    /**
     * es索引
     */
    public static final String OPENSEARCH_RECOM_CONTENT_INDEX = "gwm_app_post_embedding_512";

    /**
     * 热度表ZSET有序数组-兜底查询、重排补数、热门召回、离线兴趣标签召回补数
     * 参数：频道id
     * 格式：GWMAPP:RECOM:HOT:GRAPHIC:ZSET:[channelId]
     */
    public static final String REDIS_KEY_RECOM_GRAPHIC_HOT_ZSET_PREFIX = "GWMAPP:RECOM:HOT:GRAPHIC:ZSET:";

    /**
     * 热度表ZSET有序数组-热门召回、离线兴趣标签召回补数
     * 参数：频道id
     * 格式：GWMAPP:RECOM:HOT:VIDEO:ZSET:[channelId]
     */
    public static final String REDIS_KEY_RECOM_VIDEO_HOT_ZSET_PREFIX = "GWMAPP:RECOM:HOT:VIDEO:ZSET:";

    /**
     * t0用户行为-选择召回策略、内容召回、itemCF召回、userCF召回
     * 参数：前一小时时间、频道id
     * 格式：GWMAPP:RECOM:T0BEHAVIOUR:[yyyy-MM-dd HH]:[channelId]
     */
    public static final String REDIS_KEY_RECOM_T0_BEHAVIOUR_PREFIX = "GWMAPP:RECOM:T0BEHAVIOUR:";

    /**
     * t1用户行为-选择召回策略
     * 参数：前一天时间、频道id
     * 格式：GWMAPP:RECOM:T1BEHAVIOUR:[yyyy-MM-dd]:[channelId]
     */
    public static final String REDIS_KEY_RECOM_T1_BEHAVIOUR_PREFIX = "GWMAPP:RECOM:T1BEHAVIOUR:";

    /**
     * itemCFt1召回
     * 参数：前一天时间、频道id
     * 格式：GWMAPP:RECOM:T1RECALL:GRAPHIC:ITEMCF:[yyyy-MM-dd]:[channelId]
     */
    public static final String REDIS_KEY_RECOM_T1_GRAPHIC_ITEMCF_PREFIX = "GWMAPP:RECOM:T1RECALL:GRAPHIC:ITEMCF:";

    /**
     * itemCFt1召回
     * 参数：前一天时间、频道id
     * 格式：GWMAPP:RECOM:T1RECALL:VIDEO:ITEMCF:[yyyy-MM-dd]:[channelId]
     */
    public static final String REDIS_KEY_RECOM_T1_VIDEO_ITEMCF_PREFIX = "GWMAPP:RECOM:T1RECALL:VIDEO:ITEMCF:";

    /**
     * itemCF相似度
     * 参数：前一天时间、频道id
     * 格式：GWMAPP:RECOM:SIMILARIT:ITEMCF:GRAPHIC:[channelId]
     */
    public static final String REDIS_KEY_RECOM_SIMILARIT_GRAPHIC_ITEMCF_PREFIX = "GWMAPP:RECOM:SIMILARIT:ITEMCF:GRAPHIC:";

    /**
     * itemCF相似度
     * 参数：前一天时间、频道id
     * 格式：GWMAPP:RECOM:SIMILARIT:ITEMCF:VIDEO:[channelId]
     */
    public static final String REDIS_KEY_RECOM_SIMILARIT_VIDEO_ITEMCF_PREFIX = "GWMAPP:RECOM:SIMILARIT:ITEMCF:VIDEO:";

    /**
     * userCF相似度
     * 参数：前一天时间
     * 格式：GWMAPP:RECOM:SIMILARIT:USERCF
     */
    public static final String REDIS_KEY_RECOM_SIMILARIT_USERCF_PREFIX = "GWMAPP:RECOM:SIMILARIT:USERCF";

    /**
     * userCFT1召回
     * 参数：前一天时间、频道id
     * 格式：GWMAPP:RECOM:T1RECALL:GRAPHIC:USERCF:[yyyy-MM-dd]:[channelId]
     */
    public static final String REDIS_KEY_RECOM_GRAPHIC_USERCF_PREFIX = "GWMAPP:RECOM:T1RECALL:GRAPHIC:USERCF:";

    /**
     * userCFT1召回
     * 参数：前一天时间、频道id
     * 格式：GWMAPP:RECOM:T1RECALL:VIDEO:USERCF:[yyyy-MM-dd]:[channelId]
     */
    public static final String REDIS_KEY_RECOM_VIDEO_USERCF_PREFIX = "GWMAPP:RECOM:T1RECALL:VIDEO:USERCF:";

    /**
     * 用户点击关注召回
     * 参数：前一天时间、频道id
     * 格式：GWMAPP:RECOM:T1RECALL:GRAPHIC:FOLLOWERRECALL:[yyyy-MM-dd]:[channelId]
     */
    public static final String REDIS_KEY_RECOM_GRAPHIC_FOLLOW_PREFIX = "GWMAPP:RECOM:T1RECALL:GRAPHIC:FOLLOWERRECALL:";

    /**
     * 用户点击关注召回
     * 参数：前一天时间、频道id
     * 格式：GWMAPP:RECOM:T1RECALL:VIDEO:FOLLOWERRECALL:[yyyy-MM-dd]:[channelId]
     */
    public static final String REDIS_KEY_RECOM_VIDEO_FOLLOW_PREFIX = "GWMAPP:RECOM:T1RECALL:VIDEO:FOLLOWERRECALL:";

    /**
     * 离线兴趣标签召回
     * 参数：前一天时间、频道id
     * 格式：GWMAPP:RECOM:T1RECALL:GRAPHIC:SELECTTAGS:[yyyy-MM-dd]:[channelId]
     */
    public static final String REDIS_KEY_RECOM_GRAPHIC_SELECTTAGS_PREFIX = "GWMAPP:RECOM:T1RECALL:GRAPHIC:SELECTTAGS:";

    /**
     * 离线兴趣标签召回
     * 参数：前一天时间、频道id
     * 格式：GWMAPP:RECOM:T1RECALL:VIDEO:SELECTTAGS:[yyyy-MM-dd]:[channelId]
     */
    public static final String REDIS_KEY_RECOM_VIDEO_SELECTTAGS_PREFIX = "GWMAPP:RECOM:T1RECALL:VIDEO:SELECTTAGS:";

    /**
     * 根据id查询内容详情
     * 参数：频道id
     * 格式：GWMAPP:RECOM:HOT:GRAPHIC:HASH:[channelId]
     */
    public static final String REDIS_KEY_RECOM_GRAPHIC_HOT_HASH_PREFIX = "GWMAPP:RECOM:HOT:GRAPHIC:HASH:";

    /**
     * 根据id查询内容详情
     * 参数：频道id
     * 格式：GWMAPP:RECOM:HOT:VIDEO:HASH:[channelId]
     */
    public static final String REDIS_KEY_RECOM_VIDEO_HOT_HASH_PREFIX = "GWMAPP:RECOM:HOT:VIDEO:HASH:";

    /**
     * 用户发帖-黑名单用户发帖
     * 参数：频道id
     * 格式：GWMAPP:RECOM:BLACKUSER:GRAPHIC:[channelId]
     */
    public static final String REDIS_KEY_RECOM_GRAPHIC_HOT_HASH_HACKUSER_PREFIX = "GWMAPP:RECOM:BLACKUSER:GRAPHIC:";

    /**
     * 用户发帖-黑名单用户发帖
     * 参数：频道id
     * 格式：GWMAPP:RECOM:BLACKUSER:VIDEO:[channelId]
     */
    public static final String REDIS_KEY_RECOM_VIDEO_HOT_HASH_HACKUSER_PREFIX = "GWMAPP:RECOM:BLACKUSER:VIDEO:";

    /**
     * 热门无序集合-召回数量不足兜底查询
     * 参数：频道id
     * 格式：GWMAPP:RECOM:HOT:GRAPHIC:SET:[channelId]
     */
    public static final String REDIS_KEY_RECOM_GRAPHIC_SET_TOP_PREFIX = "GWMAPP:RECOM:HOT:GRAPHIC:SET:";

    /**
     * 热门无序集合-召回数量不足兜底查询
     * 参数：频道id
     * 格式：GWMAPP:RECOM:HOT:VIDEO:SET:[channelId]
     */
    public static final String REDIS_KEY_RECOM_VIDEO_SET_TOP_PREFIX = "GWMAPP:RECOM:HOT:VIDEO:SET:";

    /**
     * 内容特征-查询特征CTR预测
     * 参数：前一天时间
     * 格式：GWMAPP:RECOM:CTR:CONTENT:FEATURES:[yyyy-MM-dd]
     */
    public static final String REDIS_KEY_RECOM_CONTENT_FEATURE_PREFIX = "GWMAPP:RECOM:CTR:CONTENT:FEATURES:";

    /**
     * 用户特征-查询特征CTR预测
     * 参数：前一天时间
     * 格式：GWMAPP:RECOM:CTR:USER:GRAPHIC:FEATURES:[yyyy-MM-dd]
     */
    public static final String REDIS_KEY_RECOM_USER_GRAPHIC_FEATURE_PREFIX = "GWMAPP:RECOM:CTR:USER:GRAPHIC:FEATURES:";

    /**
     * 用户特征-查询特征CTR预测
     * 参数：前一天时间
     * 格式：GWMAPP:RECOM:CTR:USER:VIDEO:FEATURES:[yyyy-MM-dd]
     */
    public static final String REDIS_KEY_RECOM_USER_VIDEO_FEATURE_PREFIX = "GWMAPP:RECOM:CTR:USER:VIDEO:FEATURES:";

    /**
     * T1离线过滤内容-过滤内容
     * 参数：前一天时间
     * 格式：GWMAPP:RECOM:T1FILTER:GRAPHIC:[yyyy-MM-dd]
     */
    public static final String REDIS_KEY_RECOM_USER_T1_GRAPHIC_FILTER_PREFIX = "GWMAPP:RECOM:T1FILTER:GRAPHIC";

    /**
     * T1离线过滤内容-过滤内容
     * 参数：前一天时间
     * 格式：GWMAPP:RECOM:T1FILTER:VIDEO:[yyyy-MM-dd]
     */
    public static final String REDIS_KEY_RECOM_USER_T1_VIDEO_FILTER_PREFIX = "GWMAPP:RECOM:T1FILTER:VIDEO:";

    /**
     * T1内容召回
     * 参数：前一天时间、频道id
     * 格式：GWMAPP:RECOM:T1RECALL:GRAPHIC:CONTENTCALL:[yyyy-MM-dd]:[channelId]
     */
    public static final String REDIS_KEY_RECOM_T1_RECALL_CONTENT_GRAPHIC_PREFIX = "GWMAPP:RECOM:T1RECALL:GRAPHIC:CONTENTCALL:";

    /**
     * T1内容召回
     * 参数：前一天时间、频道id
     * 格式：GWMAPP:RECOM:T1RECALL:VIDEO:CONTENTCALL:[yyyy-MM-dd]:[channelId]
     */
    public static final String REDIS_KEY_RECOM_T1_RECALL_CONTENT_VIDEO_PREFIX = "GWMAPP:RECOM:T1RECALL:VIDEO:CONTENTCALL:";

    /**
     * 内容向量-内容召回缓存向量查询
     * 参数：前一天时间、内容id
     * 格式：GWMAPP:RECOM:CONTENT:VECTOR:[yyyy-MM-dd]:[contentId]
     */
    public static final String REDIS_KEY_RECOM_CONTENT_VECTOR_PREFIX = "GWMAPP:RECOM:CONTENT:VECTOR:";

    /**
     * 频道页配置
     * 参数：无
     * 格式：GWMAPP:RECOM:CHANNEL:IDS:CONFIG
     */
    public static final String REDIS_KEY_RECOM_CHANNEL_IDS_CONFIG_PREFIX = "GWMAPP:RECOM:CHANNEL:IDS:CONFIG";

    /**
     * 用户理解图文召回redis key
     * 参数：userId
     * 格式：GWMAPP:RECOM:RECALL:USER_UNDERSTANDING:GRAPHIC:
     */
    public static final String REDIS_KEY_RECOM_GRAPHIC_USER_UNDERSTAND_PREFIX = "GWMAPP:RECOM:RECALL:USER_UNDERSTANDING:GRAPHIC:";

    /**
     * 用户理解视频召回redis key
     * 参数：userId
     * 格式：GWMAPP:RECOM:RECALL:USER_UNDERSTANDING:VIDEO:
     */
    public static final String REDIS_KEY_RECOM_VIDEO_USER_UNDERSTAND_PREFIX = "GWMAPP:RECOM:RECALL:USER_UNDERSTANDING:VIDEO:";


}
