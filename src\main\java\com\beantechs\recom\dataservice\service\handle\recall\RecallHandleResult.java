package com.beantechs.recom.dataservice.service.handle.recall;

import com.beantechs.recom.dataservice.entity.bo.ContentScoreVo;
import com.beantechs.recom.dataservice.entity.resp.RecommendRespContentInfo;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class RecallHandleResult {

    private List<ContentScoreVo> userCfGraphicResult;
    private List<ContentScoreVo> userCfVideoResult;

    private List<ContentScoreVo> itemCfGraphicResult;
    private List<ContentScoreVo> itemCfVideoResult;

    private List<ContentScoreVo> contentGraphicResult;
    private List<ContentScoreVo> contentVideoResult;

    private List<ContentScoreVo> interestTagGraphicResult;
    private List<ContentScoreVo> interestTagVideoResult;

    private List<ContentScoreVo> userFollowGraphicResult;
    private List<ContentScoreVo> userFollowVideoResult;

    private List<ContentScoreVo> hotGraphicResult;
    private List<ContentScoreVo> hotVideoResult;

    private List<ContentScoreVo> dssmGraphicResult;
    private List<ContentScoreVo> dssmVideoResult;

    private List<ContentScoreVo> userUnderstandGraphicResult;
    private List<ContentScoreVo> userUnderstandVideoResult;

    private List<RecommendRespContentInfo> channelTagResult;

    public RecallHandleResult() {
        this.userCfGraphicResult = new ArrayList<>();
        this.userCfVideoResult = new ArrayList<>();

        this.itemCfGraphicResult = new ArrayList<>();
        this.itemCfVideoResult = new ArrayList<>();

        this.contentGraphicResult = new ArrayList<>();
        this.contentVideoResult = new ArrayList<>();

        this.interestTagGraphicResult = new ArrayList<>();
        this.interestTagVideoResult = new ArrayList<>();

        this.userFollowGraphicResult = new ArrayList<>();
        this.userFollowVideoResult = new ArrayList<>();

        this.hotGraphicResult = new ArrayList<>();
        this.hotVideoResult = new ArrayList<>();

        this.dssmGraphicResult = new ArrayList<>();
        this.dssmVideoResult = new ArrayList<>();

        this.userUnderstandGraphicResult = new ArrayList<>();
        this.userUnderstandVideoResult = new ArrayList<>();

        this.channelTagResult = new ArrayList<>();
    }


    public static RecallHandleResult clear(RecallHandleResult result){
        RecallHandleResult recallHandleResult = new RecallHandleResult();
        result.setInterestTagGraphicResult(recallHandleResult.getInterestTagGraphicResult());
        result.setInterestTagVideoResult(recallHandleResult.getInterestTagVideoResult());
        result.setHotGraphicResult(recallHandleResult.getHotGraphicResult());
        result.setHotVideoResult(recallHandleResult.getHotVideoResult());
        result.setChannelTagResult(recallHandleResult.getChannelTagResult());
        return  recallHandleResult;
    }

}
