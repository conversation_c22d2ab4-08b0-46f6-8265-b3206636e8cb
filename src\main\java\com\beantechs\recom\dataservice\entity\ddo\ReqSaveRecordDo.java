package com.beantechs.recom.dataservice.entity.ddo;

import lombok.Data;
import org.springframework.data.relational.core.mapping.Column;

import java.util.UUID;

@Data
public class ReqSaveRecordDo {

    @Column("uuid")
    private String uuid;

    @Column("req_params")
    private String reqParams;

    @Column("step_name")
    private String stepName;

    @Column("result")
    private String result;

    @Column("create_time")
    private String createTime;

    // 生成UUID的方法
    public void generateUuid() {
        this.uuid = UUID.randomUUID().toString();
    }

}
