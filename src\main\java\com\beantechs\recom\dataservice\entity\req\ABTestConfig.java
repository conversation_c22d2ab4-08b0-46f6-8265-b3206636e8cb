package com.beantechs.recom.dataservice.entity.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ABTestConfig {

    //用户id
    private String userId;

    // 分组名称
    private String group_name;

    // 新老接口标识: old-老接口，new-新接口
    private String api;

    // 分流比例
    private double rate;

    // 权重配置  "weight": "{'t0_weight': 1.1, 'graphic_vs_video': '4:1'}"
    private String weight;

    // 冷启动策略 cold_start_base指当前版本策略 一般不动
    private String cold_start;

    // 召回策略列表  "recall": "usercf_recall,itemcf_recall,content_recall,user_interest_recall,follow_users_recall,hot_recall,user_understanding_recall"
    private String recall;

    // 精排模型使用 "rank": "lgb20250117"
    private String rank;

    // 重排序策略列表 "re_rank": "insert_new,graphic_vs_video,tag_break_up"，insert_new-插新逻辑、graphic_vs_video-图文视频配比、tag_break_up-标签打散
    private String re_rank;


    public static ABTestConfig of() {
        return new ABTestConfig(
                "-1",
                "group_base",
                "old",
                0.7,
                "{'t0_weight': 1.1, 'graphic_vs_video': '4:1'}",
                "cold_start_base", "usercf_recall,itemcf_recall,content_recall,user_interest_recall,follow_users_recall,hot_recall",
                "lgb20250117",
                "insert_new,graphic_vs_video,tag_break_up"
        );
    }

}
