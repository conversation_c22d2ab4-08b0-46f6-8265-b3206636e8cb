package com.beantechs.recom.dataservice.config;

import com.alibaba.fastjson.JSON;
import com.beantechs.recom.dataservice.entity.bo.RecommendContext;
import com.beantechs.recom.dataservice.entity.resp.RecommendResp;
import com.beantechs.recom.dataservice.service.RecommendStrategyFactory;
import com.beantechs.recom.dataservice.service.handle.recall.RecallHandleResult;

public class RecommendContextHolder {

    private static final ThreadLocal<RecommendContext> contextThreadLocal = new ThreadLocal<>();

    public static void setContext(RecommendContext context) {
        contextThreadLocal.set(context);
    }

    public static RecommendContext getContext() {
        return contextThreadLocal.get();
    }

    public static void clearContext() {
        contextThreadLocal.remove();
    }

    public static void setContextHashInterestTagField(boolean hashInterestTag) {
        RecommendContext context = getContext();
        context.setHasInterestTag(hashInterestTag);
        setContext(context);
    }


    public static void setContextChannelConfigField(String channelConfig) {
        RecommendContext context = getContext();
        context.setChannelConfig(channelConfig);
        setContext(context);
    }

    public static void setContextStrategyField(String strategyWay) {
        RecommendContext context = getContext();
        context.setStrategyWay(strategyWay);
        context.setRecommendStrategy(RecommendStrategyFactory.getStrategy(strategyWay));
        context.setResultResult(new RecallHandleResult());
        setContext(context);
    }


    public static void setRecomDetail() {
        RecommendContext context = getContext();
        RecommendResp recommendResp = context.getRecommendResp();
        recommendResp.setRecomDetail(JSON.toJSONString(context));
        context.setRecommendResp(recommendResp);
        setContext(context);
    }


    public static void setErrorLogo() {
        RecommendContext context = getContext();
        context.setHasErrorEnum(Boolean.TRUE);
        setContext(context);
    }

    public static void clear() {
        contextThreadLocal.set(new RecommendContext());
    }


}
