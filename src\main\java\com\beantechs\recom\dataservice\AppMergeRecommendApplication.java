package com.beantechs.recom.dataservice;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableAsync
@EnableScheduling
@Slf4j
@EnableDiscoveryClient
public class AppMergeRecommendApplication {

    public static void main(String[] args) {
        SpringApplication.run(AppMergeRecommendApplication.class, args);
        log.warn("==================================project run success=========================================");
    }

}

