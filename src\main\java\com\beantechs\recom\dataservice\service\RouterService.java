package com.beantechs.recom.dataservice.service;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beantechs.recom.dataservice.config.DIYException;
import com.beantechs.recom.dataservice.entity.req.ABTestConfig;
import com.beantechs.recom.dataservice.entity.req.MainReqParams;
import com.beantechs.recom.dataservice.entity.resp.RecommendResp;
import com.beantechs.recom.dataservice.entity.resp.ResponseEntity;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;


@Slf4j
@Service
public class RouterService {

    @Value("${env}")
    private String env;

    private final RedisTemplate<String, String> redisTemplate;

    public RouterService(RedisTemplate<String, String> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    /**
     * 处理推荐请求
     *
     * @param mainReqParams 推荐请求参数
     * @return 推荐响应结果
     */
    public ResponseEntity<RecommendResp> router(MainReqParams mainReqParams) {
        // 参数校验
        try {
            paramCheck(mainReqParams);
        } catch (Exception e) {
            return ResponseEntity.of(RecommendResp.ofDefault(mainReqParams.getUserId()));
        }

        mainReqParams.setContentTagList(tagListCheck(mainReqParams));

        ABTestConfig abTestConfig = initGroupConfig(mainReqParams);

        ResponseEntity<RecommendResp> result = ResponseEntity.of(RecommendResp.ofDefault(mainReqParams.getUserId()));

        String api = abTestConfig.getApi();
        JSONObject json = new JSONObject();
        json.put("mainReqParams", mainReqParams);
        json.put("abTestConfig", abTestConfig);

        String uri;
        if ("new".equals(api)) {
            uri = "http://app-merge-recommend-service:8080/app-recommend/new";
            if (env.equals("pre")) {
                uri = "http://app-merge-recommend-pre-service:8080/pre-app-recommend/new";
            }

        } else {
            uri = "http://app-merge-recommend-service:8080/app-recommend/old";
            if (env.equals("pre")) {
                uri = "http://app-merge-recommend-pre-service:8080/pre-app-recommend/old";
            }
        }


        Map<String, List<String>> header = new HashMap<>();
        header.put("Content-Type", Collections.singletonList("application/json"));

        String post;
        try {
            post = HttpRequest
                    .post(uri)
//                        .post("http://localhost:8080/app-recommend/new")
                    .header(header, true)
                    .body(JSON.toJSONString(json))
                    .execute()
                    .body();
            if (StringUtils.hasText(post)) {
                result = JSON.parseObject(post, ResponseEntity.class);
            }
        } catch (Exception e) {
            String errorMsg = "路由推荐接口失败, userId:【%s】, channelId：【%s】, ABTestGroup信息:【%s】".formatted(
                    mainReqParams.getUserId(), mainReqParams.getChannelId(), JSON.toJSONString(abTestConfig));
            log.error(errorMsg, e);
            e.printStackTrace();
        }
        return result;
    }

    private static @NotNull List<String> tagListCheck(MainReqParams mainReqParams) {
        List<String> tagList = new ArrayList<>();
        try {
            tagList = mainReqParams.getContentTagList().stream().map(o -> {
                String[] split = o.split(":");
                if (!StringUtils.hasText(split[2])) {
                    throw new DIYException();
                }
                return split[2];
            }).distinct().filter(Objects::nonNull).toList();
        } catch (Exception e) {
            String errorMsg = "频道变标签参数校验有误, 将以空集合标签进行推荐 => userId:【%s】, channelId：【%s】, 参数：【%s】".formatted(
                    mainReqParams.getUserId(), mainReqParams.getChannelId(),
                    JSON.toJSONString(mainReqParams.getContentTagList()));
            log.warn(errorMsg);
        }
        return tagList;
    }

    private static void paramCheck(MainReqParams mainReqParams) throws DIYException {
        if (!StringUtils.hasText(mainReqParams.getChannelId())) {
            mainReqParams.setChannelId("-1");
        }

        if (Objects.isNull(mainReqParams.getPageSize()) || mainReqParams.getPageSize() < 0) {
            mainReqParams.setPageSize(10);
        }

        if (mainReqParams.getPageSize() > 100) {
            mainReqParams.setPageSize(100);
        }

        if (!StringUtils.hasText(mainReqParams.getUserId())) {
            log.error("路由服务参数校验 => 未获取到userId, userId为空!");
            throw new DIYException("路由服务参数校验 => 未获取到userId, userId为空!");
        }
    }

    /**
     * 构建分群配置
     */
    private @Nullable ABTestConfig initGroupConfig(MainReqParams mainReqParams) {
        ABTestConfig abTestConfig = ABTestConfig.of();
        try {
            Object groupConfig = redisTemplate.opsForHash().get("GWM:ABTEST:GROUP", mainReqParams.getUserId());
            if (Objects.isNull(groupConfig)) {
                groupConfig = redisTemplate.opsForHash().get("GWM:ABTEST:GROUP", "-1");
                if (Objects.isNull(groupConfig)) {
                    log.error("查询用户分组配置失败, 未查询到默认分组, 使用默认配置：【{}】, userId:【{}】, channelId:【{}】",
                            mainReqParams.getUserId(), mainReqParams.getChannelId(), JSON.toJSONString(abTestConfig));
                    abTestConfig = ABTestConfig.of();
                }
            }
            abTestConfig = JSON.parseObject((String) (groupConfig), ABTestConfig.class);
        } catch (Exception e) {
            String errorMsg = "查询用户分组配置，userId:【%s】，channelId：【%s】".formatted(mainReqParams.getUserId(),
                    mainReqParams.getChannelId());
            log.error(errorMsg, e);
            e.printStackTrace();
        }
        return abTestConfig;
    }

}
