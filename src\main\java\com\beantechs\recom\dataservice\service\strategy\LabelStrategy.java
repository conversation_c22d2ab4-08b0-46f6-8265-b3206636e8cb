package com.beantechs.recom.dataservice.service.strategy;

import com.beantechs.recom.dataservice.service.handle.ModuleHandle;
import com.beantechs.recom.dataservice.service.handle.ModuleHandleChainBuild;
import com.beantechs.recom.dataservice.service.handle.MultiRecallHandler;
import com.beantechs.recom.dataservice.service.handle.ResponseHandler;

/**
 * 热门策略
 */
@RecommendStrategyAnnotation(way = "label")
public class LabelStrategy implements RecommendStrategy {

    private final ModuleHandle handlerChain;

    public LabelStrategy() {
        this.handlerChain = new ModuleHandleChainBuild()
                .addHandler(new MultiRecallHandler())
                .addHandler(new ResponseHandler())
                .build();
    }


    @Override
    public void execute() {
        handlerChain.handle();
    }
}
