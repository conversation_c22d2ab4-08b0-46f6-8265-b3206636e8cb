package com.beantechs.recom.dataservice.service.handle.recall;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.beantechs.recom.dataservice.config.Constants;
import com.beantechs.recom.dataservice.entity.bo.ContentScoreVo;
import com.beantechs.recom.dataservice.entity.bo.RecommendContext;
import com.beantechs.recom.dataservice.service.CommonHandel;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.Request;
import org.opensearch.client.Response;
import org.opensearch.client.RestClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@MultiRecallType(type = "content-graphic")
public class ContentGraphicRecall implements RecallStrategy {
    @Override
    public List<ContentScoreVo> execute(RecommendContext context) {
        String userId = context.getMainReqParams().getUserId();
        String channelId = context.getMainReqParams().getChannelId();
        RedisTemplate<String, String> redisTemplate = SpringUtil.getBean("stringRedisTemplate");
        RestClient restClient = SpringUtil.getBean(RestClient.class);
        JSONObject weight = com.alibaba.fastjson.JSON.parseObject(context.getAbTestConfig().getWeight());

        List<ContentScoreVo> t0Behavior = t0Behavior(userId, channelId, redisTemplate);
        Map<String, List<ContentScoreVo>> t0Recall = t0Recall(userId, channelId, t0Behavior, restClient, redisTemplate);
        List<ContentScoreVo> t1Recall = t1Recall(userId, channelId, redisTemplate);

        return fusion(t0Behavior, t0Recall, t1Recall, weight);
    }

    private List<ContentScoreVo> t1Recall(String userId, String channelId, RedisTemplate<String, String> redisTemplate) {
        String key = Constants.REDIS_KEY_RECOM_T1_RECALL_CONTENT_GRAPHIC_PREFIX + channelId + ":" + userId;
        String value = null;
        try {
            value = (String) redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            String errorMsg = "内容召回查询T1召回 => 查询T1召回redis失败, userId:【%s】, channelId：【%s】".formatted(userId, channelId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }

        return CommonHandel.parseContentScoreVoRecallResult(value);
    }


    private List<ContentScoreVo> t0Behavior(String userId, String channelId, RedisTemplate<String, String> redisTemplate) {
        String key = Constants.REDIS_KEY_RECOM_T0_BEHAVIOUR_PREFIX + LocalDateTime.now().minusHours(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH")) + ":" + channelId;
        List<ContentScoreVo> result = List.of();
        try {
            String value = (String) redisTemplate.opsForHash().get(key, userId);
            if (StringUtils.hasText(value)) {
                result = JSON.parseArray(value)
                        .stream()
                        .map(JSON::toJSONString)
                        .map(JSONObject::parseObject)
                        .filter(o -> o.getString("post_type").equals("-1"))
                        .map(o -> o.getString("content_score"))
                        .map(CommonHandel::parseContentScoreVoRecallResult)
                        .flatMap(List::stream).toList();
            }
        } catch (Exception e) {
            String errorMsg = "内容召回查询T0用户行为 = > 查询T0用户行为redis失败, userId:【%s】, channelId：【%s】".formatted(userId, channelId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }
        return result;
    }


    public Map<String, List<ContentScoreVo>> t0Recall(
            String userId,
            String channelId,
            List<ContentScoreVo> t0Behavior,
            RestClient restClient,
            RedisTemplate<String, String> redisTemplate
    ) {
        Map<String, List<ContentScoreVo>> recallMap = new HashMap<>();

        Map<String, List<Double>> vectors = vectorsGet(userId, t0Behavior, redisTemplate);
        if (vectors.isEmpty()) {
            return Collections.emptyMap();
        }

        try {
            StringBuilder requestBody = new StringBuilder();
            for (Map.Entry<String, List<Double>> entry : vectors.entrySet()) {
                Map<String, Object> request = requestBodyBuild(entry.getValue(), channelId);
                requestBody.append("{\"index\":\"").append(Constants.OPENSEARCH_RECOM_CONTENT_INDEX).append("\"}\n");
                requestBody.append(new ObjectMapper().writeValueAsString(request)).append("\n");
            }

            // 执行bulk请求
            Request request = new Request("POST", "/_msearch");
            request.setJsonEntity(requestBody.toString());

            Response response = restClient.performRequest(request);
            Iterator<String> vectorsCIDs = vectors.keySet().iterator();

            // 解析响应
            JSONObject responseObj = new ObjectMapper().readValue(response.getEntity().getContent(), JSONObject.class);
            JSONArray responses = responseObj.getJSONArray("responses");
            for (int i = 0; i < responses.size() && vectorsCIDs.hasNext(); i++) {
                String contentId = vectorsCIDs.next();
                List<ContentScoreVo> graphicList = new ArrayList<>();
                responses.getJSONObject(i).getJSONObject("hits").getJSONArray("hits")
                        .forEach(item -> graphicList.add(ContentScoreVo.esObjConvertVo(JSONObject.parseObject(JSON.toJSONString(item)))));
                List<ContentScoreVo> scoreList = recallMap.get(contentId);
                if (CollectionUtils.isEmpty(scoreList)) {
                    recallMap.put(contentId, graphicList);
                } else {
                    scoreList.addAll(graphicList);
                    recallMap.put(contentId, scoreList);
                }
            }
        } catch (Exception e) {
            String errorMsg = "内容召回查询相似图文 => 根据向量匹配es相似度图文内容失败, userId:【%s】, channelId：【%s】".formatted(userId, channelId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }
        return recallMap;
    }


    private Map<String, List<Double>> vectorsGet(
            String userId,
            List<ContentScoreVo> t0Behavior,
            RedisTemplate<String, String> redisTemplate
    ) {
        Map<String, List<Double>> vectorMap = new HashMap<>();
        List<String> t0BehaviorCIDs = t0Behavior.stream().map(ContentScoreVo::getContentId).toList();

        if (t0BehaviorCIDs.isEmpty()) {
            return Collections.emptyMap();
        }

        List<String> keys = t0BehaviorCIDs.stream().map(o -> Constants.REDIS_KEY_RECOM_CONTENT_VECTOR_PREFIX + o).toList();
        try {
            List<String> values = redisTemplate.opsForValue().multiGet(keys);
            if (!CollectionUtils.isEmpty(values)) {
                for (int i = 0; i < t0BehaviorCIDs.size(); i++) {
                    if (StringUtils.hasText(values.get(i))) {
                        vectorMap.put(t0BehaviorCIDs.get(i), JSONArray.parseArray(values.get(i), Double.class));
                    }
                }
            }
        } catch (Exception e) {
            String errorMsg = "图文内容召回根据内容id查询向量 => 从redis获取内容向量失败, userId:【%s】, ".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }
        return vectorMap;
    }


    private Map<String, Object> requestBodyBuild(List<Double> vector, String channelId) {
        Map<String, Object> searchRequest = new HashMap<>();

        // 构建KNN查询
        Map<String, Object> knnParams = new HashMap<>();
        Map<String, Object> vectorParams = new HashMap<>();
        vectorParams.put("vector", vector);
        vectorParams.put("k", "150");
        knnParams.put("vector", vectorParams);

        // 构建bool查询
        Map<String, Object> bool = new HashMap<>();
        bool.put("should", Collections.singletonList(Map.of("knn", knnParams)));

        // 构建filter条件
        List<Map<String, Object>> mustClauses = new ArrayList<>();
        mustClauses.add(Map.of("match", Map.of("post_type", "1")));
        mustClauses.add(Map.of("match", Map.of("channel_ids", channelId)));
        mustClauses.add(Map.of("term", Map.of("is_deleted", Map.of("value", 0))));
        mustClauses.add(Map.of("term", Map.of("is_online", Map.of("value", 1))));
        mustClauses.add(Map.of("term", Map.of("is_recommend", Map.of("value", 1))));
        mustClauses.add(Map.of("term", Map.of("is_sink", Map.of("value", 0))));
        mustClauses.add(Map.of("term", Map.of("is_usable", Map.of("value", 1))));

        Map<String, Object> filterBool = new HashMap<>();
        filterBool.put("must", mustClauses);
        bool.put("filter", Map.of("bool", filterBool));

        // 组装最终请求
        searchRequest.put("size", 150);
        searchRequest.put("track_scores", true);
        searchRequest.put("query", Map.of("bool", bool));

        searchRequest.put("_source", "post_type");

        return searchRequest;
    }


    private List<ContentScoreVo> fusion(
            List<ContentScoreVo> t0Behavior,
            Map<String, List<ContentScoreVo>> t0Recall,
            List<ContentScoreVo> t1Recall,
            JSONObject weight
    ) {
        Map<String, Double> fusionMap = new HashMap<>();

        t0Recall.forEach((id, contentList) -> {
            for (ContentScoreVo content : contentList) {
                String contentId = content.getContentId();
                double score = Double.parseDouble(content.getScore());
                Optional<ContentScoreVo> first = t0Behavior.stream().filter(t -> contentId.equals(t.getContentId())).findFirst();
                if (first.isPresent()) {
                    score = Double.parseDouble(first.get().getScore()) * score * weight.getDouble("t0_weight");
                }
                fusionMap.merge(contentId, score, Double::sum);
            }
        });

        for (ContentScoreVo content : t1Recall) {
            fusionMap.merge(content.getContentId(), Double.parseDouble(content.getScore()), Double::sum);
        }

        if (!CollectionUtils.isEmpty(fusionMap)) {
            return fusionMap.entrySet().stream()
                    .map(entry -> {
                        ContentScoreVo vo = new ContentScoreVo();
                        vo.setContentId(entry.getKey());
                        vo.setScore(String.valueOf(entry.getValue()));
                        return vo;
                    })
                    .sorted((a, b) -> Double.compare(
                            Double.parseDouble(b.getScore()),
                            Double.parseDouble(a.getScore())))
                    .toList();
        } else {
            return Collections.emptyList();
        }
    }

}
