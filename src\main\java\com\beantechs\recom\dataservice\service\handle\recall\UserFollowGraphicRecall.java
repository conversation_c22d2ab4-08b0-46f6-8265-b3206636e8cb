package com.beantechs.recom.dataservice.service.handle.recall;

import cn.hutool.extra.spring.SpringUtil;
import com.beantechs.recom.dataservice.config.Constants;
import com.beantechs.recom.dataservice.entity.bo.ContentScoreVo;
import com.beantechs.recom.dataservice.entity.bo.RecommendContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@MultiRecallType(type = "userfollow-graphic")
public class UserFollowGraphicRecall implements RecallStrategy {
    @Override
    public List<ContentScoreVo> execute(RecommendContext context) {
        String userId = context.getMainReqParams().getUserId();
        String channelId = context.getMainReqParams().getChannelId();
        RedisTemplate<String, String> redisTemplate = SpringUtil.getBean("stringRedisTemplate");

        String key = Constants.REDIS_KEY_RECOM_GRAPHIC_FOLLOW_PREFIX + channelId + ":" + userId;


        String value = null;
        try {
            value = (String) redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            String errorMsg = "用户关注图文召回 = > 获取图文召回结果失败, userId:【%s】".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }
        if (StringUtils.hasText(value)) {
            return Arrays.stream(value.split(",")).map(ContentScoreVo::ofNull).toList();
        } else {
            return new ArrayList<>();
        }
    }
}
