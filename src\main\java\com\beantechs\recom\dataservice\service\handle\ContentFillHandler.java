package com.beantechs.recom.dataservice.service.handle;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beantechs.recom.dataservice.config.Constants;
import com.beantechs.recom.dataservice.config.RecommendContextHolder;
import com.beantechs.recom.dataservice.entity.bo.HotContentInfoBo;
import com.beantechs.recom.dataservice.entity.bo.RecommendContext;
import com.beantechs.recom.dataservice.entity.bo.ResortCommonBo;
import com.beantechs.recom.dataservice.entity.resp.RecommendRespContentInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 内容补足处理器
 */
@Slf4j
public class ContentFillHandler extends ModuleHandle {

    @Override
    public void handle() {
        RecommendContext context = RecommendContextHolder.getContext();
        Integer pageSize = context.getMainReqParams().getPageSize();
        String channelId = context.getMainReqParams().getChannelId();
        Set<String> filter = context.getFilterCollections().getTotalFilterCollections();
        List<RecommendRespContentInfo> resp = context.getResp();

        RedisTemplate<String, String> redisTemplate = SpringUtil.getBean("stringRedisTemplate");

        Integer fillNum = 0;
        if (CollectionUtils.isEmpty(resp)) {
            fillNum = pageSize;
        }

        if (resp.size() < pageSize) {
            fillNum = pageSize - resp.size();
        }

        if (fillNum > 0) {
            String key = Constants.REDIS_KEY_RECOM_GRAPHIC_HOT_ZSET_PREFIX + channelId;
            List<RecommendRespContentInfo> fillContent = new ArrayList<>();
            Set<String> value = redisTemplate.opsForZSet().range(key, 0, 300);
            if (!CollectionUtils.isEmpty(value)) {
                fillContent = value.stream()
                        .filter(Objects::nonNull)
                        .map(o -> {
                            JSONObject json = JSON.parseObject(o);
                            if (filter.contains(json.getString("content_id"))) {
                                return null;
                            }
                            HotContentInfoBo content = HotContentInfoBo.ofJSON(json);
                            String label = "-1".equals(channelId) ? content.getCarBrandNameNew() : content.getTopicKmeansLabel();
                            return ResortCommonBo.of(content.getContentId(), "graphic", content.getPublishTime(), label);
                        })
                        .filter(Objects::nonNull)
                        .limit(fillNum)
                        .map(r -> RecommendRespContentInfo.build(r.getContentId(), r.getType()))
                        .toList();
            }
            resp.addAll(fillContent);
        }

        context.setResp(resp);
        RecommendContextHolder.setContext(context);


        if (nextHandler != null) {
            nextHandler.handle();
        }
    }
}
