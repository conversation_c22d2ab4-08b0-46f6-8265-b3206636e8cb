package com.beantechs.recom.dataservice.service.recall;

import com.beantechs.recom.dataservice.config.Constants;
import com.beantechs.recom.dataservice.entity.bo.MultiRecallResultBo;
import com.beantechs.recom.dataservice.service.CommonHandel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * 基于内容的召回处理器
 * 该类负责基于用户历史行为数据，通过内容相似度计算进行内容召回
 * 主要功能包括:
 * 1. 获取用户在不同时间窗口(T0/T1)的行为数据
 * 2. 基于用户交互过的内容，在OpenSearch中查找相似内容
 * 3. 对不同来源的内容进行加权打分，生成最终的召回结果
 */
@Slf4j
@RecallType(type = Constants.RECALL_WAY_USER_FOLLOW)
@Service
public class RecallUserFollowHandle extends CommonHandel implements RecallHandle {

    private final RedisTemplate<String, String> redisTemplate;

    /**
     * 构造函数
     */
    public RecallUserFollowHandle(RedisTemplate<String, String> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    /**
     * 处理内容召回的主要方法
     *
     * @param userId    用户ID
     * @param channelId 频道ID
     * @return 多路召回结果，包含图文和视频内容
     */
    @Override
    public MultiRecallResultBo handle(String userId, String channelId) {
        long l = System.currentTimeMillis();

        String graphicFollowKey = Constants.REDIS_KEY_RECOM_GRAPHIC_FOLLOW_PREFIX + channelId + ":" + userId;
        String videoFollowKey = Constants.REDIS_KEY_RECOM_VIDEO_FOLLOW_PREFIX + channelId + ":" + userId;

        String graphicSqlRecallResult = null;
        try {
            graphicSqlRecallResult = (String) redisTemplate.opsForValue().get(graphicFollowKey);
        } catch (Exception e) {
            String errorMsg = "用户关注召回 = > redis获取图文召回结果失败 === 用户ID:%s".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }

        String videoRecallResult = null;
        try {
            videoRecallResult = (String) redisTemplate.opsForValue().get(videoFollowKey);
        } catch (Exception e) {
            String errorMsg = "用户关注召回 = > redis获取视频召回结果失败 === 用户ID:%s".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }

        MultiRecallResultBo multiRecallResultBo = new MultiRecallResultBo();
        List<String> graphicVoList = List.of();
        List<String> videoVoList = List.of();
        if (StringUtils.hasText(graphicSqlRecallResult)) {
            graphicVoList = Arrays.asList(graphicSqlRecallResult.split(","));
            multiRecallResultBo.setUserFollowVideos(graphicVoList);
        }
        if (StringUtils.hasText(videoRecallResult)) {
            videoVoList = Arrays.asList(videoRecallResult.split(","));
            multiRecallResultBo.setUserFollowVideos(videoVoList);
        }
        log.warn("用户关注召回结果 => userId:【{}】, 图文:【{}】条, 视频:【{}】条, 总耗时:【{}】ms", userId, graphicVoList.size(), videoVoList.size(), System.currentTimeMillis() - l);
        return multiRecallResultBo;
    }


}
