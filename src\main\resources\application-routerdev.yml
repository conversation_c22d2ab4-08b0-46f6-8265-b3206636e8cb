env: routerdev
mysql:
  url: *******************************************************************************************************************************************************************************************************************
  username: root
  password: CHveUK^NZhq6DsNunHED
spring:
  application:
    name: app-merge-recommend-router-service
  data:
    redis:
      host: redis-cnlfen8ge9n87xo3a.redis.ivolces.com
      port: 6379
      database: 0
      password: VcvQPBs7vzU32^uyLGtq
      connect-timeout: 10s
      timeout: 5s
      lettuce:
        pool:
          min-idle: 10
          max-idle: 2000
          max-active: 2000
          max-wait: -1ms
  cloud:
    nacos:
      discovery:
        server-addr: nacos.gwm-app-ailab-test.beantechyun.cn:80
opensearch:
  hosts: opensearch-o-00g0g8uv4esb.escloud.ivolces.com:9200
  username: admin
  password: kBZTyUbTW3dc2uJF2!Qd
server:
  servlet:
    context-path: /app-recommend