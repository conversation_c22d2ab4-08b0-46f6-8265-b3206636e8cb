package com.beantechs.recom.dataservice.service.handle.recall;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.beantechs.recom.dataservice.entity.bo.RecommendContext;
import com.beantechs.recom.dataservice.entity.resp.RecommendRespContentInfo;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.opensearch.client.Request;
import org.opensearch.client.Response;
import org.opensearch.client.RestClient;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@MultiRecallType(type = "channel-tag")
public class ChannelTagRecall implements TagRecallStrategy {
    @Override
    public List<RecommendRespContentInfo> execute(RecommendContext context) {
        String userId = context.getMainReqParams().getUserId();
        String channelId = context.getMainReqParams().getChannelId();
        List<String> tagList = context.getMainReqParams().getContentTagList();
        Integer pageSize = context.getMainReqParams().getPageSize();
        Set<String> filterSet = context.getFilterCollections().getTotalFilterCollections();
        Set<String> alreadyRecomSet = context.getFilterCollections().getAlreadyRecommendCollection();

        RestClient restClient = SpringUtil.getBean(RestClient.class);

        Map<String, List<JSONObject>> tagRecallMap = new HashMap<>();
        List<JSONObject> sortResult = new ArrayList<>();

        try {
            Response response = restClient.performRequest(requestBodyBuild(tagList));
            JSONArray responses = new ObjectMapper().readValue(response.getEntity().getContent(), JSONObject.class).getJSONArray("responses");
            for (int i = 0; i < tagList.size(); i++) {
                String tagId = tagList.get(i);
                JSONArray hits = responses.getJSONObject(i).getJSONObject("hits").getJSONArray("hits");
                if (!CollectionUtils.isEmpty(hits)) {
                    List<JSONObject> source = hits.stream()
                            .map(o -> JSON.parseObject(JSON.toJSONString(o)).getJSONObject("_source"))
                            .map(o -> JSON.parseObject(JSON.toJSONString(o)))
                            .filter(o -> !(filterSet.contains(o.getString("content_id"))))
                            .toList();
                    tagRecallMap.put(tagId, source);
                }
            }


            if (!CollectionUtils.isEmpty(tagRecallMap)) {
                // 剩余所需数量
                int leftNum = pageSize;
                // 剩余标签数量
                int leftTagNum = tagRecallMap.size();
                // 已保存内容
                Set<String> readySet = new HashSet<>();

                // 按每个标签下内容数量进行升序
                List<Map.Entry<String, List<JSONObject>>> tagEntryList = new ArrayList<>(tagRecallMap.entrySet());
                tagEntryList.sort(Comparator.comparingInt(entry -> entry.getValue().size()));
                // 遍历排序后的 List
                for (Map.Entry<String, List<JSONObject>> entry : tagEntryList) {
                    // 当前tagId需从tagEntryList中取出topK条
                    int topK = leftNum / leftTagNum;
                    List<JSONObject> tagValues = entry.getValue();
                    // 实际取出列表
                    List<JSONObject> list = tagValues.stream().filter(o -> !readySet.contains(o.getString("content_id"))).limit(topK).toList();
                    readySet.addAll(list.stream().map(o -> o.getString("content_id")).toList());
                    sortResult.addAll(list);
                    leftNum = leftNum - list.size();
                    leftTagNum--;
                }

                // 取数完之后如果数量还不够
                if (sortResult.size() < pageSize) {
                    // 还差N条
                    int num = pageSize - sortResult.size();
                    filterSet.addAll(readySet);
                    filterSet.removeAll(alreadyRecomSet);

                    // 执行请求
                    Response responseAgain = restClient.performRequest(requestBodyBuildAgain(num, tagList, new ArrayList<>(filterSet)));
                    List<JSONObject> recall = new ObjectMapper()
                            .readValue(responseAgain.getEntity().getContent(), JSONObject.class)
                            .getJSONObject("hits")
                            .getJSONArray("hits")
                            .stream()
                            .map(o -> JSON.parseObject(JSON.toJSONString(o)).getJSONObject("_source"))
                            .map(o -> JSON.parseObject(JSON.toJSONString(o)))
                            .toList();
                    sortResult.addAll(recall);
                }
            } else {
                filterSet.removeAll(alreadyRecomSet);
                // 执行请求
                Response responseAgain = restClient.performRequest(requestBodyBuildAgain(pageSize, tagList, new ArrayList<>(filterSet)));
                List<Object> hits = new ObjectMapper().readValue(responseAgain.getEntity().getContent(), JSONObject.class)
                        .getJSONObject("hits").getJSONArray("hits").stream().toList();
                if (!CollectionUtils.isEmpty(hits)) {
                    List<JSONObject> source = hits.stream()
                            .map(o -> JSON.parseObject(JSON.toJSONString(o)).getJSONObject("_source"))
                            .map(o -> JSON.parseObject(JSON.toJSONString(o)))
                            .toList();
                    sortResult.addAll(source);
                }
            }
        } catch (Exception e) {
            String errorMsg = "根据频道标签召回, 查询es失败, userId：【%s】, channelId：【%s】".formatted(userId, channelId);
            log.error(errorMsg, e);
                e.printStackTrace();
        }
        return sortResult.stream().map(o -> RecommendRespContentInfo.build(o.getString("content_id"), o.getString("post_type").equals("1") ? "graphic" : "video")).toList();
    }


    private static @NotNull Request requestBodyBuild(List<String> tagList) throws Exception {
        StringBuilder requestBody = new StringBuilder();
        String nearOneYearDateTime = LocalDateTime.now().minusYears(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        for (String tagId : tagList) {
            Map<String, Object> query = new HashMap<>();
            Map<String, Object> boolQuery = new HashMap<>();
            List<Object> mustList = new ArrayList<>();


            Map<String, Object> termsQuery = new HashMap<>();
            Map<String, Object> termsMap = new HashMap<>();
            termsQuery.put("content_tag_id.keyword", tagId);
            termsMap.put("term", termsQuery);
            mustList.add(termsMap);

            Map<String, Object> rangeQuery = new HashMap<>();
            Map<String, Object> rangeMap = new HashMap<>();
            Map<String, Object> publishTimeRange = new HashMap<>();
            publishTimeRange.put("gte", nearOneYearDateTime);
            rangeQuery.put("publish_time", publishTimeRange);
            rangeMap.put("range", rangeQuery);
            mustList.add(rangeMap);

            boolQuery.put("must", mustList);
            query.put("bool", boolQuery);

            Map<String, Object> sort = new HashMap<>();
            Map<String, Object> hotScoreSort = new HashMap<>();
            hotScoreSort.put("order", "desc");
            sort.put("hot_score", hotScoreSort);

            List<String> sourceFields = Arrays.asList("content_id", "post_type", "score_rank", "content_tag_id");

            Map<String, Object> searchRequest = new HashMap<>();
            searchRequest.put("size", 500);
            searchRequest.put("query", query);
            searchRequest.put("sort", List.of(sort));
            searchRequest.put("_source", sourceFields);
            requestBody.append("{\"index\":\"").append("hot_t0t1_result_es").append("\"}\n");
            requestBody.append(new ObjectMapper().writeValueAsString(searchRequest)).append("\n");
        }

        // 执行bulk请求
        Request request = new Request("POST", "/_msearch");
        request.setJsonEntity(requestBody.toString());
        return request;
    }


    private static Request requestBodyBuildAgain(int num, List<String> tags, List<String> filters) throws JsonProcessingException {
        Map<String, Object> searchRequest = new HashMap<>();
        String nearOneYearDateTime = LocalDateTime.now().minusYears(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        // 设置查询返回的文档数量
        searchRequest.put("size", num);

        // 构建 bool 查询
        Map<String, Object> bool = new HashMap<>();

        // 构建 must 子句
        List<Map<String, Object>> mustClauses = new ArrayList<>();

        // 构建 publish_time 范围查询
        Map<String, Object> publishTimeRange = new HashMap<>();
        Map<String, Object> rangeParams = new HashMap<>();
        rangeParams.put("gte", nearOneYearDateTime);
        publishTimeRange.put("publish_time", rangeParams);
        mustClauses.add(Map.of("range", publishTimeRange));

        // 构建 content_tag_id 条件查询
        Map<String, Object> contentTagIdTerms = new HashMap<>();
        contentTagIdTerms.put("content_tag_id.keyword", tags);
        mustClauses.add(Map.of("terms", contentTagIdTerms));

        bool.put("must", mustClauses);

        // 构建 must_not 子句
        List<Map<String, Object>> mustNotClauses = new ArrayList<>();
        Map<String, Object> contentIdTerms = new HashMap<>();
        contentIdTerms.put("content_id", filters);
        mustNotClauses.add(Map.of("terms", contentIdTerms));
        bool.put("must_not", mustNotClauses);

        // 将 bool 查询添加到主查询中
        searchRequest.put("query", Map.of("bool", bool));

        // 构建排序规则
        List<Map<String, Object>> sortRules = new ArrayList<>();
        Map<String, Object> hotScoreSort = new HashMap<>();
        Map<String, String> order = new HashMap<>();
        order.put("order", "desc");
        hotScoreSort.put("hot_score", order);
        sortRules.add(hotScoreSort);
        searchRequest.put("sort", sortRules);

        // 指定返回的字段
        List<String> sourceFields = Arrays.asList("content_id", "post_type", "score_rank", "content_tag_id");
        searchRequest.put("_source", sourceFields);


        // 将查询体转换为 JSON 字符串
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonBody = objectMapper.writeValueAsString(searchRequest);

        // 创建请求
        Request request = new Request("GET", "/hot_t0t1_result_es/_search");
        request.setJsonEntity(jsonBody);
        return request;
    }
}
