package com.beantechs.recom.dataservice.entity.bo;

import com.beantechs.recom.dataservice.config.CommonUtils;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

@Data
@NoArgsConstructor
public class FilterCollections {

    /**
     * 总过滤集合
     */
    private Set<String> totalFilterCollections;

    /**
     * 用户已推荐的内容集合
     */
    private Set<String> alreadyRecommendCollection;

    public FilterCollections(
            Set<String> t1GraphicFilterCollections,
            Set<String> t1VideoFilterCollections,
            Set<String> offlineCollections,
            Set<String> dislikeCollections,
            Set<String> exposureCollections,
            Set<String> userBlockCollections,
            Set<String> alreadyRecommendCollection
    ) {
        this.alreadyRecommendCollection = alreadyRecommendCollection;
        this.totalFilterCollections = CommonUtils.mergeMultipleSets(
                t1GraphicFilterCollections,
                t1VideoFilterCollections,
                offlineCollections,
                dislikeCollections,
                exposureCollections,
                userBlockCollections,
                alreadyRecommendCollection
        );
    }

}
