package com.beantechs.recom.dataservice.service.handle.recall;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class MultiRecallFactory {
    private static final Map<String, RecallStrategy> recallStrategyMap = new HashMap<>();
    private static final Map<String, TagRecallStrategy> recallTagStrategyMap = new HashMap<>();

    static {
        // 扫描所有策略类，根据注解注册策略
        Class<?>[] recallStrategyClasses = {
                UserUnderstandVideoRecall.class,
                UserUnderstandGraphicRecall.class,
                UserFollowVideoRecall.class,
                UserFollowGraphicRecall.class,
                ItemCFVideoRecall.class,
                ItemCFGraphicRecall.class,
                UserCFVideoRecall.class,
                UserCFGraphicRecall.class,
                InterestTagVideoRecall.class,
                InterestTagGraphicRecall.class,
                HotVideoRecall.class,
                HotGraphicRecall.class,
                DSSMVideoRecall.class,
                DSSMGraphicRecall.class,
                ContentVideoRecall.class,
                ContentGraphicRecall.class
        };
        for (Class<?> recallStrategyClass : recallStrategyClasses) {
            MultiRecallType annotation = recallStrategyClass.getAnnotation(MultiRecallType.class);
            if (annotation != null) {
                try {
                    recallStrategyMap.put(annotation.type(), (RecallStrategy) recallStrategyClass.getDeclaredConstructor().newInstance());
                } catch (Exception e) {
                    log.error("未知召回策略", e);
                }
            }
        }

        Class<?>[] recallTagStrategyClasses = {
                ChannelTagRecall.class
        };
        for (Class<?> recallTagStrategyClass : recallTagStrategyClasses) {
            MultiRecallType annotation = recallTagStrategyClass.getAnnotation(MultiRecallType.class);
            if (annotation != null) {
                try {
                    recallTagStrategyMap.put(annotation.type(), (TagRecallStrategy) recallTagStrategyClass.getDeclaredConstructor().newInstance());
                } catch (Exception e) {
                    log.error("未知召回策略", e);
                }
            }
        }
    }

    public static RecallStrategy getRecallStrategy(String type) {
        return recallStrategyMap.get(type);
    }

    public static TagRecallStrategy getTagRecallStrategy(String type) {
        return recallTagStrategyMap.get(type);
    }

}
