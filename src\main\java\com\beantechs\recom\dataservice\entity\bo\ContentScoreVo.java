package com.beantechs.recom.dataservice.entity.bo;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 * 内容评分VO
 * 用于表示内容的评分信息
 */
@Data
public class ContentScoreVo {
    /**
     * 内容ID
     */
    private String contentId;

    /**
     * 内容评分
     */
    private String score;


    public static ContentScoreVo of(String contentId, String score) {
        ContentScoreVo contentScoreVo = new ContentScoreVo();
        contentScoreVo.setContentId(contentId);
        contentScoreVo.setScore(score);
        return contentScoreVo;
    }


    public static ContentScoreVo ofNull(String contentId) {
        ContentScoreVo contentScoreVo = new ContentScoreVo();
        contentScoreVo.setContentId(contentId);
        contentScoreVo.setScore("0.00");
        return contentScoreVo;
    }


    public static ContentScoreVo esObjConvertVo(JSONObject jsonObject) {
        ContentScoreVo contentScoreVo = new ContentScoreVo();
        contentScoreVo.setContentId(jsonObject.getString("_id"));
        contentScoreVo.setScore(jsonObject.getString("_score"));
        return contentScoreVo;
    }

}
