package com.beantechs.recom.dataservice.service.recall;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.beantechs.recom.dataservice.config.Constants;
import com.beantechs.recom.dataservice.entity.bo.ContentScoreVo;
import com.beantechs.recom.dataservice.entity.bo.MultiRecallResultBo;
import com.beantechs.recom.dataservice.entity.bo.UserScoreVo;
import com.beantechs.recom.dataservice.service.CommonHandel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 基于用户协同过滤的召回处理器
 * 通过计算用户相似度，从相似用户的行为中召回内容
 * 支持T0/T1时间窗口的行为数据，并对不同时间窗口的数据进行加权处理
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Slf4j
@RecallType(type = Constants.RECALL_WAY_USER_CF)
@Service
public class RecallUserCFHandle extends CommonHandel implements RecallHandle {

    private final RedisTemplate<String, String> redisTemplate;

    public RecallUserCFHandle(RedisTemplate<String, String> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public MultiRecallResultBo handle(String userId, String channelId) {
        long l = System.currentTimeMillis();
        // 获取相似用户列表
        List<UserScoreVo> similarUsers = getSimilarUsers(userId);

        List<String> similarUserIds = similarUsers.stream().map(UserScoreVo::getUserId).toList();

        // 获取相似用户的行为数据
        Map<String, List<ContentScoreVo>> t0GraphicScores = new HashMap<>();
        Map<String, List<ContentScoreVo>> t0VideoScores = new HashMap<>();

        String dateString = LocalDateTime.now().minusHours(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH"));
        String t0Key = Constants.REDIS_KEY_RECOM_T0_BEHAVIOUR_PREFIX + dateString + ":" + channelId;
        List<Object> similarUserBehaviourScore;
        try {
            similarUserBehaviourScore = redisTemplate.opsForHash().multiGet(t0Key, similarUserIds.stream().map(o -> (Object) o).toList());
            if (!CollectionUtils.isEmpty(similarUserBehaviourScore)) {
                for (int i = 0; i < similarUserIds.size(); i++) {
                    if (!Objects.isNull(similarUserBehaviourScore.get(i))) {
                        Optional<JSONObject> graphic = JSONArray.parseArray(String.valueOf(similarUserBehaviourScore.get(i)))
                                .stream()
                                .map(JSON::toJSONString)
                                .map(JSONObject::parseObject)
                                .filter(o -> o.getString("post_type").equals("1"))
                                .findFirst();
                        Optional<JSONObject> video = JSONArray.parseArray(String.valueOf(similarUserBehaviourScore.get(i)))
                                .stream()
                                .map(JSON::toJSONString)
                                .map(JSONObject::parseObject)
                                .filter(o -> o.getString("post_type").equals("2"))
                                .findFirst();

                        if (graphic.isPresent()) {
                            t0GraphicScores.put(String.valueOf(similarUserIds.get(i)), parseContentScoreVoRecallResult(graphic.get().getString("content_score")));
                        }
                        if (video.isPresent()) {
                            t0VideoScores.put(String.valueOf(similarUserIds.get(i)), parseContentScoreVoRecallResult(video.get().getString("content_score")));
                        }
                    }
                }
            }
        } catch (Exception e) {
            String errorMsg = "UserCF召回结果 => userId:%s, 从redis获取相似用户行为评分失败".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }

        // 获取离线计算的召回结果
        List<ContentScoreVo> offlineGraphics = getOfflineGraphics(userId, channelId);

        List<ContentScoreVo> offlineVideos = getOfflineVideos(userId, channelId);

        // 计算加权分数
        List<ContentScoreVo> weightedGraphicScores = calculateWeightedScores(
                similarUsers,
                t0GraphicScores,
                offlineGraphics
        );
        List<ContentScoreVo> weightedVideoScores = calculateWeightedScores(
                similarUsers,
                t0VideoScores,
                offlineVideos
        );
        // 封装结果
        MultiRecallResultBo result = new MultiRecallResultBo();
        result.setUserCfGraphics(weightedGraphicScores);
        result.setUserCfVideos(weightedVideoScores);

        log.warn("UserCF召回结果 => userId:【{}】, 图文:【{}】条, 视频:【{}】条, 总耗时:【{}】ms", userId, weightedGraphicScores.size(), weightedVideoScores.size(), System.currentTimeMillis() - l);
        return result;
    }

    private List<ContentScoreVo> getOfflineGraphics(String userId, String channelId) {
        String recallGraphicKey = Constants.REDIS_KEY_RECOM_GRAPHIC_USERCF_PREFIX + channelId + ":" + userId;

        String graphic = null;
        try {
            graphic = (String) redisTemplate.opsForValue().get(recallGraphicKey);
        } catch (Exception e) {
            String errorMsg = "UserCF召回 => userId:%s, Redis查询离线图文异常".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }
        return parseContentScoreVoRecallResult(graphic);
    }

    private List<ContentScoreVo> getOfflineVideos(String userId, String channelId) {
        String recallVideoKey = Constants.REDIS_KEY_RECOM_VIDEO_USERCF_PREFIX + channelId + ":" + userId;

        String video = null;
        try {
            video = (String) redisTemplate.opsForValue().get(recallVideoKey);
        } catch (Exception e) {
            String errorMsg = "UserCF召回 => Redis查询离线视频异常 === 用户ID:%s".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }
        return parseContentScoreVoRecallResult(video);
    }


    private List<UserScoreVo> getSimilarUsers(String userId) {
        String userCFKey = Constants.REDIS_KEY_RECOM_SIMILARIT_USERCF_PREFIX;

        String similarUsers = null;
        try {
            similarUsers = (String) redisTemplate.opsForHash().get(userCFKey, userId);
        } catch (Exception e) {
            String errorMsg = "UserCF召回 = > Redis查询相似用户异常 === 用户ID:%s".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }
        return parseSimilarUsers(similarUsers);
    }

    private List<UserScoreVo> parseSimilarUsers(String similarUsers) {
        if (!StringUtils.hasText(similarUsers)) {
            return new ArrayList<>();
        }

        return Arrays.stream(similarUsers.split(",")).map(item -> {
            String[] parts = item.split(":");
            UserScoreVo userScoreVo = new UserScoreVo();
            userScoreVo.setUserId(parts[0]);
            userScoreVo.setScore(parts[1]);
            return userScoreVo;
        }).collect(Collectors.toList());
    }

    /**
     * 计算内容的加权分数
     * 考虑用户相似度、行为分数和时间窗口权重
     *
     * @param similarUsers    相似用户列表
     * @param t0Scores        T0时间窗口的行为分数
     * @param offlineContents 离线计算的召回结果
     * @return 加权后的内容分数列表
     */
    private List<ContentScoreVo> calculateWeightedScores(
            List<UserScoreVo> similarUsers,
            Map<String, List<ContentScoreVo>> t0Scores,
            List<ContentScoreVo> offlineContents) {
        Map<String, Double> contentScores = new HashMap<>();

        // 处理相似用户的行为数据
        for (UserScoreVo similarUser : similarUsers) {
            String userId = similarUser.getUserId();
            double similarityScore = Double.parseDouble(similarUser.getScore());
            List<ContentScoreVo> t0Behaviors = t0Scores.getOrDefault(userId, Collections.emptyList());
            processUserScores(t0Behaviors, similarityScore, contentScores);
        }

        // 合并离线召回结果
        for (ContentScoreVo offlineContent : offlineContents) {
            String contentId = offlineContent.getContentId();
            double offlineScore = Double.parseDouble(offlineContent.getScore());
            contentScores.merge(contentId, offlineScore, Double::sum);
        }

        return contentScores.entrySet().stream()
                .map(entry -> {
                    ContentScoreVo vo = new ContentScoreVo();
                    vo.setContentId(entry.getKey());
                    vo.setScore(String.valueOf(entry.getValue()));
                    return vo;
                })
                .sorted((a, b) -> Double.compare(
                        Double.parseDouble(b.getScore()),
                        Double.parseDouble(a.getScore())))
                .collect(Collectors.toList());
    }

    /**
     * 处理单个用户的行为分数
     *
     * @param userScores      用户的行为分数列表
     * @param similarityScore 用户相似度分数
     * @param contentScores   累积的内容分数映射
     */
    private void processUserScores(
            List<ContentScoreVo> userScores,
            double similarityScore,
            Map<String, Double> contentScores) {

        for (ContentScoreVo score : userScores) {
            String contentId = score.getContentId();
            double behaviorScore = Double.parseDouble(score.getScore());
            double weightedScore = behaviorScore * similarityScore * 1.1;
            contentScores.merge(contentId, weightedScore, Double::sum);
        }
    }
}
