package com.beantechs.recom.dataservice.controller;

import com.beantechs.recom.dataservice.entity.req.MainReqParams;
import com.beantechs.recom.dataservice.entity.resp.RecommendResp;
import com.beantechs.recom.dataservice.entity.resp.ResponseEntity;
import com.beantechs.recom.dataservice.service.RouterService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 推荐服务API
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Slf4j
@Tag(name = "router-controller", description = "推荐服务接口")
public class RouteController {


    private final RouterService routerService;

    public RouteController(RouterService routerService) {
        this.routerService = routerService;
    }


    @Operation(summary = "路由接口-选择调用哪个接口", description = "路由接口-选择调用哪个接口")
    @PostMapping("/real/recom")
    public ResponseEntity<RecommendResp> recom(
            @RequestBody @Valid @Parameter(description = "推荐请求参数", required = true) MainReqParams mainReqParams
    ) {
        return routerService.router(mainReqParams);
    }


}
