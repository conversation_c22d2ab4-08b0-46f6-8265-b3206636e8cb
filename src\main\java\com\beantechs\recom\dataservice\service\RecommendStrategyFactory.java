package com.beantechs.recom.dataservice.service;


import com.beantechs.recom.dataservice.service.strategy.*;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class RecommendStrategyFactory {
    private static final Map<String, RecommendStrategy> strategyMap = new HashMap<>();

    static {
        // 扫描所有策略类，根据注解注册策略
        Class<?>[] strategyClasses = {ChannelTagStrategy.class, FullProcessStrategy.class, LabelStrategy.class, PopularStrategy.class};
        for (Class<?> strategyClass : strategyClasses) {
            RecommendStrategyAnnotation annotation = strategyClass.getAnnotation(RecommendStrategyAnnotation.class);
            if (annotation != null) {
                try {
                    strategyMap.put(annotation.way(), (RecommendStrategy) strategyClass.getDeclaredConstructor().newInstance());
                } catch (Exception e) {
                    log.error("未知策略", e);
                }
            }
        }
    }

    public static RecommendStrategy getStrategy(String way) {
        return strategyMap.get(way);
    }


}
