package com.beantechs.recom.dataservice.entity.bo;

import com.beantechs.recom.dataservice.config.Constants;
import com.beantechs.recom.dataservice.entity.resp.RecommendRespContentInfo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 多路召回结果实体类
 * 包含不同召回方式的图文和视频内容ID列表
 */
@Data
@Slf4j
public class MultiRecallResultBo {

    /**
     * UserCF召回图文内容ID列表
     */
    private List<ContentScoreVo> userCfGraphics;

    /**
     * UserCF召回视频内容ID列表
     */
    private List<ContentScoreVo> userCfVideos;

    /**
     * ItemCF召回图文内容ID列表
     */
    private List<ContentScoreVo> itemCfGraphics;

    /**
     * ItemCF召回视频内容ID列表
     */
    private List<ContentScoreVo> itemCfVideos;

    /**
     * 内容召回图文内容ID列表
     */
    private List<ContentScoreVo> contentGraphics;

    /**
     * 内容召回视频内容ID列表
     */
    private List<ContentScoreVo> contentVideos;

    /**
     * 用户兴趣召回图文内容ID列表
     */
    private List<ContentScoreVo> userInterestGraphics;

    /**
     * 用户兴趣召回视频内容ID列表
     */
    private List<ContentScoreVo> userInterestVideos;


    /**
     * 用户兴趣召回图文内容ID列表
     */
    private List<String> userFollowGraphics;

    /**
     * 用户兴趣召回视频内容ID列表
     */
    private List<String> userFollowVideos;


    /**
     * 热门召回图文内容ID列表
     */
    private List<ContentScoreVo> hotGraphics;

    /**
     * 热门召回视频内容ID列表
     */
    private List<ContentScoreVo> hotVideos;

    /**
     * 图文id集合
     */
    private List<String> graphicIds;

    /**
     * 视频id集合
     */
    private List<String> videoIds;

    /**
     * CTR图文排序池
     */
    private List<HotContentInfoBo> ctrSortGraphicDetails;

    /**
     * CTR视频排序池
     */
    private List<HotContentInfoBo> ctrSortVideoDetails;

    /**
     * 新内容图文详情列表
     */
    private List<HotContentInfoBo> newestGraphicDetails;

    /**
     * 新内容视频详情列表
     */
    private List<HotContentInfoBo> newestVideoDetails;

    /**
     * 需要过滤的集合
     */
    private Set<String> filterList;

    /**
     * 推荐结果
     */
    private List<RecommendRespContentInfo> result;


    public static boolean isEmpty(MultiRecallResultBo multiRecallResultBo) {
        return CollectionUtils.isEmpty(multiRecallResultBo.getUserCfGraphics()) &&
               CollectionUtils.isEmpty(multiRecallResultBo.getUserCfVideos()) &&
               CollectionUtils.isEmpty(multiRecallResultBo.getItemCfGraphics()) &&
               CollectionUtils.isEmpty(multiRecallResultBo.getItemCfVideos()) &&
               CollectionUtils.isEmpty(multiRecallResultBo.getUserFollowGraphics()) &&
               CollectionUtils.isEmpty(multiRecallResultBo.getUserFollowVideos()) &&
               CollectionUtils.isEmpty(multiRecallResultBo.getContentGraphics()) &&
               CollectionUtils.isEmpty(multiRecallResultBo.getContentVideos()) &&
               CollectionUtils.isEmpty(multiRecallResultBo.getUserInterestGraphics()) &&
               CollectionUtils.isEmpty(multiRecallResultBo.getUserInterestVideos()) &&
               CollectionUtils.isEmpty(multiRecallResultBo.getHotGraphics()) &&
               CollectionUtils.isEmpty(multiRecallResultBo.getHotVideos());
    }


    /**
     * 合并所有召回结果并按分数排序
     */
    public static void mergeAndSortResults(
            MultiRecallResultBo multiRecallResultBo,
            RedisTemplate<String, String> redisTemplate,
            List<String> t1FilterIds,
            String userId,
            String channelId,
            Set<String> contentRecommended
    ) {
        long l = System.currentTimeMillis();
        int limit = 150;

        List<ContentScoreVo> userCfGraphics = multiRecallResultBo.getUserCfGraphics();
        List<ContentScoreVo> userCfVideos = multiRecallResultBo.getUserCfVideos();
        List<ContentScoreVo> itemCfGraphics = multiRecallResultBo.getItemCfGraphics();
        List<ContentScoreVo> itemCfVideos = multiRecallResultBo.getItemCfVideos();
        List<ContentScoreVo> contentGraphics = multiRecallResultBo.getContentGraphics();
        List<ContentScoreVo> contentVideos = multiRecallResultBo.getContentVideos();
        List<ContentScoreVo> userInterestGraphics = multiRecallResultBo.getUserInterestGraphics();
        List<ContentScoreVo> userInterestVideos = multiRecallResultBo.getUserInterestVideos();
        List<String> userFollowGraphics = multiRecallResultBo.getUserFollowGraphics();
        List<String> userFollowVideos = multiRecallResultBo.getUserFollowVideos();
        List<ContentScoreVo> hotGraphics = multiRecallResultBo.getHotGraphics();
        List<ContentScoreVo> hotVideos = multiRecallResultBo.getHotVideos();

        Set<String> filterList = new HashSet<>();
        Set<String> offlineRecommend = null;
        String offlineKey = Constants.REDIS_KEY_TODAY_CONTENT_OFFLINE + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String dislikeKey = Constants.REDIS_KEY_TODAY_CONTENT_USER_DISLIKE + userId + ":" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String exposureKey = Constants.REDIS_KEY_TODAY_CONTENT_USER_EXPOSURE + userId + ":" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String hackUserKey = Constants.REDIS_KEY_TODAY_CONTENT_USER_HACK + userId + ":" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        try {
            offlineRecommend = redisTemplate.opsForSet().members(offlineKey);
        } catch (Exception e) {
            String errorMsg = "T0过滤 => userId:%s, 查询redis下线内容失败".formatted(userId);
            log.error(errorMsg, e);
                e.printStackTrace();
        }
        Set<String> dislikeRecommend = null;
        try {
            dislikeRecommend = redisTemplate.opsForSet().members(dislikeKey);
        } catch (Exception e) {
            String errorMsg = "T0过滤 => userId:%s, 查询redis不喜欢内容失败".formatted(userId);
            log.error(errorMsg, e);
                e.printStackTrace();
        }
        Set<String> exposureRecommend = null;
        try {
            exposureRecommend = redisTemplate.opsForSet().members(exposureKey);
        } catch (Exception e) {
            String errorMsg = "T0过滤 => userId:%s, 查询redis曝光内容失败".formatted(userId);
            log.error(errorMsg, e);
                e.printStackTrace();
        }
        Set<String> hackUserRecommend = null;
        try {
            hackUserRecommend = redisTemplate.opsForSet().members(hackUserKey);
        } catch (Exception e) {
            String errorMsg = "T0过滤 => userId:%s, 查询redis用户黑名单内容失败".formatted(userId);
            log.error(errorMsg, e);
                e.printStackTrace();
        }

        if (!CollectionUtils.isEmpty(offlineRecommend)) {
            filterList.addAll(offlineRecommend);
        }
        if (!CollectionUtils.isEmpty(t1FilterIds)) {
            filterList.addAll(t1FilterIds);
        }

        if (!CollectionUtils.isEmpty(dislikeRecommend)) {
            filterList.addAll(dislikeRecommend);
        }

        if (!CollectionUtils.isEmpty(exposureRecommend)) {
            filterList.addAll(exposureRecommend);
        }

        if (!CollectionUtils.isEmpty(contentRecommended)) {
            filterList.addAll(contentRecommended);
        }

        if (!CollectionUtils.isEmpty(hackUserRecommend)) {
            filterList.addAll(hackUserList(userId, redisTemplate, hackUserRecommend, channelId));
        }

        multiRecallResultBo.setFilterList(filterList);

        Set<String> graphicIds = new HashSet<>();
        Set<String> videoIds = new HashSet<>();

        if (!CollectionUtils.isEmpty(userCfGraphics)) {
            graphicIds.addAll(userCfGraphics.stream().map(ContentScoreVo::getContentId).filter(c -> !filterList.contains(c)).limit(limit).toList());
        }
        if (!CollectionUtils.isEmpty(userCfVideos)) {
            videoIds.addAll(userCfVideos.stream().map(ContentScoreVo::getContentId).filter(c -> !filterList.contains(c)).limit(limit).toList());
        }

        if (!CollectionUtils.isEmpty(itemCfGraphics)) {
            graphicIds.addAll(itemCfGraphics.stream().map(ContentScoreVo::getContentId).filter(c -> !filterList.contains(c)).limit(limit).toList());
        }
        if (!CollectionUtils.isEmpty(itemCfVideos)) {
            videoIds.addAll(itemCfVideos.stream().map(ContentScoreVo::getContentId).filter(c -> !filterList.contains(c)).limit(limit).toList());
        }

        if (!CollectionUtils.isEmpty(contentGraphics)) {
            graphicIds.addAll(contentGraphics.stream().map(ContentScoreVo::getContentId).filter(c -> !filterList.contains(c)).limit(limit).toList());
        }
        if (!CollectionUtils.isEmpty(contentVideos)) {
            videoIds.addAll(contentVideos.stream().map(ContentScoreVo::getContentId).filter(c -> !filterList.contains(c)).limit(limit).toList());
        }

        if (!CollectionUtils.isEmpty(userInterestGraphics)) {
            graphicIds.addAll(userInterestGraphics.stream().map(ContentScoreVo::getContentId).filter(c -> !filterList.contains(c)).limit(limit).toList());
        }
        if (!CollectionUtils.isEmpty(userInterestVideos)) {
            videoIds.addAll(userInterestVideos.stream().map(ContentScoreVo::getContentId).filter(c -> !filterList.contains(c)).limit(limit).toList());
        }

        if (!CollectionUtils.isEmpty(userFollowGraphics)) {
            graphicIds.addAll(userFollowGraphics.stream().filter(c -> !filterList.contains(c)).limit(limit).toList());
        }
        if (!CollectionUtils.isEmpty(userFollowVideos)) {
            videoIds.addAll(userFollowVideos.stream().filter(c -> !filterList.contains(c)).limit(limit).toList());
        }

        if (!CollectionUtils.isEmpty(hotGraphics)) {
            graphicIds.addAll(hotGraphics.stream().map(ContentScoreVo::getContentId).filter(c -> !filterList.contains(c)).limit(limit).toList());
        }
        if (!CollectionUtils.isEmpty(hotVideos)) {
            videoIds.addAll(hotVideos.stream().map(ContentScoreVo::getContentId).filter(c -> !filterList.contains(c)).limit(limit).toList());
        }

        List<String> graphicList = graphicIds.stream().distinct().toList();
        List<String> videoList = videoIds.stream().distinct().toList();

        multiRecallResultBo.setGraphicIds(graphicList);
        multiRecallResultBo.setVideoIds(videoList);
        log.warn("过滤逻辑 => userId:【{}】, 需要过滤的列表集合大小:【{}】, 过滤后图文列表大小:【{}】, 视频大小:【{}】, 总耗时:【{}】ms", userId, filterList.size(), graphicList.size(), videoList.size(), System.currentTimeMillis() - l);
    }


    public static void filterExposure(
            String userId,
            String channelId,
            MultiRecallResultBo multiRecallResultBo,
            RedisTemplate<String, String> redisTemplate,
            Set<String> contentRecommended,
            List<String> t1FilterIds
    ) {
        Set<String> filterList = new HashSet<>();
        Set<String> offlineRecommend = null;

        String offlineKey = Constants.REDIS_KEY_TODAY_CONTENT_OFFLINE + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String dislikeKey = Constants.REDIS_KEY_TODAY_CONTENT_USER_DISLIKE + userId + ":" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String exposureKey = Constants.REDIS_KEY_TODAY_CONTENT_USER_EXPOSURE + userId + ":" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String hackUser = Constants.REDIS_KEY_TODAY_CONTENT_USER_HACK + userId + ":" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        try {
            offlineRecommend = redisTemplate.opsForSet().members(offlineKey);
        } catch (Exception e) {
            String errorMsg = "T0过滤 => userId:%s, 查询redis下线内容失败".formatted(userId);
            log.error(errorMsg, e);
                e.printStackTrace();
        }
        Set<String> dislikeRecommend = null;
        try {
            dislikeRecommend = redisTemplate.opsForSet().members(dislikeKey);
        } catch (Exception e) {
            String errorMsg = "T0过滤 => userId:%s, 查询redis不喜欢内容失败".formatted(userId);
            log.error(errorMsg, e);
                e.printStackTrace();
        }
        Set<String> exposureRecommend = null;
        try {
            exposureRecommend = redisTemplate.opsForSet().members(exposureKey);
        } catch (Exception e) {
            String errorMsg = "T0过滤 => userId:%s, 查询redis曝光内容失败".formatted(userId);
            log.error(errorMsg, e);
                e.printStackTrace();
        }
        Set<String> hackUserRecommend = null;
        try {
            hackUserRecommend = redisTemplate.opsForSet().members(hackUser);
        } catch (Exception e) {
            String errorMsg = "T0过滤 => userId:%s, 查询redis曝光内容失败".formatted(userId);
            log.error(errorMsg, e);
                e.printStackTrace();
        }


        if (!CollectionUtils.isEmpty(offlineRecommend)) {
            filterList.addAll(offlineRecommend);
        }
        if (!CollectionUtils.isEmpty(t1FilterIds)) {
            filterList.addAll(t1FilterIds);
        }

        if (!CollectionUtils.isEmpty(dislikeRecommend)) {
            filterList.addAll(dislikeRecommend);
        }

        if (!CollectionUtils.isEmpty(exposureRecommend)) {
            filterList.addAll(exposureRecommend);
        }

        if (!CollectionUtils.isEmpty(contentRecommended)) {
            filterList.addAll(contentRecommended);
        }

        if (!CollectionUtils.isEmpty(hackUserRecommend)) {
            filterList.addAll(hackUserList(userId, redisTemplate, hackUserRecommend, channelId));
        }

        multiRecallResultBo.setFilterList(filterList);


        if (!CollectionUtils.isEmpty(filterList)) {
            List<HotContentInfoBo> newestGraphicDetails = multiRecallResultBo.getNewestGraphicDetails();
            List<HotContentInfoBo> newestVideoDetails = multiRecallResultBo.getNewestVideoDetails();
            List<HotContentInfoBo> ctrSortGraphicDetails = multiRecallResultBo.getCtrSortGraphicDetails();
            List<HotContentInfoBo> ctrSortVideoDetail = multiRecallResultBo.getCtrSortVideoDetails();

            List<HotContentInfoBo> newestGraphicDetailsNew;
            List<HotContentInfoBo> newestVideoDetailsNew;
            List<HotContentInfoBo> ctrSortGraphicDetailsNew;
            List<HotContentInfoBo> ctrSortVideoDetailNew;

            newestGraphicDetailsNew = newestGraphicDetails.stream().filter(o -> !filterList.contains(o.getContentId())).toList();
            newestVideoDetailsNew = newestVideoDetails.stream().filter(o -> !filterList.contains(o.getContentId())).toList();
            ctrSortGraphicDetailsNew = ctrSortGraphicDetails.stream().filter(o -> !filterList.contains(o.getContentId())).toList();
            ctrSortVideoDetailNew = ctrSortVideoDetail.stream().filter(o -> !filterList.contains(o.getContentId())).toList();

            multiRecallResultBo.setNewestGraphicDetails(newestGraphicDetailsNew);
            multiRecallResultBo.setNewestVideoDetails(newestVideoDetailsNew);
            multiRecallResultBo.setCtrSortGraphicDetails(ctrSortGraphicDetailsNew);
            multiRecallResultBo.setCtrSortVideoDetails(ctrSortVideoDetailNew);
        }
        log.warn("推荐池链路需过滤的列表集合size:【{}】", filterList.size());
    }


    public static Set<String> hackUserList(String userId, RedisTemplate<String, String> redisTemplate, Set<String> hackUserList, String channelId) {
        Set<String> cIds = new HashSet<>();
        List<Object> userList = new ArrayList<>(hackUserList);

        String hackUserGraphicKey = Constants.REDIS_KEY_RECOM_GRAPHIC_HOT_HASH_HACKUSER_PREFIX + channelId;
        String hackUserVideoKey = Constants.REDIS_KEY_RECOM_VIDEO_HOT_HASH_HACKUSER_PREFIX + channelId;
        List<Object> hackuserGraphicContents;
        List<String> hackUserGraphic = null;
        try {
            hackuserGraphicContents = redisTemplate.opsForHash().multiGet(hackUserGraphicKey, userList);
            if (!CollectionUtils.isEmpty(hackuserGraphicContents)) {
                hackUserGraphic = hackuserGraphicContents.stream().filter(Objects::nonNull).map(String::valueOf).flatMap(s -> Arrays.stream(s.split(","))).toList();
            }
        } catch (Exception e) {
            String errorMsg = "T0过滤 => userId:%s, redis查询黑名单用户发图文帖失败".formatted(userId);
            log.error(errorMsg, e);
                e.printStackTrace();
        }

        List<Object> hackuserVideoContents;
        List<String> hackUserVideo = null;
        try {
            hackuserVideoContents = redisTemplate.opsForHash().multiGet(hackUserVideoKey, userList);
            if (!CollectionUtils.isEmpty(hackuserVideoContents)) {
                hackUserVideo = hackuserVideoContents.stream().filter(Objects::nonNull).map(String::valueOf).flatMap(s -> Arrays.stream(s.split(","))).toList();
            }
        } catch (Exception e) {
            String errorMsg = "T0过滤 => userId:%s, redis查询黑名单用户发视频帖失败".formatted(userId);
            log.error(errorMsg, e);
                e.printStackTrace();
        }

        if (!CollectionUtils.isEmpty(hackUserGraphic)) {
            cIds.addAll(hackUserGraphic);
        }
        if (!CollectionUtils.isEmpty(hackUserVideo)) {
            cIds.addAll(hackUserVideo);
        }
        return cIds;

    }


}