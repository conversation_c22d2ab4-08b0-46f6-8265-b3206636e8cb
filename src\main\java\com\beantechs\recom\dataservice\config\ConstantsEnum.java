package com.beantechs.recom.dataservice.config;

import lombok.Getter;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Getter
public enum ConstantsEnum {

    RECALL_ASYNC_TASK_USER_GRAPHIC("userCF-graphic", "userCF-graphic", ""),
    RECALL_ASYNC_TASK_USER_VIDEO("userCF-video", "userCF-video", ""),
    RECALL_ASYNC_TASK_ITEM_GRAPHIC("itemCF-graphic", "itemCF-graphic", ""),
    RECALL_ASYNC_TASK_ITEM_VIDEO("itemCF-video", "itemCF-video", ""),
    RECALL_ASYNC_TASK_CONTENT_GRAPHIC("content-graphic", "content-graphic", ""),
    RECALL_ASYNC_TASK_CONTENT_VIDEO("content-video", "content-video", ""),
    RECALL_ASYNC_TASK_INTEREST_GRAPHIC("interest-tag-graphic", "interest-tag-graphic", ""),
    RECALL_ASYNC_TASK_INTEREST_VIDEO("interest-tag-video", "interest-tag-video", ""),
    RECALL_ASYNC_TASK_USERFOLLOW_GRAPHIC("userfollow-graphic", "userfollow-graphic", ""),
    RECALL_ASYNC_TASK_USERFOLLOW_VIDEO("userfollow-video", "userfollow-video", ""),
    RECALL_ASYNC_TASK_HOT_GRAPHIC("hot-graphic", "hot-graphic", ""),
    RECALL_ASYNC_TASK_HOT_VIDEO("hot-video", "hot-video", ""),
    RECALL_ASYNC_TASK_USERUNDERSTAND_GRAPHIC("user-understand-graphic", "user-understand-graphic", ""),
    RECALL_ASYNC_TASK_USERUNDERSTAND_VIDEO("user-understand-video", "user-understand-video", ""),
    RECALL_ASYNC_TASK_CHANNEL_TAG("channel-tag", "channel-tag", ""),
    ABGROUP_WEIGHT_FIELD_CONTENT_PROPORTION("weight", "graphic_vs_video", ""),
    ABGROUP_RERANK_FIELD_CONTENT_PROPORTION("re_rank", "graphic_vs_video", ""),
    ABGROUP_RERANK_FIELD_TAG_SCATTER("re_rank", "tag_break_up", ""),
    ABGROUP_RERANK_FIELD_FILL_RECENTLY("re_rank", "insert_new", "");

    private String field;
    private String value;
    private String desc;


    ConstantsEnum(String field, String value, String desc) {
        this.field = field;
        this.value = value;
        this.desc = desc;
    }
}
