package com.beantechs.recom.dataservice.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beantechs.recom.dataservice.config.Constants;
import com.beantechs.recom.dataservice.config.RecommendContextHolder;
import com.beantechs.recom.dataservice.entity.bo.RecommendContext;
import com.beantechs.recom.dataservice.entity.resp.RecommendRespContentInfo;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.opensearch.client.Request;
import org.opensearch.client.Response;
import org.opensearch.client.RestClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
@Slf4j
public class ConstantsService {

    protected final RedisTemplate<String, String> redisTemplate;
    protected final RestClient restClient;

    public ConstantsService(@Qualifier("stringRedisTemplate") RedisTemplate<String, String> redisTemplate, RestClient restClient) {
        this.redisTemplate = redisTemplate;
        this.restClient = restClient;
    }

    protected void channelConfigInit() {
        String userId = RecommendContextHolder.getContext().getMainReqParams().getUserId();
        String channelId = RecommendContextHolder.getContext().getMainReqParams().getChannelId();

        String channelConfig = null;
        try {
            channelConfig = redisTemplate.opsForValue().get(Constants.REDIS_KEY_RECOM_CHANNEL_IDS_CONFIG_PREFIX);
        } catch (Exception e) {
            String errorMsg = "读取redis频道配置 => 未获取到redis频道配置, userId:【%s】, channelId:【%s】, ".formatted(userId, channelId);
            log.error(errorMsg, e);
            e.printStackTrace();
            RecommendContextHolder.setErrorLogo();
        }
        RecommendContextHolder.setContextChannelConfigField(channelConfig);
    }


    /**
     * 检查用户是否有行为数据
     * 通过查询用户最近的行为记录来判断
     *
     * @return 是否有行为数据
     */
    protected boolean hasUserBehavior() {
        String channelId = RecommendContextHolder.getContext().getMainReqParams().getChannelId();
        String userId = RecommendContextHolder.getContext().getMainReqParams().getUserId();
        String dateStringT0 = LocalDateTime.now().minusHours(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH"));
        String behaviourT1 = Constants.REDIS_KEY_RECOM_T1_BEHAVIOUR_PREFIX + channelId;
        String behaviourT0 = Constants.REDIS_KEY_RECOM_T0_BEHAVIOUR_PREFIX + dateStringT0 + ":" + channelId;
        boolean hasKeyT1 = false;
        try {
            hasKeyT1 = redisTemplate.opsForHash().hasKey(behaviourT1, userId);
        } catch (Exception e) {
            String errorMsg = "userId不为空, 查询redis检查T1是否有用户行为失败, userId:【%s】, channelId:【%s】".formatted(userId, channelId);
            log.error(errorMsg, e);
            e.printStackTrace();
            RecommendContextHolder.setErrorLogo();
        }

        if (hasKeyT1) {
            return true;
        }

        boolean hasKeyT0 = false;
        try {
            hasKeyT0 = redisTemplate.opsForHash().hasKey(behaviourT0, userId);
        } catch (Exception e) {
            String errorMsg = "userId不为空, 查询redis检查T0是否有用户行为失败, userId:【%s】, channelId:【%s】".formatted(userId, channelId);
            log.error(errorMsg, e);
            e.printStackTrace();
            RecommendContextHolder.setErrorLogo();
        }

        return hasKeyT0;
    }

    protected boolean hasInterestTag(String userId, String channelId) {
        boolean hasInterestTag = false;
        String labelKey = Constants.REDIS_KEY_RECOM_USER_SELECT_INTEREST_LABEL + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + ":" + userId;
        String label = null;
        try {
            label = redisTemplate.opsForValue().get(labelKey);
        } catch (Exception e) {
            String errorMsg = "查询用户兴趣标签 => userId:【%s】, channelId:【%s】, 查询redis兴趣标签失败, redis key:【%s】".formatted(userId, channelId, labelKey);
            log.error(errorMsg, e);
            e.printStackTrace();
            RecommendContextHolder.setErrorLogo();
        }
        if (StringUtils.hasText(label)) {
            hasInterestTag = true;
        }
        RecommendContextHolder.setContextHashInterestTagField(hasInterestTag);
        return hasInterestTag;
    }


    public void filterCollectionsInit() {
        Set<String> offlineCollections = new HashSet<>();
        Set<String> dislikeCollections = new HashSet<>();
        Set<String> exposureCollections = new HashSet<>();
        Set<String> userBlockCollections = new HashSet<>();
        Set<String> alreadyRecommendCollections = new HashSet<>();

        filterCollectionsGet(
                offlineCollections,
                dislikeCollections,
                exposureCollections,
                userBlockCollections,
                alreadyRecommendCollections
        );

        RecommendContext.mergeFilterCollections(
                getT1GraphicFilterIds(),
                getT1VideoFilterIds(),
                offlineCollections,
                dislikeCollections,
                exposureCollections,
                userBlockCollections,
                alreadyRecommendCollections
        );
    }


    protected void recommendStrategyInit() {
        List<String> contentTagList = RecommendContextHolder.getContext().getMainReqParams().getContentTagList();
        String userId = RecommendContextHolder.getContext().getMainReqParams().getUserId();
        String channelId = RecommendContextHolder.getContext().getMainReqParams().getChannelId();
        String channelConfig = RecommendContextHolder.getContext().getChannelConfig();

        String strategyWay = "popular";
        if (!channelId.equals("-1")) {
            // 如果参数中的频道没有配置, 代表走冷启动热门召回
            if (StringUtils.hasText(channelConfig)) {
                JSONObject jsonObject = JSON.parseObject(channelConfig);
                if (jsonObject.containsKey(channelId)) {
                    List<String> list = jsonObject.getJSONArray(channelId).stream().map(String::valueOf).toList();
                    if (org.apache.commons.collections4.CollectionUtils.isEqualCollection(list, contentTagList)) {
                        if (hasUserBehavior()) {
                            strategyWay = "full";
                        } else {
                            boolean hasInterestTag = hasInterestTag(userId, channelId);
                            if (hasInterestTag) {
                                strategyWay = "label";
                            }
                        }
                    } else {
                        strategyWay = "channel-tag";
                    }
                } else {
                    strategyWay = "channel-tag";
                }
            } else {
                strategyWay = "channel-tag";
            }
        } else {
            if (hasUserBehavior()) {
                strategyWay = "full";
            } else {
                boolean hasInterestTag = hasInterestTag(userId, channelId);
                if (hasInterestTag) {
                    strategyWay = "label";
                }
            }
        }
        RecommendContextHolder.setContextStrategyField(strategyWay);
    }


    private void filterCollectionsGet(Set<String> offlineCollections, Set<String> dislikeCollections, Set<String> exposureCollections, Set<String> userBlockCollections, Set<String> alreadyRecommendCollections) {
        String channelId = RecommendContextHolder.getContext().getMainReqParams().getChannelId();
        String userId = RecommendContextHolder.getContext().getMainReqParams().getUserId();

        String offlineKey = Constants.REDIS_KEY_TODAY_CONTENT_OFFLINE + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String dislikeKey = Constants.REDIS_KEY_TODAY_CONTENT_USER_DISLIKE + userId + ":" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String exposureKey = Constants.REDIS_KEY_TODAY_CONTENT_USER_EXPOSURE + userId + ":" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String userBlockKey = Constants.REDIS_KEY_TODAY_CONTENT_USER_HACK + userId + ":" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String alreadyRecommendedKey = Constants.REDIS_KEY_RECOM_CONTENTS_ALREADY_RECOM + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + ":" + userId;

        try {
            offlineCollections.addAll(redisTemplate.opsForSet().members(offlineKey));
        } catch (Exception e) {
            String errorMsg = "获取下线过滤内容 => userId:【%s】, channelId：【%s】, 查询redis下线内容失败".formatted(userId, channelId);
            log.error(errorMsg, e);
            e.printStackTrace();
            RecommendContextHolder.setErrorLogo();
        }
        try {
            dislikeCollections.addAll(redisTemplate.opsForSet().members(dislikeKey));
        } catch (Exception e) {
            String errorMsg = "获取不喜欢过滤内容 => userId:【%s】, channelId：【%s】, 查询redis不喜欢内容失败".formatted(userId, channelId);
            log.error(errorMsg, e);
            e.printStackTrace();
            RecommendContextHolder.setErrorLogo();
        }
        try {
            exposureCollections.addAll(redisTemplate.opsForSet().members(exposureKey));
        } catch (Exception e) {
            String errorMsg = "获取曝光过滤内容 => userId:【%s】, channelId：【%s】, 查询redis曝光内容失败".formatted(userId, channelId);
            log.error(errorMsg, e);
            e.printStackTrace();
            RecommendContextHolder.setErrorLogo();
        }
        try {
            Set<String> userBlockIdsCollections = redisTemplate.opsForSet().members(userBlockKey);
            if (!CollectionUtils.isEmpty(userBlockIdsCollections)) {
                userBlockCollections.addAll(hackUserList(userId, channelId, userBlockIdsCollections));
            }
        } catch (Exception e) {
            String errorMsg = "获取拉黑的用户发帖内容 => userId:【%s】, channelId：【%s】, 查询redis用户拉黑的用户发帖内容失败".formatted(userId, channelId);
            log.error(errorMsg, e);
            e.printStackTrace();
            RecommendContextHolder.setErrorLogo();
        }


        try {
            alreadyRecommendCollections.addAll(redisTemplate.opsForSet().members(alreadyRecommendedKey));
        } catch (Exception e) {
            String errorMsg = "获取用户已推内容 => userId:【%s】, channelId：【%s】, 查询redis获取用户已推内容失败".formatted(userId, channelId);
            log.error(errorMsg, e);
            e.printStackTrace();
            RecommendContextHolder.setErrorLogo();
        }
    }


    private Set<String> getT1GraphicFilterIds() {
        String channelId = RecommendContextHolder.getContext().getMainReqParams().getChannelId();
        String userId = RecommendContextHolder.getContext().getMainReqParams().getUserId();

        String graphicKey = Constants.REDIS_KEY_RECOM_USER_T1_GRAPHIC_FILTER_PREFIX;

        String userGraphicFilter = null;
        List<String> list = List.of();
        try {
            userGraphicFilter = (String) redisTemplate.opsForHash().get(graphicKey, userId);
        } catch (Exception e) {
            String errorMsg = "获取T1计算的图文过滤内容, userId:【%s】, channelId:【%s】, 查询T1图文过滤内容失败".formatted(userId, channelId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }
        if (StringUtils.hasText(userGraphicFilter)) {
            list = Arrays.stream(userGraphicFilter.split(",")).filter(StringUtils::hasText).toList();
        }
        return new HashSet<>(list);
    }


    private Set<String> getT1VideoFilterIds() {
        String channelId = RecommendContextHolder.getContext().getMainReqParams().getChannelId();
        String userId = RecommendContextHolder.getContext().getMainReqParams().getUserId();

        String videoKey = Constants.REDIS_KEY_RECOM_USER_T1_VIDEO_FILTER_PREFIX;

        String userVideoFilter = null;
        List<String> list = List.of();
        try {
            userVideoFilter = (String) redisTemplate.opsForHash().get(videoKey, userId);
        } catch (Exception e) {
            String errorMsg = "获取T1计算的视频过滤内容, userId:【%s】, channelId:【%s】, 查询T1视频过滤内容失败".formatted(userId, channelId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }
        if (StringUtils.hasText(userVideoFilter)) {
            list = Arrays.stream(userVideoFilter.split(",")).filter(StringUtils::hasText).toList();
        }
        return new HashSet<>(list);
    }


    public Set<String> hackUserList(String userId, String channelId, Set<String> hackUserList) {
        Set<String> cIds = new HashSet<>();
        List<Object> userList = new ArrayList<>(hackUserList);

        String hackUserGraphicKey = Constants.REDIS_KEY_RECOM_GRAPHIC_HOT_HASH_HACKUSER_PREFIX + channelId;
        String hackUserVideoKey = Constants.REDIS_KEY_RECOM_VIDEO_HOT_HASH_HACKUSER_PREFIX + channelId;
        List<Object> hackuserGraphicContents;
        List<String> hackUserGraphic = null;
        try {
            hackuserGraphicContents = redisTemplate.opsForHash().multiGet(hackUserGraphicKey, userList);
            if (!CollectionUtils.isEmpty(hackuserGraphicContents)) {
                hackUserGraphic = hackuserGraphicContents.stream().filter(Objects::nonNull).map(String::valueOf).flatMap(s -> Arrays.stream(s.split(","))).toList();
            }
        } catch (Exception e) {
            String errorMsg = "根据用户黑名单过滤 => userId:【%s】, channelId：【%s】, 根据用户黑名单查询黑名单用户发帖失败".formatted(userId, channelId);
            log.error(errorMsg, e);
            e.printStackTrace();
            RecommendContextHolder.setErrorLogo();
        }

        List<Object> hackuserVideoContents;
        List<String> hackUserVideo = null;
        try {
            hackuserVideoContents = redisTemplate.opsForHash().multiGet(hackUserVideoKey, userList);
            if (!CollectionUtils.isEmpty(hackuserVideoContents)) {
                hackUserVideo = hackuserVideoContents.stream().filter(Objects::nonNull).map(String::valueOf).flatMap(s -> Arrays.stream(s.split(","))).toList();
            }
        } catch (Exception e) {
            String errorMsg = "T0过滤 => userId:%s, redis查询黑名单用户发视频帖失败".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
            RecommendContextHolder.setErrorLogo();
        }

        if (!CollectionUtils.isEmpty(hackUserGraphic)) {
            cIds.addAll(hackUserGraphic);
        }
        if (!CollectionUtils.isEmpty(hackUserVideo)) {
            cIds.addAll(hackUserVideo);
        }
        return cIds;

    }


    public List<RecommendRespContentInfo> recommendHomePageContentFinal(String userId, Integer size) {
        try {
            // 构建 must 子句
            List<Map<String, Object>> mustClauses = new ArrayList<>();

            // 构建 car_brand_name_new 字段存在查询
            Map<String, Object> carBrandExists = new HashMap<>();
            carBrandExists.put("field", "car_brand_name_new");
            Map<String, Object> carBrandExistsQuery = new HashMap<>();
            carBrandExistsQuery.put("exists", carBrandExists);
            mustClauses.add(carBrandExistsQuery);

            // 构建 car_model_name_new 字段存在查询
            Map<String, Object> carModelExists = new HashMap<>();
            carModelExists.put("field", "car_model_name_new");
            Map<String, Object> carModelExistsQuery = new HashMap<>();
            carModelExistsQuery.put("exists", carModelExists);
            mustClauses.add(carModelExistsQuery);

            // 构建 car_brand_name_new 非空 must_not 子句
            List<Map<String, Object>> carBrandMustNotClauses = new ArrayList<>();
            Map<String, Object> carBrandTerm = new HashMap<>();
            carBrandTerm.put("car_brand_name_new.keyword", "");
            Map<String, Object> carBrandTermQuery = new HashMap<>();
            carBrandTermQuery.put("term", carBrandTerm);
            carBrandMustNotClauses.add(carBrandTermQuery);
            Map<String, Object> carBrandInnerBoolQuery = new HashMap<>();
            carBrandInnerBoolQuery.put("must_not", carBrandMustNotClauses);
            mustClauses.add(Collections.singletonMap("bool", carBrandInnerBoolQuery));

            // 构建 car_model_name_new 非空 must_not 子句
            List<Map<String, Object>> carModelMustNotClauses = new ArrayList<>();
            Map<String, Object> carModelTerm = new HashMap<>();
            carModelTerm.put("car_model_name_new.keyword", "");
            Map<String, Object> carModelTermQuery = new HashMap<>();
            carModelTermQuery.put("term", carModelTerm);
            carModelMustNotClauses.add(carModelTermQuery);
            Map<String, Object> carModelInnerBoolQuery = new HashMap<>();
            carModelInnerBoolQuery.put("must_not", carModelMustNotClauses);
            mustClauses.add(Collections.singletonMap("bool", carModelInnerBoolQuery));

            // 构建外部 bool 查询（must）
            Map<String, Object> outerBoolQuery = new HashMap<>();
            outerBoolQuery.put("must", mustClauses);

            // 构建主查询
            Map<String, Object> mainQuery = new HashMap<>();
            mainQuery.put("bool", outerBoolQuery);

            // 构建排序
            Map<String, Object> scoreRankSort = new HashMap<>();
            Map<String, String> scoreRankOrder = new HashMap<>();
            scoreRankOrder.put("order", "asc");
            scoreRankSort.put("score_rank", scoreRankOrder);
            List<Map<String, Object>> sortList = new ArrayList<>();
            sortList.add(scoreRankSort);

            // 构建 _source
            List<String> sourceFields = Arrays.asList("content_id", "post_type", "score_rank");

            // 构建完整的查询体
            Map<String, Object> queryBody = new HashMap<>();
            queryBody.put("query", mainQuery);
            queryBody.put("sort", sortList);
            queryBody.put("size", 100);
            queryBody.put("_source", sourceFields);

            // 将查询体转换为 JSON 字符串
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonBody = objectMapper.writeValueAsString(queryBody);

            // 创建请求
            Request request = new Request("GET", "/hot_t0t1_result_es/_search");
            request.setJsonEntity(jsonBody);

            // 执行请求
            Response response = restClient.performRequest(request);
            JSONObject responseObj = new ObjectMapper().readValue(response.getEntity().getContent(), JSONObject.class);
            List<Object> jsonArray = responseObj.getJSONObject("hits").getJSONArray("hits").stream().toList();

            // 打乱顺序随机取10条
            List<Object> copyList = new ArrayList<>(jsonArray);
            Collections.shuffle(copyList);

            return copyList.stream()
                    .limit(size)
                    .map(o -> JSONObject.parseObject(JSON.toJSONString(o)).getJSONObject("_source"))
                    .map(o -> RecommendRespContentInfo.build(o.getString("content_id"), o.getString("post_type").equals("1") ? "graphic" : "video"))
                    .toList();

        } catch (Exception e) {
            String errorMsg = "最终最终兜底 => 兜底失败, 查询es失败, userId:【%s】".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }
        return null;
    }


    public List<RecommendRespContentInfo> recommendOtherPageContentFinal(String userId, Integer size, List<String> contentTagIds) {
        try {
            // 构建 terms 查询
            Map<String, Object> termsQuery = new HashMap<>();
            termsQuery.put("content_tag_id", contentTagIds);
            Map<String, Object> termsClause = new HashMap<>();
            termsClause.put("terms", termsQuery);

            // 构建内部 bool 查询（must）
            List<Map<String, Object>> mustClauses = new ArrayList<>();
            mustClauses.add(termsClause);
            Map<String, Object> innerBoolQuery = new HashMap<>();
            innerBoolQuery.put("must", mustClauses);

            // 构建主查询
            Map<String, Object> mainQuery = new HashMap<>();
            mainQuery.put("bool", innerBoolQuery);

            // 构建排序
            Map<String, Object> sortField = new HashMap<>();
            sortField.put("order", "desc");
            List<Map<String, Object>> sort = new ArrayList<>();
            sort.add(Collections.singletonMap("hot_score", sortField));

            // 构建 _source
            List<String> sourceFields = Arrays.asList("content_id", "post_type", "score_rank");

            // 构建完整的查询体
            Map<String, Object> queryBody = new HashMap<>();
            queryBody.put("query", mainQuery);
            queryBody.put("size", 100);
            queryBody.put("sort", sort);
            queryBody.put("_source", sourceFields);

            // 将查询体转换为 JSON 字符串
            ObjectMapper objectMapper = new ObjectMapper();
            String jsonBody = objectMapper.writeValueAsString(queryBody);

            // 创建请求
            Request request = new Request("GET", "/hot_t0t1_result_es/_search");
            request.setJsonEntity(jsonBody);

            // 执行请求
            Response response = restClient.performRequest(request);
            HttpEntity entity = response.getEntity();
            JSONObject responseObj = new ObjectMapper().readValue(entity.getContent(), JSONObject.class);
            List<Object> jsonArray = responseObj.getJSONObject("hits").getJSONArray("hits").stream().toList();

            // 打乱顺序随机取10条
            List<Object> copyList = new ArrayList<>(jsonArray);
            Collections.shuffle(copyList);
            return copyList.stream()
                    .limit(size)
                    .map(o -> JSONObject.parseObject(JSON.toJSONString(o)).getJSONObject("_source"))
                    .sorted(Comparator.comparing(jsonObject -> jsonObject.getDouble("score_rank")))
                    .map(o -> RecommendRespContentInfo.build(o.getString("content_id"), o.getString("post_type").equals("1") ? "graphic" : "video"))
                    .toList();

        } catch (Exception e) {
            String errorMsg = "最终最终兜底 => 频道页兜底失败, 查询es失败, userId:【%s】".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }
        return null;
    }

}
