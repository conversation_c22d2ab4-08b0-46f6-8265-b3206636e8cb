package com.beantechs.recom.dataservice.service.handle;

import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.beantechs.recom.dataservice.config.CommonUtils;
import com.beantechs.recom.dataservice.config.Constants;
import com.beantechs.recom.dataservice.config.RecommendContextHolder;
import com.beantechs.recom.dataservice.entity.bo.ContentScoreVo;
import com.beantechs.recom.dataservice.entity.bo.ContentTypeScoreVo;
import com.beantechs.recom.dataservice.entity.bo.RecommendContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 算法精排处理器
 */
@Slf4j
public class AlgModelRankHandler extends ModuleHandle {
    @Override
    public void handle() {
        RecommendContext context = RecommendContextHolder.getContext();
        String userId = context.getMainReqParams().getUserId();
        String channelId = context.getMainReqParams().getChannelId();
        Integer pageSize = context.getMainReqParams().getPageSize();
        List<String> fusionGraphicIds = context.getFusionGraphicIds();
        List<String> fusionVideoIds = context.getFusionVideoIds();

        RedisTemplate<String, String> redisTemplate = SpringUtil.getBean("stringRedisTemplate");

        // 转换为模型特征进行模型排序
        List<List<Double>> features = new ArrayList<>();
        int graphicSize = 0;

        CompletableFuture<List<List<Double>>> graphicFuture = null;
        CompletableFuture<List<List<Double>>> videoFuture = null;

        if (!CollectionUtils.isEmpty(fusionGraphicIds)) {
            graphicFuture = graphicFutureGet(userId, fusionGraphicIds, redisTemplate, context);
        }
        if (!CollectionUtils.isEmpty(fusionVideoIds)) {
            videoFuture = videoFutureGet(userId, fusionVideoIds, redisTemplate, context);
        }

        CompletableFuture.allOf(graphicFuture, videoFuture).join();

        try {
            List<List<Double>> graphics = graphicFuture.get();
            graphicSize = graphics.size();
            features.addAll(graphics);
        } catch (Exception e) {
            String errorMsg = "CTR精排获取图文特征失败, userId：【%s】, channelId：【%s】".formatted(
                    context.getMainReqParams().getUserId(), context.getMainReqParams().getChannelId());
            log.error(errorMsg, e);
            e.printStackTrace();
        }

        try {
            features.addAll(videoFuture.get());
        } catch (Exception e) {
            String errorMsg = "CTR精排获取视频特征失败, userId：【%s】, channelId：【%s】".formatted(
                    context.getMainReqParams().getUserId(), context.getMainReqParams().getChannelId());
            log.error(errorMsg, e);
            e.printStackTrace();
        }


        List<Double> array = null;
        if (!CollectionUtils.isEmpty(features)) {
            JSONObject json = new JSONObject();
            json.put("features", features);
            json.put("userId", userId);

            Map<String, List<String>> header = new HashMap<>();
            header.put("Content-Type", Collections.singletonList("application/json"));

            String post = null;
            try {
                post = HttpRequest
                        .post("http://gwm-app-recom-ctr-model-service:5000/ctr-sort/predict")
//                        .post("http://127.0.0.1:5000/ctr-sort/predict")
                        .header(header, true)
                        .body(JSON.toJSONString(json))
                        .execute()
                        .body();
                if (StringUtils.hasText(post)) {
                    array = JSON.parseArray(JSON.parseObject(post).getString("data"), Double.class);
                }
            } catch (Exception e) {
                String errorMsg = "新接口CTR排序异常, userId:【%s】,channelId：【%s】".formatted(userId, channelId);
                log.error(errorMsg, e);
                e.printStackTrace();
            }


            List<Double> graphicSort;
            List<Double> videoSort;
            if (!CollectionUtils.isEmpty(array)) {
                try {
                    graphicSort = array.subList(0, graphicSize);
                } catch (Exception e) {
                    String errorMsg = "CTR切割图文列表异常, userId:【%s】,channelId：【%s】".formatted(userId, channelId);
                    log.warn(errorMsg);
                    graphicSort = new ArrayList<>();
                }
                try {
                    videoSort = array.subList(graphicSize, array.size());
                } catch (Exception e) {
                    String errorMsg = "CTR切割视频列表异常, userId:【%s】,channelId：【%s】".formatted(userId, channelId);
                    log.warn(errorMsg);
                    videoSort = new ArrayList<>();
                }
            } else {
                graphicSort = new ArrayList<>();
                videoSort = new ArrayList<>();
            }

            List<ContentScoreVo> graphicScoreVoList = new ArrayList<>();
            List<ContentScoreVo> videoScoreVoList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(fusionGraphicIds)) {
                for (int i = 0; i < fusionGraphicIds.size(); i++) {
                    ContentScoreVo contentScoreVo = new ContentScoreVo();
                    contentScoreVo.setContentId(fusionGraphicIds.get(i));
                    if (CollectionUtils.isEmpty(graphicSort)) {
                        contentScoreVo.setScore(String.valueOf(0.00));
                    } else {
                        if (Objects.isNull(graphicSort.get(i))) {
                            contentScoreVo.setScore(String.valueOf(0.00));
                        } else {
                            contentScoreVo.setScore(String.valueOf(graphicSort.get(i)));
                        }
                    }
                    graphicScoreVoList.add(contentScoreVo);
                }
            }

            if (!CollectionUtils.isEmpty(fusionVideoIds)) {
                for (int i = 0; i < fusionVideoIds.size(); i++) {
                    ContentScoreVo contentScoreVo = new ContentScoreVo();
                    contentScoreVo.setContentId(fusionVideoIds.get(i));
                    if (CollectionUtils.isEmpty(videoSort)) {
                        contentScoreVo.setScore(String.valueOf(0.00));
                    } else {
                        if (Objects.isNull(videoSort.get(i))) {
                            contentScoreVo.setScore(String.valueOf(0.00));
                        } else {
                            contentScoreVo.setScore(String.valueOf(videoSort.get(i)));
                        }
                    }
                    videoScoreVoList.add(contentScoreVo);
                }
            }


            List<ContentTypeScoreVo> CTRFusionList = CommonUtils.mergeMultipleLists(
                    graphicScoreVoList.stream().map(o -> ContentTypeScoreVo.typeOf(o, "graphic")).toList(),
                    videoScoreVoList.stream().map(o -> ContentTypeScoreVo.typeOf(o, "graphic")).toList()
            ).stream().sorted((o1, o2) -> {
                Double o1v = Double.parseDouble(o1.getScore());
                Double o2v = Double.parseDouble(o2.getScore());
                return o2v.compareTo(o1v);
            }).toList();


            List<String> CTRSortGraphicList = graphicScoreVoList.stream().sorted((o1, o2) -> {
                Double o1v = Double.parseDouble(o1.getScore());
                Double o2v = Double.parseDouble(o2.getScore());
                return o2v.compareTo(o1v);
            }).map(ContentScoreVo::getContentId).toList();

            List<String> CTRSortVideoList = videoScoreVoList.stream().sorted((o1, o2) -> {
                Double o1v = Double.parseDouble(o1.getScore());
                Double o2v = Double.parseDouble(o2.getScore());
                return o2v.compareTo(o1v);
            }).map(ContentScoreVo::getContentId).toList();

            context.setContentTypeListBackUp(CTRFusionList.stream().limit(pageSize).map(ContentTypeScoreVo::getContentType).toList());
            context.setCTRSortGraphicList(CTRSortGraphicList.stream().toList());
            context.setCTRSortVideoList(CTRSortVideoList.stream().toList());
        } else {
            List<ContentTypeScoreVo> fusionList = CommonUtils.mergeMultipleLists(
                    fusionGraphicIds.stream().map(o -> ContentTypeScoreVo.typeScoreVoOf(o, "graphic")).toList(),
                    fusionVideoIds.stream().map(o -> ContentTypeScoreVo.typeScoreVoOf(o, "graphic")).toList()
            );
            Collections.shuffle(fusionList);
            context.setContentTypeListBackUp(fusionList.stream().limit(pageSize).map(ContentTypeScoreVo::getContentType).toList());
            context.setCTRSortGraphicList(fusionGraphicIds);
            context.setCTRSortVideoList(fusionVideoIds);
        }

        RecommendContextHolder.setContext(context);

        if (nextHandler != null) {
            nextHandler.handle();
        }
    }

    private CompletableFuture<List<List<Double>>> videoFutureGet(String userId, List<String> fusionVideoIds, RedisTemplate<String, String> redisTemplate, RecommendContext context) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return videoVectorGet(userId, fusionVideoIds, redisTemplate);
            } catch (Exception e) {
                String errorMsg = "CTR精排获取视频特征失败, userId：【%s】, channelId：【%s】".formatted(
                        context.getMainReqParams().getUserId(), context.getMainReqParams().getChannelId());
                log.error(errorMsg, e);
                e.printStackTrace();
                return new ArrayList<>();
            }
        });
    }

    private CompletableFuture<List<List<Double>>> graphicFutureGet(String userId, List<String> fusionGraphicIds, RedisTemplate<String, String> redisTemplate, RecommendContext context) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return graphicVectorGet(userId, fusionGraphicIds, redisTemplate);
            } catch (Exception e) {
                String errorMsg = "CTR精排获取图文特征失败, userId：【%s】, channelId：【%s】".formatted(
                        context.getMainReqParams().getUserId(), context.getMainReqParams().getChannelId());
                log.error(errorMsg, e);
                e.printStackTrace();
                return new ArrayList<>();
            }
        });
    }


    /**
     * 获取图文内容特征
     */
    private List<List<Double>> graphicVectorGet(String userId, List<String> graphicIds, RedisTemplate<String, String> redisTemplate) {
        String userKey = Constants.REDIS_KEY_RECOM_USER_GRAPHIC_FEATURE_PREFIX;
        String contentKey = Constants.REDIS_KEY_RECOM_CONTENT_FEATURE_PREFIX;

        List<List<Double>> features = new ArrayList<>();
        List<Double> userFeature = new ArrayList<>();
        try {
            String userFeatureStr = redisTemplate.opsForValue().get(userKey + ":" + userId);
            if (StringUtils.hasText(userFeatureStr)) {
                JSONObject jsonObject = JSON.parseObject(userFeatureStr);
                userFeature.add(Double.valueOf(jsonObject.getOrDefault("register_day", 0.00).toString()));
                userFeature.add(Double.valueOf(jsonObject.getOrDefault("age", 33.00).toString()));
                userFeature.add(Double.valueOf(jsonObject.getOrDefault("fans_cnt", 0.00).toString()));
                userFeature.add(Double.valueOf(jsonObject.getOrDefault("user_select_app_tag", 0.00).toString()));
                userFeature.add(Double.valueOf(jsonObject.getOrDefault("sex", 2.00).toString()));
                userFeature.add(Double.valueOf(jsonObject.getOrDefault("user_select_car_scene", 0.00).toString()));
                userFeature.add(Double.valueOf(jsonObject.getOrDefault("user_select_price_range", 0.00).toString()));
                userFeature.add(Double.valueOf(jsonObject.getOrDefault("user_select_drive_mode", 0.00).toString()));
                userFeature.add(Double.valueOf(jsonObject.getOrDefault("user_recent_3d_behaviours_ctr", 0.00).toString()));
                userFeature.add(Double.valueOf(jsonObject.getOrDefault("user_recent_7d_behaviours_ctr", 0.00).toString()));
                userFeature.add(Double.valueOf(jsonObject.getOrDefault("user_recent_7d_behaviours_brand_name_set", 0.00).toString()));
                userFeature.add(Double.valueOf(jsonObject.getOrDefault("user_recent_7d_most_ctr_brand_name", 0.00).toString()));
                userFeature.add(Double.valueOf(jsonObject.getOrDefault("user_recent_7d_behaviours_cnt", 0.00).toString()));
                userFeature.add(Double.valueOf(jsonObject.getOrDefault("user_recent_7d_behaviours_topic_id_set", 0.00).toString()));
            } else {
                userFeature.add(0.00);
                userFeature.add(33.00);
                userFeature.add(0.00);
                userFeature.add(0.00);
                userFeature.add(2.00);
                userFeature.add(0.00);
                userFeature.add(0.00);
                userFeature.add(0.00);
                userFeature.add(0.00);
                userFeature.add(0.00);
                userFeature.add(0.00);
                userFeature.add(0.00);
                userFeature.add(0.00);
                userFeature.add(0.00);
            }
        } catch (Exception e) {
            String errorMsg = ("查询用户图文特征, 查询redis特征失败 userId:【%s】").formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }

        if (!CollectionUtils.isEmpty(graphicIds)) {
            try {
                List<String> contentFeaturesObj = redisTemplate.opsForValue().multiGet(graphicIds.stream().map(o -> contentKey + ":" + o).toList());
                if (!CollectionUtils.isEmpty(contentFeaturesObj)) {
                    for (int i = 0; i < graphicIds.size(); i++) {
                        if (Objects.nonNull(contentFeaturesObj.get(i))) {
                            JSONObject jsonObject = JSON.parseObject(contentFeaturesObj.get(i));
                            List<Double> contentFeature = new ArrayList<>(userFeature);
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("content_recent_3d_behaviours_ctr", 0.00).toString()));
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("content_recent_3d_behaviours_cnt", 0.00).toString()));
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("content_recent_3d_click_cnt", 0.00).toString()));
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("content_recent_3d_like_cnt", 0.00).toString()));
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("content_recent_7d_behaviours_ctr", 0.00).toString()));
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("content_recent_7d_behaviours_cnt", 0.00).toString()));
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("content_recent_7d_click_cnt", 0.00).toString()));
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("content_recent_7d_like_cnt", 0.00).toString()));
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("topic_kmeans_label", 0.00).toString()));
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("publisher", 0.00).toString()));
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("publish_day", 0.00).toString()));
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("car_model_name_new", 0.00).toString()));
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("topic_cnt", 0.00).toString()));
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("post_app_tag", 0.00).toString()));
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("content_type", 0.00).toString()));
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("car_brand_name_new", 0.00).toString()));
                            features.add(contentFeature);
                        } else {
                            List<Double> contentFeature = new ArrayList<>(userFeature);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            features.add(contentFeature);
                        }
                    }
                } else {
                    for (int i = 0; i < graphicIds.size(); i++) {
                        List<Double> contentFeature = new ArrayList<>(userFeature);
                        contentFeature.add(0.00);
                        contentFeature.add(0.00);
                        contentFeature.add(0.00);
                        contentFeature.add(0.00);
                        contentFeature.add(0.00);
                        contentFeature.add(0.00);
                        contentFeature.add(0.00);
                        contentFeature.add(0.00);
                        contentFeature.add(0.00);
                        contentFeature.add(0.00);
                        contentFeature.add(0.00);
                        contentFeature.add(0.00);
                        contentFeature.add(0.00);
                        contentFeature.add(0.00);
                        contentFeature.add(0.00);
                        contentFeature.add(0.00);
                        features.add(contentFeature);
                    }
                }
            } catch (Exception e) {
                String errorMsg = ("查询内容图文特征, 查询redis特征失败 userId:【%s】").formatted(userId);
                log.error(errorMsg, e);
                e.printStackTrace();
            }
        }
        // 组合特征
        return features;
    }

    /**
     * 获取视频内容特征
     */
    private List<List<Double>> videoVectorGet(String userId, List<String> videosIds, RedisTemplate<String, String> redisTemplate) {
        String userFeatureKey = Constants.REDIS_KEY_RECOM_USER_VIDEO_FEATURE_PREFIX;
        String contentFeatureKey = Constants.REDIS_KEY_RECOM_CONTENT_FEATURE_PREFIX;

        List<List<Double>> videoFeatures = new ArrayList<>();

        List<Double> userFeature = new ArrayList<>();
        try {
            String userFeatureStr = redisTemplate.opsForValue().get(userFeatureKey + ":" + userId);
            if (StringUtils.hasText(userFeatureStr)) {
                JSONObject jsonObject = JSON.parseObject(userFeatureStr);
                userFeature.add(Double.valueOf(jsonObject.getOrDefault("register_day", 0.00).toString()));
                userFeature.add(Double.valueOf(jsonObject.getOrDefault("age", 33.00).toString()));
                userFeature.add(Double.valueOf(jsonObject.getOrDefault("fans_cnt", 0.00).toString()));
                userFeature.add(Double.valueOf(jsonObject.getOrDefault("user_select_app_tag", 0.00).toString()));
                userFeature.add(Double.valueOf(jsonObject.getOrDefault("sex", 2.00).toString()));
                userFeature.add(Double.valueOf(jsonObject.getOrDefault("user_select_car_scene", 0.00).toString()));
                userFeature.add(Double.valueOf(jsonObject.getOrDefault("user_select_price_range", 0.00).toString()));
                userFeature.add(Double.valueOf(jsonObject.getOrDefault("user_select_drive_mode", 0.00).toString()));
                userFeature.add(Double.valueOf(jsonObject.getOrDefault("user_recent_3d_behaviours_ctr", 0.00).toString()));
                userFeature.add(Double.valueOf(jsonObject.getOrDefault("user_recent_7d_behaviours_ctr", 0.00).toString()));
                userFeature.add(Double.valueOf(jsonObject.getOrDefault("user_recent_7d_behaviours_brand_name_set", 0.00).toString()));
                userFeature.add(Double.valueOf(jsonObject.getOrDefault("user_recent_7d_most_ctr_brand_name", 0.00).toString()));
                userFeature.add(Double.valueOf(jsonObject.getOrDefault("user_recent_7d_behaviours_cnt", 0.00).toString()));
                userFeature.add(Double.valueOf(jsonObject.getOrDefault("user_recent_7d_behaviours_topic_id_set", 0.00).toString()));
            } else {
                userFeature.add(0.00);
                userFeature.add(33.00);
                userFeature.add(0.00);
                userFeature.add(0.00);
                userFeature.add(2.00);
                userFeature.add(0.00);
                userFeature.add(0.00);
                userFeature.add(0.00);
                userFeature.add(0.00);
                userFeature.add(0.00);
                userFeature.add(0.00);
                userFeature.add(0.00);
                userFeature.add(0.00);
                userFeature.add(0.00);
            }
        } catch (Exception e) {
            String errorMsg = ("查询用户视频特征, 查询redis特征失败 userId:【%s】").formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }

        if (!CollectionUtils.isEmpty(videosIds)) {
            try {
                List<String> contentFeaturesObj = redisTemplate.opsForValue().multiGet(videosIds.stream().map(o -> contentFeatureKey + ":" + o).toList());
                if (!CollectionUtils.isEmpty(contentFeaturesObj)) {
                    for (int i = 0; i < videosIds.size(); i++) {
                        if (Objects.nonNull(contentFeaturesObj.get(i))) {
                            JSONObject jsonObject = JSON.parseObject(contentFeaturesObj.get(i));
                            ArrayList<Double> contentFeature = new ArrayList<>(userFeature);
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("content_recent_3d_behaviours_ctr", 0.00).toString()));
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("content_recent_3d_behaviours_cnt", 0.00).toString()));
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("content_recent_3d_click_cnt", 0.00).toString()));
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("content_recent_3d_like_cnt", 0.00).toString()));
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("content_recent_7d_behaviours_ctr", 0.00).toString()));
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("content_recent_7d_behaviours_cnt", 0.00).toString()));
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("content_recent_7d_click_cnt", 0.00).toString()));
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("content_recent_7d_like_cnt", 0.00).toString()));
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("topic_kmeans_label", 0.00).toString()));
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("publisher", 0.00).toString()));
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("publish_day", 0.00).toString()));
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("car_model_name_new", 0.00).toString()));
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("topic_cnt", 0.00).toString()));
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("post_app_tag", 0.00).toString()));
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("content_type", 0.00).toString()));
                            contentFeature.add(Double.valueOf(jsonObject.getOrDefault("car_brand_name_new", 0.00).toString()));
                            videoFeatures.add(contentFeature);
                        } else {
                            ArrayList<Double> contentFeature = new ArrayList<>(userFeature);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            contentFeature.add(0.00);
                            videoFeatures.add(contentFeature);
                        }
                    }
                } else {
                    for (int i = 0; i < videosIds.size(); i++) {
                        ArrayList<Double> contentFeature = new ArrayList<>(userFeature);
                        contentFeature.add(0.00);
                        contentFeature.add(0.00);
                        contentFeature.add(0.00);
                        contentFeature.add(0.00);
                        contentFeature.add(0.00);
                        contentFeature.add(0.00);
                        contentFeature.add(0.00);
                        contentFeature.add(0.00);
                        contentFeature.add(0.00);
                        contentFeature.add(0.00);
                        contentFeature.add(0.00);
                        contentFeature.add(0.00);
                        contentFeature.add(0.00);
                        contentFeature.add(0.00);
                        contentFeature.add(0.00);
                        contentFeature.add(0.00);
                        videoFeatures.add(contentFeature);
                    }
                }
            } catch (Exception e) {
                String errorMsg = ("查询内容视频特征, 查询redis特征失败 userId:【%s】").formatted(userId);
                log.error(errorMsg, e);
                e.printStackTrace();
            }
        }
        // 组合特征
        return videoFeatures;
    }
}
