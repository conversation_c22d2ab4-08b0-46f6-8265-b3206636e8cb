package com.beantechs.recom.dataservice.service.handle;

public class ModuleHandleChainBuild {

    private ModuleHandle firstHandle;
    private ModuleHandle lastHandle;

    public ModuleHandleChainBuild addHandler(ModuleHandle handler) {
        if (firstHandle == null) {
            firstHandle = handler;
        } else {
            lastHandle.setNextHandler(handler);
        }
        lastHandle = handler;
        return this;
    }

    public ModuleHandle build() {
        return firstHandle;
    }

}
