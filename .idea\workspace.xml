<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="c1cff4ec-2fbd-4b3b-a4cd-fc14ce1b33a3" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="AnnotationType" />
        <option value="Interface" />
        <option value="Enum" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="develop" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="HARD" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/src/main/java/com/beantechs/recom/dataservice/service/handle/MultiRecallHandler.java" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="HttpClientSelectedEnvironments">
    <file url="jar://$APPLICATION_HOME_DIR$/plugins/restClient/lib/restClient.jar!/com/intellij/ws/rest/client/requests/collection/requests-with-loop.http" environment="test" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\apache-maven-3.9.9" />
        <option name="localRepository" value="D:\apache-maven-3.9.9\repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\apache-maven-3.9.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2qhOyHY74nBP6e9F1tcVlU0KyVF" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="1" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Application.OpenSearchTest.executor&quot;: &quot;Debug&quot;,
    &quot;Application.RecommendService.executor&quot;: &quot;Run&quot;,
    &quot;Application.RuleRankHandler.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #1.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #10.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #11.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #12.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #13.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #14.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #15.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #16.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #17.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #18.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #19.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #2.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #20.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #21.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #22.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #23.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #24.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #25.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #26.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #27.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #28.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #29.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #3.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #30.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #31.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #32.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #33.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #34.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #35.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #36.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #37.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #38.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #39.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #4.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #40.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #41.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #42.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #43.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #44.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #45.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #46.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #47.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #48.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #49.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #5.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #50.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #6.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #7.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #8.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | #9.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | 设置测试数据#2.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.http-requests-log | 设置测试数据.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.rest-api | #1.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.rest-api_5 | #1.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.rest-api_5 | #2.executor&quot;: &quot;Run&quot;,
    &quot;HTTP Request.rest-api_6 | #1.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.OpenSearchTest.test.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.OpenSearchTest.testModel.executor&quot;: &quot;Run&quot;,
    &quot;JUnit.com.beantechs.recom.dataservice.entity in app-merge-recommend-service.executor&quot;: &quot;Profiler#1&quot;,
    &quot;Maven.app-merge-recommend-service [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.app-merge-recommend-service [compile].executor&quot;: &quot;Run&quot;,
    &quot;Maven.app-merge-recommend-service [package].executor&quot;: &quot;Run&quot;,
    &quot;Notification.DisplayName-DoNotAsk-Database detector&quot;: &quot;Database detector&quot;,
    &quot;Notification.DoNotAsk-Database detector&quot;: &quot;true&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;Spring Boot.AppMergeRecommendApplication.executor&quot;: &quot;Debug&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/dev-center-dataservice/app-merge-recommend-service&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Project&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\dev-center-dataservice\app-merge-recommend-service" />
      <recent name="D:\dev-center-dataservice\app-merge-recommend-service\src\main\resources" />
      <recent name="D:\dev-center-dataservice\app-merge-recommend-service\src\main\resources\META-INF" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\dev-center-dataservice\app-merge-recommend-service\src\main\resources\META-INF" />
      <recent name="D:\dev-center-dataservice\app-merge-recommend-service" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.beantechs.recom.dataservice.entity.bo" />
      <recent name="com.beantechs.recom.dataservice.service.handle.recall" />
      <recent name="com.beantechs.recom.dataservice.service.handle" />
      <recent name="com.beantechs.recom.dataservice.service.strategy" />
      <recent name="com.beantechs.recom.dataservice.controller" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.AppMergeRecommendApplication">
    <configuration name="http-requests-log | #1" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$PROJECT_DIR$/.idea/httpRequests/http-requests-log.http" executionIdentifier="#1" runType="Run single request">
      <method v="2" />
    </configuration>
    <configuration name="http-requests-log | #2" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$PROJECT_DIR$/.idea/httpRequests/http-requests-log.http" executionIdentifier="#2" index="2" runType="Run single request">
      <method v="2" />
    </configuration>
    <configuration name="http-requests-log | #3" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$PROJECT_DIR$/.idea/httpRequests/http-requests-log.http" executionIdentifier="#3" index="3" runType="Run single request">
      <method v="2" />
    </configuration>
    <configuration name="http-requests-log | #6" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$PROJECT_DIR$/.idea/httpRequests/http-requests-log.http" executionIdentifier="#6" index="6" runType="Run single request">
      <method v="2" />
    </configuration>
    <configuration name="http-requests-log | #8" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$PROJECT_DIR$/.idea/httpRequests/http-requests-log.http" executionIdentifier="#8" index="8" runType="Run single request">
      <method v="2" />
    </configuration>
    <configuration name="AppMergeRecommendApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="app-merge-recommend-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.beantechs.recom.dataservice.AppMergeRecommendApplication" />
      <option name="VM_PARAMETERS" value="-javaagent:D:\dev-center-dataservice\apache-skywalking-java-agent-9.4.0\skywalking-agent\skywalking-agent.jar -Dskywalking.agent.service_name=app-merge-recommend-service -Dskywalking.collector.backend_service=127.0.0.1:11800" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="HTTP Request.http-requests-log | #3" />
      <item itemvalue="HTTP Request.http-requests-log | #6" />
      <item itemvalue="HTTP Request.http-requests-log | #8" />
      <item itemvalue="HTTP Request.http-requests-log | #1" />
      <item itemvalue="HTTP Request.http-requests-log | #2" />
      <item itemvalue="Spring Boot.AppMergeRecommendApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="HTTP Request.http-requests-log | #1" />
        <item itemvalue="HTTP Request.http-requests-log | #6" />
        <item itemvalue="HTTP Request.http-requests-log | #2" />
        <item itemvalue="HTTP Request.http-requests-log | #3" />
        <item itemvalue="HTTP Request.http-requests-log | #8" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="StructureViewState">
    <option name="selectedTab" value="Logical" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="c1cff4ec-2fbd-4b3b-a4cd-fc14ce1b33a3" name="Changes" comment="" />
      <created>1735111594020</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1735111594020</updated>
      <workItem from="1735111596447" duration="2039000" />
      <workItem from="1735114130435" duration="5523000" />
      <workItem from="1735123962496" duration="8143000" />
      <workItem from="1735137337391" duration="3002000" />
      <workItem from="1735178850641" duration="32276000" />
      <workItem from="1735263825074" duration="1372000" />
      <workItem from="1735265878968" duration="1338000" />
      <workItem from="1735270081852" duration="2661000" />
      <workItem from="1735277329915" duration="22513000" />
      <workItem from="1735364230649" duration="22606000" />
      <workItem from="1735522588822" duration="15532000" />
      <workItem from="1735548623575" duration="634000" />
      <workItem from="1735609307249" duration="6507000" />
      <workItem from="1735782007140" duration="16822000" />
      <workItem from="1735809394857" duration="9633000" />
      <workItem from="1735868098087" duration="23459000" />
      <workItem from="1735954575671" duration="11691000" />
      <workItem from="1735973319385" duration="21885000" />
      <workItem from="1736127552539" duration="27854000" />
      <workItem from="1736211218019" duration="20520000" />
      <workItem from="1736243125510" duration="817000" />
      <workItem from="1736246953283" duration="1294000" />
      <workItem from="1736303889174" duration="15882000" />
      <workItem from="1736386739496" duration="14512000" />
      <workItem from="1736475250271" duration="12850000" />
      <workItem from="1736728850884" duration="5977000" />
      <workItem from="1736737625483" duration="13940000" />
      <workItem from="1736816171736" duration="22362000" />
      <workItem from="1736903793816" duration="25651000" />
      <workItem from="1736991208402" duration="18070000" />
      <workItem from="1737016755920" duration="2505000" />
      <workItem from="1737022737340" duration="134000" />
      <workItem from="1737023030614" duration="844000" />
      <workItem from="1737024294233" duration="35864000" />
      <workItem from="1737176868498" duration="14491000" />
      <workItem from="1737337253674" duration="39515000" />
      <workItem from="1737511465973" duration="19672000" />
      <workItem from="1738721817597" duration="1466000" />
      <workItem from="1738723628632" duration="3193000" />
      <workItem from="1738805700513" duration="18428000" />
      <workItem from="1738894775443" duration="1097000" />
      <workItem from="1738896691176" duration="8217000" />
      <workItem from="1739004977671" duration="905000" />
      <workItem from="1739168373347" duration="8454000" />
      <workItem from="1739238602357" duration="3763000" />
      <workItem from="1739243475258" duration="51310000" />
      <workItem from="1739411086633" duration="66563000" />
      <workItem from="1739928985246" duration="101738000" />
      <workItem from="1740466523986" duration="42350000" />
      <workItem from="1740712070359" duration="2125000" />
      <workItem from="1740724466601" duration="10476000" />
      <workItem from="1740900636185" duration="2792000" />
      <workItem from="1740911762265" duration="21733000" />
      <workItem from="1741067727408" duration="21987000" />
      <workItem from="1741225269489" duration="24471000" />
      <workItem from="1741570449694" duration="21174000" />
      <workItem from="1741599844039" duration="3553000" />
      <workItem from="1741657069943" duration="18650000" />
      <workItem from="1741764853780" duration="4849000" />
      <workItem from="1741774098296" duration="30011000" />
      <workItem from="1742171694564" duration="84299000" />
      <workItem from="1742782124343" duration="8811000" />
      <workItem from="1742809975452" duration="12845000" />
      <workItem from="1742970682782" duration="18032000" />
      <workItem from="1743474481361" duration="34850000" />
      <workItem from="1744338436622" duration="50123000" />
      <workItem from="1744795038286" duration="34000" />
      <workItem from="1744795090982" duration="45000" />
      <workItem from="1744795153552" duration="110870000" />
      <workItem from="1745399740766" duration="1398000" />
      <workItem from="1745401258128" duration="54033000" />
      <workItem from="1745804249951" duration="12660000" />
      <workItem from="1745991747350" duration="2666000" />
      <workItem from="1747015420928" duration="63107000" />
      <workItem from="1747382935553" duration="69928000" />
      <workItem from="1748314748590" duration="598000" />
      <workItem from="1748937138758" duration="32979000" />
      <workItem from="1749195578661" duration="24923000" />
      <workItem from="1749619868632" duration="4280000" />
      <workItem from="1749715412428" duration="1950000" />
      <workItem from="1749782643908" duration="3143000" />
      <workItem from="1750059913186" duration="5409000" />
      <workItem from="1750330636919" duration="2152000" />
      <workItem from="1751002413732" duration="3621000" />
      <workItem from="1751951181862" duration="760000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="com.intellij.fileTypeFactory" implementationName=".gitlab-ci.yml" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/beantechs/recom/dataservice/service/handle/AlgModelRankHandler.java</url>
          <line>84</line>
          <option name="timeStamp" value="3" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <watches-manager>
      <configuration name="SpringBootApplicationConfigurationType">
        <watch expression="recallResult" />
        <watch expression="json" />
        <watch expression="graphicFuture.result" language="JAVA" />
      </configuration>
    </watches-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>