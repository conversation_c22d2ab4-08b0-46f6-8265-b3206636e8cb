package com.beantechs.recom.dataservice.entity.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 推荐内容信息
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "推荐内容信息")
@AllArgsConstructor
@NoArgsConstructor
public class RecommendRespContentInfo {

    /**
     * 内容ID
     */
    @Schema(description = "内容ID", example = "content_123")
    private String contentId;

    /**
     * 内容类型:graphics-图文, videos-视频
     */
    @Schema(description = "内容类型:graphics-图文, videos-视频", example = "graphics", allowableValues = {"graphics", "videos"})
    private String type;


    public static RecommendRespContentInfo build(String contentId, String type) {
        RecommendRespContentInfo recommendRespContentInfo = new RecommendRespContentInfo();
        recommendRespContentInfo.setContentId(contentId);
        recommendRespContentInfo.setType(type);
        return recommendRespContentInfo;
    }
}
