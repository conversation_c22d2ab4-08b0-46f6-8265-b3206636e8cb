package com.beantechs.recom.dataservice.controller;

import com.beantechs.recom.dataservice.entity.req.RecommendReq;
import com.beantechs.recom.dataservice.entity.resp.RecommendResp;
import com.beantechs.recom.dataservice.entity.resp.RecommendRespContentInfo;
import com.beantechs.recom.dataservice.entity.resp.ResponseEntity;
import com.beantechs.recom.dataservice.service.RecommendNewService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Slf4j
@Tag(name = "app-recommend-controller", description = "推荐服务接口")
public class AppRecommendController {

    private final RecommendNewService recommendNewService;

    public AppRecommendController(RecommendNewService recommendNewService) {
        this.recommendNewService = recommendNewService;
    }


    @Operation(summary = "获取推荐内容", description = "获取推荐内容")
    @PostMapping("/new")
    public ResponseEntity<RecommendResp> recom(
            @RequestBody @Valid @Parameter(description = "推荐请求参数", required = true) RecommendReq requestBody
    ) {
        RecommendResp handle = new RecommendResp();
        try {
            handle = recommendNewService.handle(requestBody);
        } catch (Exception e) {
            try {
                List<RecommendRespContentInfo> recommendRespContentInfos;
                if (requestBody.getMainReqParams().getChannelId().equals("-1")) {
                    recommendRespContentInfos = recommendNewService.recommendHomePageContentFinal(requestBody.getMainReqParams().getUserId(), requestBody.getMainReqParams().getPageSize());
                } else {
                    recommendRespContentInfos = recommendNewService.recommendOtherPageContentFinal(requestBody.getMainReqParams().getUserId(), requestBody.getMainReqParams().getPageSize(), requestBody.getMainReqParams().getContentTagList());
                }
                if (null == recommendRespContentInfos) {
                    String errorMsg = "es最终兜底 => userId:[%s], channelId:[%s], 查询无数据!".formatted(requestBody.getMainReqParams().getUserId(), requestBody.getMainReqParams().getChannelId());
                    log.warn(errorMsg);
                    return ResponseEntity.responseBySucceedData("success", RecommendResp.ofDefault(requestBody.getMainReqParams().getUserId()));
                } else {
                    handle.setUserId(requestBody.getMainReqParams().getUserId());
                    handle.setContents(recommendRespContentInfos);
                    log.warn("最终最终兜底 => 兜底成功, 查询es数据返回, userId:【{}】, 数据返回:【{}】", handle.getUserId(), handle.getContents());
                    return ResponseEntity.responseBySucceedData("success", handle);
                }
            } catch (Exception ex) {
                String errorMsg = "最终最终兜底 => userId:【%s】, 兜底失败, 查询es失败".formatted(requestBody.getMainReqParams().getUserId());
                log.error(errorMsg, ex);
            }
            return ResponseEntity.responseBySucceedData("success", RecommendResp.ofDefault(requestBody.getMainReqParams().getUserId()));
        }
        return ResponseEntity.responseBySucceedData("success", handle);
    }
}
