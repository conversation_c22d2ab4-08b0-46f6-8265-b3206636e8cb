package com.beantechs.recom.dataservice.service.recall;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.beantechs.recom.dataservice.config.Constants;
import com.beantechs.recom.dataservice.entity.bo.ContentScoreVo;
import com.beantechs.recom.dataservice.entity.bo.MultiRecallResultBo;
import com.beantechs.recom.dataservice.service.CommonHandel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 基于物品协同过滤的召回处理器
 * 通过用户的历史行为数据，找到相似内容进行召回
 * 支持T0/T1时间窗口的行为数据，并对不同时间窗口的数据进行加权处理
 *
 * <AUTHOR>
 * @since 2024-03-21
 */
@Slf4j
@RecallType(type = Constants.RECALL_WAY_ITEM_CF)
@Service
public class RecallItemCFHandle extends CommonHandel implements RecallHandle {

    private final RedisTemplate<String, String> redisTemplate;

    public RecallItemCFHandle(RedisTemplate<String, String> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public MultiRecallResultBo handle(String userId, String channelId) {
        long l = System.currentTimeMillis();
        // 获取用户行为数据
        List<ContentScoreVo> t0Scores = getT0BehaviourScores(userId, channelId);

        // 获取实时行为内容ID集合
        Set<String> realTimeContents = t0Scores.stream()
                .map(ContentScoreVo::getContentId)
                .collect(Collectors.toSet());

        // 获取离线计算的召回结果
        List<ContentScoreVo> offlineGraphics = getOfflineGraphics(userId, channelId);

        List<ContentScoreVo> offlineVideos = getOfflineVideos(userId, channelId);

        HashMap<String, List<ContentScoreVo>> similarGraphicsContents = getSimilarGraphicsContents(realTimeContents, userId, channelId);

        HashMap<String, List<ContentScoreVo>> similarVideoContents = getSimilarVideoContents(realTimeContents, userId, channelId);

        List<ContentScoreVo> weightedGraphics;
        List<ContentScoreVo> weightedVideos;

        // 获取相似内容并计算加权分数

        weightedGraphics = calculateWeightedScores(
                t0Scores,
                similarGraphicsContents,
                offlineGraphics
        );

        weightedVideos = calculateWeightedScores(
                t0Scores,
                similarVideoContents,
                offlineVideos
        );

        // 封装结果
        MultiRecallResultBo result = new MultiRecallResultBo();
        result.setItemCfGraphics(weightedGraphics);
        result.setItemCfVideos(weightedVideos);

        log.warn("ItemCF召回 => userId:【{}】, 图文:【{}】条, 视频:【{}】条, 总耗时:【{}】ms", userId, weightedGraphics.size(), weightedVideos.size(), System.currentTimeMillis() - l);
        return result;
    }

    private List<ContentScoreVo> getOfflineGraphics(String userId, String channelId) {
        String t1ItemKey = Constants.REDIS_KEY_RECOM_T1_GRAPHIC_ITEMCF_PREFIX + channelId + ":" + userId;

        String t1Item;
        try {
            t1Item = (String) redisTemplate.opsForValue().get(t1ItemKey);
        } catch (Exception e) {
            String errorMsg = "ItemCF召回 => Redis查询离线图文异常 === 用户ID:%s".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
            return Collections.emptyList();
        }

        return parseContentScoreVoRecallResult(t1Item);
    }

    private List<ContentScoreVo> getOfflineVideos(String userId, String channelId) {
        String t1ItemKey = Constants.REDIS_KEY_RECOM_T1_VIDEO_ITEMCF_PREFIX + channelId + ":" + userId;

        String t1Item;
        try {
            t1Item = (String) redisTemplate.opsForValue().get(t1ItemKey);
        } catch (Exception e) {
            String errorMsg = "ItemCF召回 => Redis查询离线视频异常 === 用户ID:%s".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
            return Collections.emptyList();
        }
        return parseContentScoreVoRecallResult(t1Item);
    }


    /**
     * 计算加权分数
     * 考虑用户行为分数、内容相似度和时间窗口权重
     */
    private List<ContentScoreVo> calculateWeightedScores(
            List<ContentScoreVo> t0Scores,
            HashMap<String, List<ContentScoreVo>> similarContents,
            List<ContentScoreVo> offlineContents
    ) {
        Map<String, Double> contentScores = new HashMap<>();

        if (!CollectionUtils.isEmpty(similarContents)) {
            similarContents.forEach((id, contentList) -> {
                for (ContentScoreVo contentScoreVo : contentList) {
                    String contentId = contentScoreVo.getContentId();
                    double score = Double.parseDouble(contentScoreVo.getScore());
                    Optional<ContentScoreVo> first = t0Scores.stream().filter(t -> contentId.equals(t.getContentId())).findFirst();
                    if (first.isPresent()) {
                        score = score * Double.parseDouble(first.get().getScore()) * 1.1;
                    }
                    contentScores.merge(contentId, score, Double::sum);
                }
            });
        }

        // 合并离线召回结果
        for (ContentScoreVo offlineContent : offlineContents) {
            String contentId = offlineContent.getContentId();
            double offlineScore = Double.parseDouble(offlineContent.getScore());
            contentScores.merge(contentId, offlineScore, Double::sum);
        }

        return contentScores.entrySet().stream()
                .map(entry -> {
                    ContentScoreVo vo = new ContentScoreVo();
                    vo.setContentId(entry.getKey());
                    vo.setScore(String.valueOf(entry.getValue()));
                    return vo;
                })
                .sorted((a, b) -> Double.compare(
                        Double.parseDouble(b.getScore()),
                        Double.parseDouble(a.getScore())))
                .collect(Collectors.toList());
    }

    /**
     * 获取相似内容列表
     */
    private HashMap<String, List<ContentScoreVo>> getSimilarGraphicsContents(Set<String> contentIds, String userId, String channelId) {
        if (contentIds.isEmpty()) {
            return new HashMap<>();
        }
        String graphicKey = Constants.REDIS_KEY_RECOM_SIMILARIT_GRAPHIC_ITEMCF_PREFIX + channelId;

        List<Object> idList = contentIds.stream().map(o -> (Object) o).toList();
        List<Object> graphicList;
        HashMap<String, List<ContentScoreVo>> map = new HashMap<>();
        try {
            graphicList = redisTemplate.opsForHash().multiGet(graphicKey, idList);
            if (!CollectionUtils.isEmpty(graphicList)) {
                for (int i = 0; i < idList.size(); i++) {
                    if (!Objects.isNull(graphicList.get(i))) {
                        map.put(String.valueOf(idList.get(i)), parseContentScoreVoRecallResult(String.valueOf(graphicList.get(i))));
                    }
                }
            }
        } catch (Exception e) {
            String errorMsg = "ItemCF召回 => userId:%s, 从Redis查询相似图文内容失败".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }
        return map;
    }


    /**
     * 获取相似内容列表
     */
    private HashMap<String, List<ContentScoreVo>> getSimilarVideoContents(Set<String> contentIds, String userId, String channelId) {
        if (contentIds.isEmpty()) {
            return new HashMap<>();
        }

        String videoKey = Constants.REDIS_KEY_RECOM_SIMILARIT_VIDEO_ITEMCF_PREFIX + channelId;

        List<Object> idList = contentIds.stream().map(o -> (Object) o).toList();
        List<Object> videoList;
        HashMap<String, List<ContentScoreVo>> map = new HashMap<>();
        try {
            videoList = redisTemplate.opsForHash().multiGet(videoKey, idList);
            if (!CollectionUtils.isEmpty(videoList)) {
                for (int i = 0; i < idList.size(); i++) {
                    if (!Objects.isNull(videoList.get(i))) {
                        map.put(String.valueOf(idList.get(i)), parseContentScoreVoRecallResult(String.valueOf(videoList.get(i))));
                    }
                }
            }
        } catch (Exception e) {
            String errorMsg = "ItemCF召回 => userId:%s, 从Redis查询相似视频内容失败".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }
        return map;
    }


    private List<ContentScoreVo> getT0BehaviourScores(String userId, String channelId) {
        String dateString = LocalDateTime.now().minusHours(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH"));
        String t0Key = Constants.REDIS_KEY_RECOM_T0_BEHAVIOUR_PREFIX + dateString + ":" + channelId;
        String toBehaviour;
        List<ContentScoreVo> collect = List.of();
        try {
            toBehaviour = (String) redisTemplate.opsForHash().get(t0Key, userId);
            if (StringUtils.hasText(toBehaviour)) {
                collect = JSONArray.parseArray(toBehaviour)
                        .stream()
                        .map(com.alibaba.fastjson2.JSON::toJSONString)
                        .map(JSONObject::parseObject)
                        .filter(o -> o.getString("post_type").equals("-1"))
                        .map(o -> o.getString("content_score"))
                        .map(CommonHandel::parseContentScoreVoRecallResult)
                        .flatMap(List::stream).toList();
            }
        } catch (Exception e) {
            String errorMsg = "ItemCF召回 => Redis查询T0行为异常 === 用户ID:%s".formatted(userId);
            log.error(errorMsg, e);
            e.printStackTrace();
        }
        return collect;
    }


}
