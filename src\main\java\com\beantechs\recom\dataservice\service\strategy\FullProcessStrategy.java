package com.beantechs.recom.dataservice.service.strategy;

import com.beantechs.recom.dataservice.service.handle.*;

/**
 * 热启动推荐策略
 */
@RecommendStrategyAnnotation(way = "full")
public class FullProcessStrategy implements RecommendStrategy {

    private final ModuleHandle handlerChain;

    public FullProcessStrategy() {
        this.handlerChain = new ModuleHandleChainBuild()
                .addHandler(new MultiRecallHandler())
                .addHandler(new ResultFusionHandler())
                .addHandler(new AlgModelRankHandler())
                .addHandler(new RuleRankHandler())
                .addHandler(new ContentFillHandler())
                .addHandler(new ResponseHandler())
                .build();
    }


    @Override
    public void execute() {
        handlerChain.handle();
    }
}
