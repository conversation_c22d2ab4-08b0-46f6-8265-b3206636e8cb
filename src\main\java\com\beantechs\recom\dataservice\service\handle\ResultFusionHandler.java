package com.beantechs.recom.dataservice.service.handle;

import com.beantechs.recom.dataservice.config.RecommendContextHolder;
import com.beantechs.recom.dataservice.entity.bo.ContentScoreVo;
import com.beantechs.recom.dataservice.entity.bo.RecommendContext;
import com.beantechs.recom.dataservice.service.handle.recall.RecallHandleResult;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 结果融合处理器
 */
@Slf4j
public class ResultFusionHandler extends ModuleHandle {

    @Override
    public void handle() {
        RecommendContext context = RecommendContextHolder.getContext();
        RecallHandleResult resultResult = context.getResultResult();

        // usercf
        List<ContentScoreVo> userGraphic = resultResult.getUserCfGraphicResult();
        List<ContentScoreVo> userVideo = resultResult.getUserCfVideoResult();

        // itemcf
        List<ContentScoreVo> itemGraphic = resultResult.getItemCfGraphicResult();
        List<ContentScoreVo> itemVideo = resultResult.getItemCfVideoResult();

        // content
        List<ContentScoreVo> contentGraphic = resultResult.getContentGraphicResult();
        List<ContentScoreVo> contentVideo = resultResult.getContentVideoResult();

        // interest
        List<ContentScoreVo> interestTagGraphic = resultResult.getInterestTagGraphicResult();
        List<ContentScoreVo> interestTagVideo = resultResult.getInterestTagVideoResult();

        // follow
        List<ContentScoreVo> userFollowGraphic = resultResult.getUserFollowGraphicResult();
        List<ContentScoreVo> userFollowVideo = resultResult.getUserFollowVideoResult();

        // popular
        List<ContentScoreVo> hotGraphic = resultResult.getHotGraphicResult();
        List<ContentScoreVo> hotVideo = resultResult.getHotVideoResult();

        // user understands
        List<ContentScoreVo> userUnderstandGraphic = resultResult.getUserUnderstandGraphicResult();
        List<ContentScoreVo> userUnderstandVideo = resultResult.getUserUnderstandVideoResult();

        List<String> graphicIds = new ArrayList<>();
        graphicIds.addAll(userGraphic.stream().map(ContentScoreVo::getContentId).toList());
        graphicIds.addAll(itemGraphic.stream().map(ContentScoreVo::getContentId).toList());
        graphicIds.addAll(contentGraphic.stream().map(ContentScoreVo::getContentId).toList());
        graphicIds.addAll(interestTagGraphic.stream().map(ContentScoreVo::getContentId).toList());
        graphicIds.addAll(userFollowGraphic.stream().map(ContentScoreVo::getContentId).toList());
        graphicIds.addAll(hotGraphic.stream().map(ContentScoreVo::getContentId).toList());
        graphicIds.addAll(userUnderstandGraphic.stream().map(ContentScoreVo::getContentId).toList());

        List<String> videoIds = new ArrayList<>();
        videoIds.addAll(userVideo.stream().map(ContentScoreVo::getContentId).toList());
        videoIds.addAll(itemVideo.stream().map(ContentScoreVo::getContentId).toList());
        videoIds.addAll(contentVideo.stream().map(ContentScoreVo::getContentId).toList());
        videoIds.addAll(interestTagVideo.stream().map(ContentScoreVo::getContentId).toList());
        videoIds.addAll(userFollowVideo.stream().map(ContentScoreVo::getContentId).toList());
        videoIds.addAll(hotVideo.stream().map(ContentScoreVo::getContentId).toList());
        videoIds.addAll(userUnderstandVideo.stream().map(ContentScoreVo::getContentId).toList());

        context.setFusionGraphicIds(graphicIds.stream().distinct().toList());
        context.setFusionVideoIds(videoIds.stream().distinct().toList());
        context.setResultResult(RecallHandleResult.clear(resultResult));

        RecommendContextHolder.setContext(context);
        if (nextHandler != null) {
            nextHandler.handle();
        }
    }
}
