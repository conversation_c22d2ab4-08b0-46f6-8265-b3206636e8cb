package com.beantechs.recom.dataservice.service.recall;

import java.lang.annotation.Annotation;

public record RecallTypeHandleImpl(String type) implements RecallType {


    @Override
    public Class<? extends Annotation> annotationType() {
        return RecallType.class;
    }

    /**
     * 动态代理下确保实现类一致
     *
     * @return hashCode
     */
    @Override
    public int hashCode() {
        int hashCode = 0;
        hashCode += (127 * "type".hashCode()) ^ type.hashCode();
        return hashCode;
    }

    @Override
    public boolean equals(Object object) {
        if (!(object instanceof RecallType searchHandleType)) {
            return false;
        }
        return type.equals(searchHandleType.type());
    }
}
