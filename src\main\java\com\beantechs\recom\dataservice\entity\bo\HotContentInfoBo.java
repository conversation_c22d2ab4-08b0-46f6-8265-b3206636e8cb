package com.beantechs.recom.dataservice.entity.bo;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

@Data
public class HotContentInfoBo {
    private String carBrandNameNew;
    private String contentId;
    private Double hotScore;
    private String publishTime;
    private String topicKmeansLabel;


    public static HotContentInfoBo of(ResultSet rs) throws SQLException {
        HotContentInfoBo info = new HotContentInfoBo();
        info.setContentId(rs.getString("content_id"));
        info.setCarBrandNameNew(rs.getString("car_brand_name_new"));
        info.setTopicKmeansLabel(rs.getString("topic_kmeans_label"));
        info.setPublishTime(rs.getString("publish_time"));
        info.setHotScore(rs.getDouble("hot_score"));
        return info;
    }

    public static HotContentInfoBo ofJSON(JSONObject rs) {
        HotContentInfoBo info = new HotContentInfoBo();
        info.setContentId(rs.getString("content_id"));
        info.setCarBrandNameNew(rs.getString("car_brand_name_new"));
        info.setTopicKmeansLabel(rs.getString("topic_kmeans_label"));
        info.setPublishTime(rs.getString("publish_time"));
        info.setHotScore(rs.getDouble("hot_score"));
        return info;
    }


    public static boolean isNewest(HotContentInfoBo obj) {
        // 解析发布时间
        LocalDate publishDate = LocalDate.parse(obj.getPublishTime().substring(0, 10), DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        // 计算发布日期与当前日期的天数差
        long daysBetween = ChronoUnit.DAYS.between(publishDate, LocalDate.now());

        // 判断是否在3天内
        return daysBetween <= 3;
    }

}
