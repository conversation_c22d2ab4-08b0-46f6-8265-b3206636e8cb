package com.beantechs.recom.dataservice.entity.bo;

import lombok.Data;

/**
 * 内容特征BO类，包含用户ID和内容ID
 */
@Data
public class ContentFeaturesBo {
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 内容ID
     */
    private String contentId;

    // 用户特征
    /**
     * 已注册时长
     */
    private Double registerDay;

    /**
     * 用户年龄
     */
    private Double age;

    /**
     * 粉丝数量
     */
    private Double fansCnt;

    /**
     * 用户选择的app标签
     */
    private Double userSelectAppTag;

    /**
     * 用户性别
     */
    private Double sex;

    /**
     * 用户选择的兴趣标签
     */
    private Double userSelectCarScene;

    /**
     * 用户选择的价格区间
     */
    private Double userSelectPriceRange;

    /**
     * 用户选择的驱动模式
     */
    private Double userSelectDriveMode;

    /**
     * 用户最近3天点击率
     */
    private Double userRecent3dBehavioursCtr;

    /**
     * 用户最近七天点击率
     */
    private Double userRecent7dBehavioursCtr;

    /**
     * 用户最近七天行为brand_name集合
     */
    private Double userRecent7dBehavioursBrandNameSet;

    /**
     * 用户最近七天行为点击率最高的brand_name
     */
    private Double userRecent7dMostCtrBrandName;

    /**
     * 用户最近七天行为量
     */
    private Double userRecent7dBehavioursCnt;

    /**
     * 用户最近七天行为topic_id集合
     */
    private Double userRecent7dBehavioursTopicIdSet;

    // 内容特征
    /**
     * 内容最近三天点击率
     */
    private Double contentRecent3dBehavioursCtr;

    /**
     * 内容最近三天行为量
     */
    private Double contentRecent3dBehavioursCnt;

    /**
     * 内容最近三天点击量
     */
    private Double contentRecent3dClickCnt;

    /**
     * 内容最近三天点赞量
     */
    private Double contentRecent3dLikeCnt;

    /**
     * 内容最近七天点击率
     */
    private Double contentRecent7dBehavioursCtr;

    /**
     * 内容最近七天行为量
     */
    private Double contentRecent7dBehavioursCnt;

    /**
     * 内容最近七天点击量
     */
    private Double contentRecent7dClickCnt;

    /**
     * 内容最近七天点赞量
     */
    private Double contentRecent7dLikeCnt;

    /**
     * 内容聚类标签列表
     */
    private Double topicKmeansLabel;

    /**
     * 内容发布者
     */
    private Double publisher;

    /**
     * 内容已发布天数
     */
    private Double publishDay;

    /**
     * 内容所属车系
     */
    private Double carModelNameNew;

    /**
     * 内容话题数量
     */
    private Double topicCnt;

    /**
     * 内容所属apptag标签
     */
    private Double postAppTag;

    /**
     * 内容类型
     */
    private Double contentType;

    /**
     * 内容品牌标签
     */
    private Double carBrandNameNew;

    public String getContentId() {
        return contentId;
    }

    public void setContentId(String contentId) {
        this.contentId = contentId;
    }
} 