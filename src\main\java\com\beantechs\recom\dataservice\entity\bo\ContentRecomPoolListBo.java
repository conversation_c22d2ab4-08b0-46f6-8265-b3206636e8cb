package com.beantechs.recom.dataservice.entity.bo;

import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;

@Data
public class ContentRecomPoolListBo implements Serializable {

    /**
     * CTR图文排序池
     */
    private List<HotContentInfoBo> ctrSortGraphicDetails;

    /**
     * CTR视频排序池
     */
    private List<HotContentInfoBo> ctrSortVideoDetails;

    /**
     * 新内容图文详情列表
     */
    private List<HotContentInfoBo> newestGraphicDetails;

    /**
     * 新内容视频详情列表
     */
    private List<HotContentInfoBo> newestVideoDetails;

    public static boolean balanceIsNotEnough(ContentRecomPoolListBo contentRecomPoolListBo) {
        return CollectionUtils.isEmpty(contentRecomPoolListBo.ctrSortGraphicDetails) || CollectionUtils.isEmpty(contentRecomPoolListBo.ctrSortVideoDetails) ||
               (contentRecomPoolListBo.ctrSortGraphicDetails.size() + contentRecomPoolListBo.ctrSortVideoDetails.size()) < 10;
    }


}
