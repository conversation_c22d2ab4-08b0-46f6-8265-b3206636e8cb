package com.beantechs.recom.dataservice.entity.resp;

import com.beantechs.recom.dataservice.entity.bo.RecommendContext;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 推荐响应结果
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "推荐响应结果")
public class RecommendResp {

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", example = "12345")
    private String userId;

    /**
     * 推荐内容列表
     */
    @Schema(description = "推荐的内容列表")
    private List<RecommendRespContentInfo> contents;

    /**
     * 推荐明细
     */
    @Schema(description = "推荐明细")
    private String recomDetail;


    public static RecommendResp of(String userId, List<RecommendRespContentInfo> contents) {
        RecommendResp recommendResp = new RecommendResp();
        recommendResp.setUserId(userId);
        recommendResp.setContents(contents);
        return recommendResp;
    }


    public static RecommendResp ofDefault(String userId) {
        RecommendResp recommendResp = new RecommendResp();
        recommendResp.setUserId(userId);
        recommendResp.setContents(List.of());
        return recommendResp;
    }




}
