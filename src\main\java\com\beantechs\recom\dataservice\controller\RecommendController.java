package com.beantechs.recom.dataservice.controller;

import com.alibaba.fastjson.JSON;
import com.beantechs.recom.dataservice.config.DIYException;
import com.beantechs.recom.dataservice.entity.req.MainReqParams;
import com.beantechs.recom.dataservice.entity.req.RecommendReq;
import com.beantechs.recom.dataservice.entity.resp.RecommendResp;
import com.beantechs.recom.dataservice.entity.resp.RecommendRespContentInfo;
import com.beantechs.recom.dataservice.entity.resp.ResponseEntity;
import com.beantechs.recom.dataservice.service.RecommendOldService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 推荐服务API
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Slf4j
@Tag(name = "recommend-controller", description = "推荐服务接口")
public class RecommendController {

    private final RecommendOldService recommendOldService;

    public RecommendController(RecommendOldService recommendOldService) {
        this.recommendOldService = recommendOldService;
    }

    /**
     * 获取推荐内容
     *
     * @param mainReqParams 推荐请求参数
     * @return 推荐响应结果
     * <p>
     * 成功返回示例:
     * {
     * "code": 200,
     * "message": "success",
     * "data": {
     * "beanId": "12345",
     * "contents": [
     * {
     * "contentId": "contentId_123",
     * "type": "graphics"
     * }
     * ]
     * }
     * }
     * </p>
     * @throws DIYException 业务异常
     */
    @Operation(summary = "获取推荐内容", description = "根据用户ID和内容类型获取个性化推荐内容")
    @PostMapping("/old")
    public ResponseEntity<RecommendResp> recom(
            @RequestBody @Valid @Parameter(description = "推荐请求参数", required = true) RecommendReq recommendReq
    ) {
        MainReqParams mainReqParams = recommendReq.getMainReqParams();
        if (!StringUtils.hasText(mainReqParams.getChannelId())) {
            mainReqParams.setChannelId("-1");
        }

        if (Objects.isNull(mainReqParams.getPageSize()) || mainReqParams.getPageSize() < 0) {
            mainReqParams.setPageSize(10);
        }

        if (mainReqParams.getPageSize() > 100) {
            mainReqParams.setPageSize(100);
        }
        RecommendResp handle = new RecommendResp();
        try {
            handle = recommendOldService.handle(mainReqParams);
        } catch (Exception e) {
            try {
                List<RecommendRespContentInfo> recommendRespContentInfos;
                if (mainReqParams.getChannelId().equals("-1")) {
                    recommendRespContentInfos = recommendOldService.recommendHomePageContentFinal(mainReqParams.getUserId(), mainReqParams.getPageSize());
                } else {
                    recommendRespContentInfos = recommendOldService.recommendOtherPageContentFinal(mainReqParams.getUserId(), mainReqParams.getPageSize(), mainReqParams.getContentTagList());
                }
                if (null == recommendRespContentInfos) {
                    String errorMsg = "es最终兜底 => userId:[%s], channelId:[%s], 查询无数据!".formatted(mainReqParams.getUserId(), mainReqParams.getChannelId());
                    log.warn(errorMsg);
                    return ResponseEntity.responseBySucceedData("success", RecommendResp.ofDefault(mainReqParams.getUserId()));
                } else {
                    handle.setUserId(mainReqParams.getUserId());
                    handle.setContents(recommendRespContentInfos);
                    log.warn("最终最终兜底 => 兜底成功, 查询es数据返回, userId:【{}】, 数据返回:【{}】", handle.getUserId(), handle.getContents());
                    return ResponseEntity.responseBySucceedData("success", handle);
                }
            } catch (Exception ex) {
                String errorMsg = "最终最终兜底 => userId:【%s】, 兜底失败, 查询es失败".formatted(mainReqParams.getUserId());
                log.error(errorMsg, ex);
            }
            return ResponseEntity.responseBySucceedData("success", RecommendResp.ofDefault(mainReqParams.getUserId()));
        }
        return ResponseEntity.responseBySucceedData("success", handle);
    }


    /**
     * 获取推荐内容——另一个接口的兜底
     *
     * @param mainReqParams 推荐请求参数
     * @return 推荐响应结果
     * <p>
     * 成功返回示例:
     * {
     * "code": 200,
     * "message": "success",
     * "data": {
     * "beanId": "12345",
     * "contents": [
     * {
     * "contentId": "contentId_123",
     * "type": "graphics"
     * }
     * ]
     * }
     * }
     * </p>
     * @throws DIYException 业务异常
     */
    @Operation(summary = "获取推荐内容——另一个接口的兜底", description = "获取推荐内容——另一个接口的兜底")
    @PostMapping("/real/recom/final")
    public ResponseEntity<RecommendResp> recomFinal(
            @RequestBody @Valid @Parameter(description = "推荐请求参数", required = true) MainReqParams mainReqParams
    ) {
        long startTime = System.currentTimeMillis();

        // 参数校验和默认值设置，与recom方法保持一致
        if (!StringUtils.hasText(mainReqParams.getChannelId())) {
            mainReqParams.setChannelId("-1");
        }

        if (Objects.isNull(mainReqParams.getPageSize()) || mainReqParams.getPageSize() < 0) {
            mainReqParams.setPageSize(10);
        }

        if (mainReqParams.getPageSize() > 100) {
            mainReqParams.setPageSize(100);
        }

        String userId = mainReqParams.getUserId();
        String channelId = mainReqParams.getChannelId();
        Integer pageSize = mainReqParams.getPageSize();

        RecommendResp response = new RecommendResp();
        response.setUserId(userId);

        if (channelId.equals("-1")) {
            // 第一级：尝试从Redis获取数据
            List<RecommendRespContentInfo> redisResult;
            try {
                redisResult = recommendOldService.queryFromRedis(userId, channelId, pageSize);
                if (!CollectionUtils.isEmpty(redisResult)) {
                    response.setContents(redisResult);
                    log.warn("兜底接口 => 查询Redis成功, userId:【{}】, 数据返回:【{}】条, 耗时:【{}】ms",
                            userId, redisResult.size(), System.currentTimeMillis() - startTime);
                    return ResponseEntity.responseBySucceedData("success", response);
                }
            } catch (Exception e) {
                log.warn("兜底接口 => Redis查询失败, userId:【{}】", userId);
            }

            // 第二级：尝试从ES获取数据
            List<RecommendRespContentInfo> esResult;
            try {
                esResult = recommendOldService.recommendHomePageContentFinal(userId, pageSize);
                if (!CollectionUtils.isEmpty(esResult)) {
                    response.setContents(esResult);
                    log.warn("兜底接口 => 查询ES成功, userId:【{}】, 数据返回:【{}】条, 耗时:【{}】ms",
                            userId, esResult.size(), System.currentTimeMillis() - startTime);
                    return ResponseEntity.responseBySucceedData("success", response);
                }
            } catch (Exception e) {
                log.warn("兜底接口 => ES查询失败, userId:【{}】", userId);
            }

            // 第三级：尝试从MySQL获取数据
            List<RecommendRespContentInfo> mysqlResult;
            try {
                mysqlResult = recommendOldService.queryFromMysql(channelId, pageSize);
                if (!CollectionUtils.isEmpty(mysqlResult)) {
                    response.setContents(mysqlResult);
                    log.warn("兜底接口 => 查询MySQL成功, userId:【{}】, 数据返回:【{}】条, 耗时:【{}】ms",
                            userId, mysqlResult.size(), System.currentTimeMillis() - startTime);
                    return ResponseEntity.responseBySucceedData("success", response);
                }
            } catch (Exception e) {
                String errorMsg = "兜底接口 => MySQL查询失败, userId:【%s】".formatted(userId);
                log.error(errorMsg, e);
                e.printStackTrace();
            }
        } else {
            // 第二级：尝试从ES获取数据
            List<RecommendRespContentInfo> esResult;
            try {
                esResult = recommendOldService.recommendOtherPageContentFinal(userId, pageSize, mainReqParams.getContentTagList());
                if (!CollectionUtils.isEmpty(esResult)) {
                    response.setContents(esResult);
                    log.warn("兜底接口 => 查询ES成功, userId:【{}】, 数据返回:【{}】条, 耗时:【{}】ms",
                            userId, esResult.size(), System.currentTimeMillis() - startTime);
                    return ResponseEntity.responseBySucceedData("success", response);
                }
            } catch (Exception e) {
                log.warn("兜底接口 => ES查询失败, userId:【{}】", userId);
            }
        }

        log.warn("兜底接口 => 查询无数据, 接口入参:【{}】, userId:【{}】", JSON.toJSONString(mainReqParams), userId);
        response.setContents(new ArrayList<>());
        return ResponseEntity.responseBySucceedData(response);
    }


}
