package com.beantechs.recom.dataservice.entity.bo;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 内容评分VO
 * 用于表示内容的评分信息
 */
@Data
@AllArgsConstructor
public class ContentTypeScoreVo {
    /**
     * 内容ID
     */
    private String contentId;

    /**
     * 类别
     */
    private String contentType;

    /**
     * 内容评分
     */
    private String score;


    public static ContentTypeScoreVo typeOf(ContentScoreVo contentScoreVo, String contentType) {
        return new ContentTypeScoreVo(contentScoreVo.getContentId(), contentType, contentScoreVo.getScore());
    }


    public static ContentTypeScoreVo typeScoreVoOf(String contentId, String contentType) {
        return new ContentTypeScoreVo(contentId, contentType, "0.00");
    }

}
