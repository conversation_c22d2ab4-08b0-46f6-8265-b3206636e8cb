package com.beantechs.recom.dataservice.entity.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.relational.core.mapping.Column;

import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class EsContentBo {


    @Column("car_brand_name")
    private List<String> carBrandName;

    @Column("car_brand_name_new")
    private List<String> carBrandNameNew;

    @Column("car_model_name")
    private List<String> carModelName;

    @Column("car_model_name_new")
    private List<String> carModelNameNew;

    @Column("channel_ids")
    private List<String> channelIds;

    @Column("content_id")
    private String contentId;

    @Column("content_tag_id")
    private List<String> contentTagId;

    @Column("content_tag_name")
    private String contentTagName;

    @Column("create_by")
    private String createBy;

    @Column("hot_score")
    private float hotScore;

    @Column("image_count")
    private int imageCount;

    @Column("post_app_tag")
    private String postAppTag;

    @Column("post_car_level")
    private String postCarLevel;

    @Column("post_car_scene")
    private String postCarScene;

    @Column("post_drive_mode")
    private String postDriveMode;

    @Column("post_price_range")
    private String postPriceRange;

    @Column("post_type")
    private String postType;

    @Column("publish_time")
    private String publishTime;

    @Column("publisher")
    private String publisher;

    @Column("rank")
    private int rank;

    @Column("score_rank")
    private long scoreRank;

    @Column("topic_id")
    private List<String> topicId;

    @Column("topic_tag")
    private List<String> topicTag;

    @Column("type")
    private String type;

    @Column("video_id")
    private String videoId;
}
